#pragma once
namespace cpx {
	class cpx_entity;

	template <typename T, std::enable_if_t<std::is_base_of_v<cpx_entity, T>>* = nullptr>
	T* assert_entity(cpx_entity* v) {
#if DEBUG
		T* e = dynamic_cast<T*>(v);
		if (!e) {
			throw_e("entity is not imodel_entity");
		}
#else
		T* e = static_cast <T*>(v);
#endif
		return e;
	}

	template <typename T, typename Base, std::enable_if_t<std::is_base_of_v<Base, T>>* = nullptr>
	T* assert_cast(Base* v) {
#if DEBUG
		T* e = dynamic_cast<T*>(v);
		if (!e) {
			throw_e("pointer is not %s", typeid(Base).name());
		}
#else
		T* e = static_cast <T*>(v);
#endif
		return e;
	}
}
