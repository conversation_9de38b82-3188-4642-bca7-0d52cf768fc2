#pragma once
#include <unordered_set>
#include "net_server.h"
#include "libcute.h"
#include "delegate.h"
#include <tcp_session.h>
#include "tcp_factory.h"

namespace net {
	class tcp_server final : public net_server {
	public:
		enum message_type_t {
			ON_CONN_IN,
			ON_CONN_LOST,
			ON_CONN_READ
		};

		struct message_t {
			message_t(message_type_t, tcp_server*, tcp_session*);
			message_type_t type{};
			tcp_server* server{};
			tcp_session* conn{};
			service_form_t form{};
			socket_form_t sock{};
			pb_slice slice{};
		};

		friend class tcp_session;
		typedef delegate<void (const message_t*)> server_invoker_t;
		CUTE_DELEGATE_FUNC_DECL(server_invoker_t, _sinvoker, add_watcher, remove_watcher)
	public:
		DELETE_MOVE_COPY(tcp_server)
		explicit tcp_server(uv_loop_t* l);
		size_t cnt_session() const;

		enum decode_t {
			kService<PERSON><PERSON>,
			k<PERSON>ock<PERSON><PERSON><PERSON>,
			k<PERSON><PERSON><PERSON><PERSON><PERSON>
		};

		decode_t decode_type() const;
		void decode_type(decode_t);
		int broadcast(const pb_base*) const;
		using session_collection = std::unordered_set<ref_ptr<tcp_session>>;
		const session_collection& sessions() const;
		writer_factory f_writer{};
		reader_factory f_reader{};
		sys::net_zip zip{};

	protected:
		int try_accept(uv_stream_t*, net_peer_id) override;

	private:
		void receive_message(tcp_session*, pb_slice);
		void on_session_change(const event_t*);
		~tcp_server() override;
		using session_t = ref_ptr<tcp_session>;
		session_collection _sessions{};
		server_invoker_t _sinvoker;
		decode_t _mtype{};
	};
}
