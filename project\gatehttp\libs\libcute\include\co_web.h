#pragma once
#include "libcute.h"
#include "web_message.h"

namespace net {
	class co_web final {
	public:
		DELETE_MOVE_COPY(co_web)
		explicit co_web(std::string url, int retry = 3);
		~co_web();
		void load(const dynamic_vo&, pb_buffer*);
		void load(const char*, size_t, pb_buffer*);
		void visit(pb_buffer*);
		dynamic_vo load(const dynamic_vo&);
		int send(const dynamic_vo&);
		web_message head{};

	private:
		void call(const char*, size_t, pb_buffer*);
		std::string _url;
		int _error;
		int _retry;
	};
}
