#pragma once
#include "libcutex.h"
#include "logger.h"
#include "ref_ptr.h"

namespace sys {
	template <typename ...T>
	class tick_queue {
	};

	template <typename T, typename ...Args>
	class tick_queue<void (T::*)(Args ...)> {
	public:
		DEFAULT_MOVE_COPY(tick_queue)
		using func_ptr = void (T::*)(Args ...);
		using iterator = typename std::vector<T*>::iterator;
		using const_iterator = typename std::vector<T*>::const_iterator;

		explicit tick_queue(const func_ptr c) : _call(c),
		                                        _updating(false),
		                                        _clearing(false),
		                                        _purge(false) {
		}

		void add(T* obj) {
			for (auto* a : _list) {
				if (a == obj) {
					return;
				}
			}
			_list.emplace_back(obj);
		}

		bool remove(T* obj) {
			auto it = std::find(_list.begin(), _list.end(), obj);
			if (it == _list.end())return false;
			return remove(it - _list.begin());
		}

		T* remove(size_t idx) {
			if (idx >= _list.size()) {
				return nullptr;
			}
			T* item = _list[idx];
			_list[idx] = nullptr;
			_purge = true;
			purge();
			return item;
		}

		template <typename Pred, typename ...PArgs,
		          std::enable_if_t<std::is_invocable_r_v<bool, Pred, T*, PArgs ...>> * = nullptr>
		int remove_if(Pred predicator, PArgs ... args) {
			int q = 0;
			for (size_t i = 0; i < _list.size(); ++i) {
				T* v = _list[i];
				if (predicator(v, (args) ...)) {
					_list[i] = nullptr;
					_purge = true;
					++q;
				}
			}
			purge();
			return q;
		}

		void operator ()(Args ... args) {
			invoke(std::forward<Args>(args)...);
		}

		void operator ()(Args&& ... args) {
			invoke(std::forward<Args>(args)...);
		}

		T* operator[](size_t at) {
			return _list[at];
		}

		size_t size() const {
			return _list.size();
		}

		void invoke(Args&& ... args) {
			_updating = true;
			on_execute(std::forward<Args>(args)...);
			_updating = false;
			purge();
		}

		void on_execute(Args&& ... args) {
			for (T* p : _list) {
				if (_clearing)return;
				if (!p)continue;
				try_call(
					(p->*_call)(args...);
				);
			}
		}

		iterator begin() {
			return _list.begin();
		}

		iterator end() {
			return _list.end();
		}

		iterator begin() const {
			return _list.begin();
		}

		iterator end() const {
			return _list.end();
		}

		const_iterator cbegin() const {
			return _list.cbegin();
		}

		const_iterator cend() const {
			return _list.cend();
		}

		void clear() {
			_clearing = true;
			_list.clear();
			_clearing = false;
		}

		~tick_queue() {
			_call = nullptr;
			_list.clear();
		}

	private:
		func_ptr _call;
		std::vector<T*> _list;
		bool _updating;
		bool _clearing;
		bool _purge;

		void purge() {
			if (_updating)return;
			if (!_purge)return;
			_purge = false;
			auto it = std::remove(_list.begin(), _list.end(), nullptr);
			_list.erase(it, _list.end());
		}
	};
}
