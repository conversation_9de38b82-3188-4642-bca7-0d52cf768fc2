#pragma once

#ifndef LIB_CUTE_X_H
#define LIB_CUTE_X_H
#pragma once
#include "json.hpp"
typedef nlohmann::json dynamic_vo;


#include "sys_exception.h"

#define try_call(sentence)\
do{try {sentence;}												\
catch(sys::sys_exception& e){log_error("error:%s", e.what());}	\
catch(std::exception& e){log_error_stack("error:%s", e.what());}\
catch(...){log_error("<unknown error>");}						\
}while(0)

#undef 	interface

#define abstract 	class
#define interface 	class
#define implements 	public
#define extends 	virtual public


#define INTERFACE_GETTER_SETTER(field, type)\
virtual type field()const = 0 ;	\
virtual void field(type)  = 0 ;


#define IMPLEMENT_GETTER_SETTER(field, type) 		\
public: 											\
	type field() const override {return _##field;} 	\
	void field(type v) override {_##field = v;}  	\
private : 											\
	type _##field {};


#define DEFINE_GETTER_SETTER(field , type ) 	\
public: 										\
	type field() const   {return _##field;} 	\
	void field(type v)   {_##field = v;}  		\
private : 										\
	type _##field {};


#endif
