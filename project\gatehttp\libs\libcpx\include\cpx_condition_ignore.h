#pragma once
#include <cpx_action.h>

#include "cpx_condition.h"

namespace cpx {
	class cpx_condition_ignore final : public cpx_condition {
	public:
		DELETE_MOVE_COPY(cpx_condition_ignore)
		void parse(const dynamic_vo& jo) override;
		int class_id() const noexcept override;
		const char* class_n() const noexcept override;
		cpx_condition_ignore();
		~cpx_condition_ignore() override; 

	protected:
		bool check_validate(const cpx_transition_context* ctx) const override;
	};
};
