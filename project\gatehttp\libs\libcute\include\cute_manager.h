#ifndef UV_MANAGER_H
#define UV_MANAGER_H  
#include "uv.h"
#include "libcute.h"
LP_NS_BEGIN
typedef void (*cute_signal_cb)(int);

typedef struct machine_info {
	char host[256];
	char my_id[256];
	char ip[80];
	int cpu_count;
	int route_type;
	const char* logcat;
	int64_t route_id;
	uv_cpu_info_t* cpus;
	cute_signal_cb sig_cb;
} machine_info_t;

typedef struct cute_service_info {
	int id;
	int type;
} cute_service_info;


LP_API int cute_set_log_category(cute_service_info);
LP_API int cute_init(const char* logcat);
LP_API int cute_start(void);
LP_API uv_loop_t* cute_mainloop(void);
LP_API uv_mutex_t* cute_mainmutex(void);
LP_API int cute_stop(void);
LP_API machine_info_t* cute_machine_info(void);
LP_API const char* cute_cwd(void);
LP_API const char* cute_exename(void);
LP_NS_END
#endif
