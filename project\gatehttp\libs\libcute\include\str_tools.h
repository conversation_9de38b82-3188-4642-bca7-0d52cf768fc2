#pragma once
#include <string>
#include "libcute.h"
#include <vector>
#include <hashmapx.h>
namespace sys {
	int gen_hash32(const char*, size_t);
	int64_t gen_hash64(const char*, size_t);
	int gen_hash32(const std::string&);
	void str_to_lwr(std::string&);
	void str_to_lwr(const char*, std::string& to);
	std::string str_format(const char* fmt, ...) CHECK_PRINTF(1, 2);
	size_t count_utf8(const char*, size_t);
	size_t cut_utf8(const char*, size_t, size_t max);
	std::string file_name(const std::string& path);
	void split_str(const std::string&, std::vector<std::string>&, char);
	void split_str(const std::string&, std::vector<std::string>&, const std::string& split);
	bool start_with(const std::string&, const std::string& head);
	bool end_with(const std::string&, const std::string& tail);
	hashmapx<std::string, std::string> split_kv(const std::string&, char ks = '&', char vs = '=');
}

namespace sys {
#define SPACE_CHARS     " \t\r\n"
#define PAIR_CHARS      "{}[]()<>\"\"\'\'``"
	std::string trim(const std::string&, const char* chars = SPACE_CHARS);
	std::string ltrim(const std::string&, const char* chars = SPACE_CHARS);
	std::string rtrim(const std::string&, const char* chars = SPACE_CHARS);
	std::string trim_pairs(const std::string&, const char* pairs = PAIR_CHARS);
}

namespace sys {
	void internal_replace(std::string& dst, int idx, const std::string& val);
	void internal_replace(std::string& dst, int idx, const char* val);

	template <typename T>
	void internal_replace(std::string& dst, const int idx, const T& arg) {
		internal_replace(dst, idx, std::to_string(arg));
	}

	template <typename T, typename... Args>
	void internal_replace(std::string& dst, const int idx,
	                      const T& arg,
	                      const Args&... args) {
		internal_replace(dst, idx, arg);
		internal_replace(dst, idx + 1, (args)...);
	}

	template <typename TSrc, typename T, typename... Args,
	          std::enable_if_t<std::is_convertible_v<TSrc, std::string>> * = nullptr>
	std::string str_replace(const TSrc& src, const T& arg, const Args&... args) {
		std::string dst = src;
		internal_replace(dst, 0, arg, (args)...);
		return dst;
	}
}

namespace sys {
	struct string_case_less : std::less<std::string> {
		bool operator()(const std::string&, const std::string&) const;
	};
}
