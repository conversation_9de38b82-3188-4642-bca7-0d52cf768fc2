#pragma once
#include "pb_base.h"
#include "route_t.h"
#include "service_common_code_wrapper.h"

namespace ecs {
	class object_t;
}

namespace sys {
	class room_id final : public pb_base {
	public:
		DEFAULT_MOVE_COPY(room_id)
		room_id();
		~room_id() override;
		room_id(const ecs::object_t&);
		operator ecs::object_t() const;
		bool operator ==(const room_id&) const;
		bool operator !=(const room_id&) const;
		bool empty() const;
		route_t game() const;
		std::string str() const;

		template <typename T>
		explicit room_id(const T& v) {
			zone = v.zone;
			place = v.place;
			machine = v.machine;
			room = v.room;
		}

		template <typename T>
		explicit operator T() const {
			T v{};
			v.zone = zone;
			v.place = place;
			v.room = room;
			v.machine = machine;
			return v;
		}

		PB_WRAPPER_room_id()
	};
}

#include <functional> // hash

namespace std {
	template <>
	struct hash<sys::room_id> {
		size_t operator()(const sys::room_id& v) const noexcept;
	};
}

using room_id = sys::room_id;
