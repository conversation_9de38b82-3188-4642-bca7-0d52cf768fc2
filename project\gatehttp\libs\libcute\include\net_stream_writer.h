#pragma once
#include "cute_peer.h"
#include "libcute.h"
#include "recycle_stream.h"

namespace net {
	class net_peer;
	struct stat_writer;

	struct net_stream_writer {
		DELETE_MOVE_COPY(net_stream_writer)
		virtual int write(const char*, size_t) =0;
		virtual int write(const recycle_stream&) = 0;
		virtual int flush() = 0;
		virtual void get_uvbuf(uv_buf_t*) = 0;
		virtual size_t size() = 0;
		virtual void on_write_done() = 0;
		virtual void stat(stat_writer&, time_t);
		virtual int ping(time_t) = 0;
		virtual int pong(time_t) = 0;
		void peer(net_peer*, cute_peer_t*);
		virtual ~net_stream_writer();

	protected:
		explicit net_stream_writer();
		cute_peer_t* _cp{};
		net_peer* _np{};
	};
};
