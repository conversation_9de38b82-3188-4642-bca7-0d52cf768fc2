#pragma once
#include <libcute.h>
#include <set>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"

namespace tvo {
	class type_props_group;

	class type_bag final : public type_data {
	public:
		DELETE_MOVE_COPY(type_bag)
		type_bag();
		~type_bag() override;
		void add_support(type_props_group*);
		bool support(int group_id) const;
		bool support(const std::string& group_key) const;
		void awake(const awake_context_t&) override;
		int posmask{};

	private:
		std::set<type_props_group*> _supports;

	public:
		static void awake_all(const awake_context_t&);
		static const std::vector<const type_bag*>& bodylist();

		enum capability_t {
			CapProperty = 1 << 0,
			CapRender = 1 << 1,
			CapBuff = 1 << 2
		};

		enum mode_t {
			ModeBag,
			ModeBody,
			ModeWarehouse,
			ModeTemp,
			ModeEmbed
		};

		enum owner_type_t {
			OwnerPlayer,
			OwnerEquip
		};

		TYPE_BAG_WRAPPER()
	};
}
