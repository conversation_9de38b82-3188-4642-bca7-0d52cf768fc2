#pragma once
#include <pb_scope.h>
#include <route_t.h>
#include <sys_exception.h>
#include "err_libgame.h"
#include "pb_libgame_initor.h"
#include "pipe_manager.h"
#include "ptask_scene_run.h"
#include "uni_task.h"
#include "usr_task_collection.h"

namespace dvo {
	template <typename TRequest, typename TResponse>
	class utask_request final : public uni_task {
	public:
		TYPE_CAST_IMPLEMENT(utask_request)
		DELETE_MOVE_COPY(utask_request)
		using request_type = TRequest;
		using response_type = TResponse;
		using self_type = utask_request;
		static constexpr int code_request = request_type::assign_type();
		static constexpr int code_response = response_type::assign_type();
		typedef void (*callback_t)(utask_request&);

		utask_request(usr_info* u, const callback_t cb): _usr(u), _cb(cb) {
		}

		~utask_request() override = default;

		void abort() override {
			_error = ERR_BAD_ARGS;
			_status = kAbort;
			_recv = true;
		}

		int id() const override {
			return _form_id;
		}

		int error() const override {
			return _error;
		}

		status_t update(const cpx::time_vo& t) override {
			if (_status == kRunning && _recv) {
				_status = kDone;
			}
			if (_status == kRunning && static_cast<time_t>(t.time) > _duetime) {
				_status = kDone;
				_error = ERR_PLAYER_PROC_TIMEOUT;
			}
			if (_status != kRunning && _cb) {
				_cb(*this);
				_cb = nullptr;
			}
			return _status;
		}

		int send(const request_type& req, const route_t& dst) {
			assert(s_reg && "please regist code at system init");
			assert(_usr && "usr is nullptr");
			const auto* p = _usr->player();
			if (!p) {
				throw_e("no player vo");
				_error = ERR_USE_NULLPTR;
				_status = kDone;
				return _error;
			}
			net::tcp_conn conn(dst);
			if ((_error = conn.send(&req, service_form::PB_FUNC_REQUEST))) {
				_status = kDone;
				return _error;
			}
			_form_id = conn.form_id();
			_duetime = static_cast<time_t>(_usr->gettime().time) + 30;
			_status = kRunning;
			p->utask().add(this);
			return 0;
		}

		const response_type& resposnse() const {
			return _response;
		}

		status_t status() const override {
			return _status;
		}

		usr_info* usr() const {
			return _usr;
		}

	private:
		int _form_id{};
		int _error{};
		usr_info* _usr{};
		callback_t _cb{};
		status_t _status{};
		time_t _duetime{};
		response_type _response{};
		bool _recv{};

		void recv_ok(response_type&& x) {
			_response = std::forward<response_type>(x);
			_recv = true;
		}

		void recv_fail() {
			_recv = true;
			_error = ERR_BAD_ARGS;
		}

		static void recv_service_fail(service_form& form) {
			auto& run = app::pipe_manager::instance().runner();
			int cid;
			if (0 != mem::pb_readint(form.data, service_fail::kClassId, cid)) {
				return;
			}
			if (cid != code_request) {
				return;
			}
			auto fail = form.obj<service_fail>();
			if (!fail) {
				return;
			}
			request_type req{};
			int err;
			if ((err = req.pb_base::decode(fail.data))) {
				log_error("parse service fail [%d] fail, errcode: %d", fail.class_id, err);
				return;
			}
			const id_64 usr_id = req.usr_id;
			const id_64 session = req.session;
			const auto* p = run.find(usr_id);
			if (!p || p->session != session) {
				return;
			}
			const auto t = p->utask().find(form.form_id);
			if (!t)return;
			auto* m = sys::type_cast<utask_request>(t.get());
			if (!m)return;
			m->recv_fail();
		}

		static void recv_response(service_form& form) {
			auto resp = form.obj<response_type>();
			if (!resp) {
				return;
			}
			const id_64 usr_id = resp.usr_id;
			const id_64 session = resp.session;
			auto& run = app::pipe_manager::instance().runner();
			const auto* p = run.find(usr_id);
			if (!p || p->session != session) {
				return;
			}
			const auto t = p->utask().find(form.form_id);
			if (!t)return;
			auto* m = sys::type_cast<utask_request>(t.get());
			if (!m)return;
			m->recv_ok(std::move(resp));
		}

		static void recv(app::pipe_thread*, const app::pipe_signal& sig) {
			const auto form = sig.form;
			if (!form) {
				return;
			}
			if (form->class_id == PBC_service_fail) {
				recv_service_fail(*form);
			} else {
				recv_response(*form);
			}
		}

	public:
		static inline bool s_reg{};

		static void reg() {
			if (s_reg) {
				return;
			}
			s_reg = true;
			app::pipe_manager::instance().game_thread().add_resp(code_request, code_response, recv);
		}
	};
}
