#pragma once
#include  "libcute.h"
#include "service_form.h"
#include "service_t.h"
#include "tcp_peer.h"
#include "consistent_key.h"

namespace net {
	class tcp_conn final {
	public:
		DELETE_MOVE_COPY(tcp_conn)
		friend class saux_route;
		explicit tcp_conn(const sys::service_t&);
		explicit tcp_conn(route_t);
		explicit tcp_conn(sys::consistent_key);
		explicit tcp_conn(ref_ptr<tcp_peer>, route_t src, route_t dst);
		explicit tcp_conn(ref_ptr<tcp_peer>, route_t dst);
		int send(const pb_base*);
		int send(const recycle_stream&) const;
		int send(const pb_base*, service_form::func_type);
		int response(const pb_base*, const ref_ptr<service_form>& to);
		int response(const pb_base*, const service_form&);
		void form_id(int32_t);
		int form_id() const;
		~tcp_conn();
		static void set_self(const route_t&);
		static route_t get_self();

	private:
		ref_ptr<tcp_peer> _peer;
		service_form _form;
		route_t _dst;
		route_t _src;
	};
}
