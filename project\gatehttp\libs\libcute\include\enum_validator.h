#pragma once
#include <atomic>
#include <libcute.h>

namespace sys {
	template <typename T, typename B = int32_t>
	struct enum_validator {
		static_assert(std::is_enum_v<T> || std::is_integral_v<T>);
		enum_validator() = default;
		~enum_validator() = default;
		DEFAULT_MOVE_COPY(enum_validator)
		using this_type = enum_validator;

		void add(const T val) {
			_mask.store(_mask.load() | static_cast<B>(val));
		}

		void remove(const T val) {
			_mask.store(_mask.load() & ~ static_cast<B>(val));
		}

		this_type& operator +=(const T val) {
			_mask.store(_mask.load() | static_cast<B>(val));
			return *this;
		}

		this_type& operator +=(const B val) {
			_mask.store(_mask.load() | val);
			return *this;
		}

		this_type& operator -=(const T val) {
			_mask.store(_mask.load() & ~ static_cast<B>(val));
			return *this;
		}

		this_type& operator -=(const B val) {
			_mask.store(_mask.load() & ~ val);
			return *this;
		}

		void all() {
			_mask.store(-1);
		}

		void clear() {
			_mask.store(0);
		}

		bool empty() const {
			return 0 == static_cast<B>(_mask.load());
		}

		T value() const {
			return static_cast<T>(_mask.load());
		}

		bool contains(const T val) const {
			return _mask.load() & static_cast<B>(val);
		}

		bool contains(const B val) const {
			return _mask.load() & val;
		}

	private:
		std::atomic<B> _mask{};
	};
}
