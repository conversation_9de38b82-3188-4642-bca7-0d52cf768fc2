#pragma once
#include <mutex>
#include "pb.h"

namespace ecs {
	struct fieldinfo_t;
	struct value_t;
	class object_t;
	class domain_t;
	struct block_t;

	class classinfo_t {
	public:
		friend class pool;
		friend class object_t;
		DELETE_MOVE_COPY(classinfo_t)
		classinfo_t(domain_t*, pb_Type*);
		~classinfo_t();
		pb_state* state() const;
		uint32_t version() const;
		pb_Type* pb() const;
		domain_t* domain() const;
		uint32_t num_fields() const;
		fieldinfo_t* field(uint32_t at) const;
		fieldinfo_t* find(uint32_t fnumber) const;
		fieldinfo_t* find(const char* fname) const;
		const char* name() const;
		int class_id() const;
		object_t new_instance();
		size_t obj_size() const;
		size_t used() const;
		size_t capacity() const;
		size_t cnt_free() const;
		size_t cnt_block() const;
		void free_mem();
		fieldinfo_t* field_dflag{}; /*delete_flag*/
		fieldinfo_t* field_id{};
	private:
		domain_t* _domain;
		pb_Type* _pb;
		fieldinfo_t* _fields{};
		uint32_t _cnt_fields{};
		uint32_t _max_fnumber{};
		int _class_id{};
		size_t* _voffset{};
		uint32_t* _findex{};
		/*flat mem pool*/
		void add_ref(char*);
		void reduce_ref(char*);
		char* allocate_line();
		void release_line(char*);
		void allocate_block();
		void internal_free();
		bool deallocate_block(char*);
		char* _current_block{};
		char* _free_slot{};
		size_t _capacity{};
		size_t _rowsize{};
		size_t _used{};
		std::mutex _mutex{};
	public:
		static void page_size(size_t v);
		static size_t page_size();
		static void print_usage();
	};
}
