#pragma once
#include <type_cast.h>

namespace dvo {
	struct worker_notification {
		DELETE_MOVE_COPY(worker_notification)
		int phase{};
		TYPE_CAST_BASE_DEFINE(worker_notification)
		worker_notification();

		template <typename T, std::enable_if_t<std::is_base_of_v<worker_notification, T>> * = nullptr>
		const T* cast() const {
			return sys::type_cast<const T>(this);
		}
	};

	template <typename T>
	struct worker_notification_impl : worker_notification {
		TYPE_CAST_IMPLEMENT(T)
	};

	struct worker_message final : worker_notification_impl<worker_message> {
	};
}
