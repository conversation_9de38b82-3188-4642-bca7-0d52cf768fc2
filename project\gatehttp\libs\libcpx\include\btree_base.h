#pragma once
#include "btree.h"

namespace bt {
	template <typename Tin, typename Tout>
	class btree_base : public btree {
	public:
		typedef Tin 	input_type;
		typedef Tout 	output_type;
		DELETE_MOVE_COPY(btree_base)
		~btree_base() override = default;
		virtual void awake(){}
	protected:
		btree_base() : btree() {
		}


		virtual void on_start(input_type*, output_type*) const = 0;

		void self_start(input_t* in, output_t* out) const final {
			on_start(static_cast<input_type*>(in),
			         static_cast<output_type*>(out));
		}

		virtual btree_status on_update(input_type*, output_type*) const =0;

		btree_status self_update(input_t* in, output_t* out) const final {
			return on_update(static_cast<input_type*>(in),
			                 static_cast<output_type*>(out));
		}

		virtual void on_stop(input_type* in, output_type* out) const =0;

		void self_stop(input_t* in, output_t* out) const final {
			on_stop(static_cast<input_type*>(in),
			        static_cast<output_type*>(out));
		}

		virtual void on_pause(input_type*, output_type*) const {
		}

		void self_pause(input_t* in, output_t* out) const final {
			on_pause(static_cast<input_type*>(in),
			         static_cast<output_type*>(out));
		}

		virtual void on_resume(input_type*, output_type*) const {
		}

		void self_resume(input_t* in, output_t* out) const final {
			on_resume(static_cast<input_type*>(in),
			          static_cast<output_type*>(out));
		}
	};
}
