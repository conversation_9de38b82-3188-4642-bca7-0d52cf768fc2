//gen by cpp_autogen automatically, don't modify it!
#pragma once
#ifndef CPX_CONDITION_BUILTIN_TYPES_H
#define CPX_CONDITION_BUILTIN_TYPES_H

#define CPX_CONDITION_BUILTIN_TYPES(XX)\
XX(-1202668847,cpx_condition_any,"conditionAny")\
XX(-108400545,cpx_condition_is_cpx_inited,"conditionCpxInited")\
XX(-1202675550,cpx_condition_or,"condition:or")\
XX(1371750136,cpx_condition_and,"condition:and")\
XX(1409768428,cpx_condition_parameter,"parameterchecker")

#include "cpx_condition_any.h"
#include "cpx_condition_is_cpx_inited.h"
#include "cpx_condition_or.h"
#include "cpx_condition_and.h"
#include "cpx_condition_parameter.h"


#endif //CPX_CONDITION_BUILTIN_TYPES_H
