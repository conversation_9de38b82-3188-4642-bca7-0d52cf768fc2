#pragma once
#include <map>

namespace net {
	struct http_cookie {
		void reset();
		bool parse(const std::string& str);
		std::string dump() const;
		std::string name;
		std::string value;
		std::string domain;
		std::string path;
		std::string expires;
		int max_age;
		bool secure;
		bool httponly;

		enum e_same_site {
			Default,
			Strict,
			Lax,
			None
		} samesite{};

		enum e_priority {
			NotSet,
			Low,
			Medium,
			High,
		} priority{};

		std::map<std::string, std::string> kv; // for multiple names
	};

	struct http_form_data {
		std::string file{};
		std::string content{};
	};
}
