#pragma once
#include "net_server.h"
#include "http_session.h"

namespace net {
	class http_server final : public net_server {
	public:
		DELETE_MOVE_COPY(http_server)

		struct message_t {
			message_t(http_session::ptr_t, http_phase, int);
			http_session::ptr_t session;
			http_phase phase;
			int version;
		};

		typedef int (*action_t)(message_t*, void*);

		struct action_cb {
			action_t action;
			void* callee;
			int operator ()(message_t*) const;
		};

		explicit http_server(uv_loop_t*);
		void set_action(action_t, void* = nullptr);

	protected:
		int try_accept(uv_stream_t*, net_peer_id) override;

	private:
		friend class http_session;
		action_cb _cb;
		~http_server() override;
	};
}
