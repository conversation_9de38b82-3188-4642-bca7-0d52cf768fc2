#ifndef DELEGATE_TREE_H
#define DELEGATE_TREE_H
#include "delegate.h"
#include "hashmapx.h"
#include "thread_locker.h" 
#include "libcute.h"
template <typename ...T>
class delegate_tree_vo {
};

template <typename TLocker, typename Ky, typename R, typename ...Args>
class delegate_tree_vo<TLocker, Ky, R(Args ...)> {
public:
	DELETE_MOVE_COPY(delegate_tree_vo)
	using key_type = Ky;
	using value_type = delegate_vo<sys::thead_no_lock, R(Args ...)>;
	typedef R (*delegate_call)(Args ...);
	template <typename C>
	using delegate_mcall = R(C::*)(Args ...);
	using this_type = delegate_tree_vo<TLocker, Ky, R(Args ...)>;
	using lock_type = TLocker;
	using thread_guard_t = sys::thread_guard<TLocker>;
	delegate_tree_vo() = default;

	this_type& add(const key_type& k, delegate_call fn) {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		value_type* v = nullptr;
		if (it == _maps.end()) {
			v = new value_type();
			_maps.emplace(k, v);
		} else {
			v = it->second;
		}
		v->add(fn);
		return *this;
	}

	this_type& remove(const key_type& k, delegate_call fn) {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		if (it != _maps.end()) {
			value_type* v = it->second;
			v->remove(fn);
		}
		return *this;
	}

	this_type& remove(const key_type& k) {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		if (it != _maps.end()) {
			const value_type* v = it->second;
			delete v;
			_maps.erase(it);
		}
		return *this;
	}

	bool contains(const key_type& k) {
		thread_guard_t g(_mutex);
		return _maps.find(k) != _maps.end();
	}

	value_type* find(const key_type& k) const {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		return it == _maps.end() ? nullptr : it->second;
	}

	template <class C>
	this_type& add(const key_type& k, C* obj, delegate_mcall<C> fn) {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		value_type* v = nullptr;
		if (it == _maps.end()) {
			v = new value_type();
			_maps.emplace(k, v);
		} else {
			v = it->second;
		}
		v->add(obj, fn);
		return *this;
	}

	template <class C>
	this_type& remove(const key_type& k, C* obj, delegate_mcall<C> fn) {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		if (it != _maps.end()) {
			auto* v = it->second;
			v->remove(obj, fn);
		}
		return *this;
	}

	template <class C>
	this_type& remove(const key_type& k, C* obj) {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		if (it != _maps.end()) {
			value_type* v = it->second;
			v->remove(obj);
		}
		return *this;
	}

	this_type& remove(void* body) {
		thread_guard_t g(_mutex);
		for (const auto& [_, d] : _maps) {
			d->remove(body);
		}
		return *this;
	}

	value_type* operator()(const key_type& k, Args&& ...args) {
		thread_guard_t g(_mutex);
		auto it = _maps.find(k);
		if (it != _maps.end()) {
			value_type* v = it->second;
			v->operator()(std::forward<Args>(args)...);
			return v;
		}
		return nullptr;
	}

	void clear() {
		thread_guard_t g(_mutex);
		private_clear();
	}

	~delegate_tree_vo() {
		thread_guard_t g(_mutex);
		private_clear();
	}

private:
	sys::hashmapx<key_type, value_type*> _maps;
	lock_type _mutex;

	void private_clear() {
		for (const auto& [_, v] : _maps) {
			delete v;
		}
		_maps.clear();
	}
};
#endif
