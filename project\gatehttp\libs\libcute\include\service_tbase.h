#pragma once
#include "libcute.h"
#include "service_t.h"
#include <hashmapx.h>
#include <mutex>
#include <shared_mutex>

namespace sys {
	class service_tbase {
	public :
		static service_tbase& instance();
		DELETE_MOVE_COPY(service_tbase)
		int read_file(const char*);
		int read_file();
		service_t select_one(int, id_64);
		using lock_read = std::shared_lock<std::shared_mutex>;
		using lock_write = std::lock_guard<std::shared_mutex>;

		template <typename Predicator, typename... Args,
		          std::enable_if_t<std::is_invocable_r_v<bool, Predicator, const service_t&, Args...>>* = nullptr>
		service_t select_one(const int type, Predicator&& fn, Args&&... args) {
			lock_read g(_mutex);
			const auto it = _sheets.find(type);
			if (it == _sheets.end()) {
				return nullptr;
			}
			const auto* sh = it->second;
			for (const auto& [_, v] : sh->datas) {
				if (fn(v, std::forward<Args>(args)...)) {
					return v;
				}
			}
			return nullptr;
		}

		template <typename Predicator, typename... Args,
		          std::enable_if_t<std::is_invocable_r_v<bool, Predicator, const service_t&, Args...>>* = nullptr>
		void select_list(const int type, std::vector<service_t>& to, Predicator&& fn, Args&&... args) {
			lock_read g(_mutex);
			const auto it = _sheets.find(type);
			if (it == _sheets.end()) {
				return;
			}
			const auto* sh = it->second;
			for (const auto& [_, v] : sh->datas) {
				if (fn(v, std::forward<Args>(args)...)) {
					to.emplace_back(v);
				}
			}
		}

		void to_array(int, std::vector<service_t>&);
		std::vector<service_t> to_array(int );
		/*return all service should be working*/
		std::vector<service_t> workings();
		std::vector<service_t> everything();
		service_t singleton(int);
		size_t size(int);
		size_t size();
		void clear();
		int version();

	private:
		service_tbase();
		~service_tbase();
		void clear_sheets();

		class sheet_t {
		public:
			DELETE_MOVE_COPY(sheet_t)
			sheet_t();
			~sheet_t();
			hashmapx<id_64, service_t> datas{};
		};

		std::shared_mutex _mutex;
		hashmapx<int, sheet_t*> _sheets{};
		int _version{};
		time_t _readtime{};
	};
}
