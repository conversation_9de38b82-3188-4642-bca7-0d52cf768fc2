#pragma once
#include "message_collection.h"
#include "pb_scope.h"
class pb_base;

struct message_data_appender {
	DEFAULT_MOVE_COPY(message_data_appender)
	explicit message_data_appender(pb_buffer*);
	~message_data_appender();
	int send(int c, pb_slice) const;
	int send(int c, int sh, pb_slice) const;
	int send(const pb_base&) const;

	template <typename T, typename ...Args>
	int send(T* d, const int c, const int sh,
	         int (T::* call)(pb_buffer*, Args ...),
	         Args&&... args) const {
		mem::pb_message_scope scope(_buf, message_collection::kDatas);
		message_data md{};
		md.code = c;
		md.sheet = sh;
		int err;
		if ((err = md.encode(_buf))) {
			scope.ignore();
			return err;
		}
		{
			mem::pb_bytes_scope sub_scope(_buf, message_data::kBytes);
			err = (d->*call)(_buf, std::forward<Args>(args)...);
			if (err) {
				scope.ignore();
				return err;
			}
		}
		return 0;
	}

	template <typename T, typename ...Args>
	int send(T* d, int (T::* call)(pb_buffer*, Args ...), Args&&... args) const {
		return send(d, d->class_id(), d->sheetcode(), call, std::forward<Args>(args)...);
	}

private:
	pb_buffer* _buf;
};
