#pragma once
#include "btree_status.h"
#include "cpx_core.h"
#include "cpx_input.h"
#include "libcutex.h"

namespace bt {
	class bt_context;
}

namespace cpx {
	class cpx_machine;
	struct tick_context;
	class cpx_input;
	class cpx_output;

	class cpx_action : cpx_core {
	public:
		DELETE_MOVE_COPY(cpx_action)

		struct read_context {
			std::string file{};
			size_t tree_size{};
			cpx_machine* machine{};
		};

		int priority{};
		int indent{};
		size_t hierachy{};
		bt::btree_status update(cpx_input*, cpx_output*) const;
		void read(const dynamic_vo&, read_context&, int);
		void stop(cpx_input*, cpx_output*) const;
		void reset(cpx_input*, cpx_output*) const;
		size_t size() const;
		const cpx_action* child_at(size_t at) const;
		bt::btree_status status(const cpx_input*) const;
		void status(const cpx_input*, bt::btree_status) const;

		template <typename T, std::enable_if_t<std::is_base_of_v<bt::bt_context, T>>* = nullptr>
		T* get_context(const cpx_input* in) const {
			return in->cluster.context<T>(hierachy);
		}

		virtual int class_id() const noexcept = 0;
		virtual const char* class_n() const noexcept = 0;
		~cpx_action() override;

	protected:
		virtual void on_start(cpx_input*, cpx_output*) const = 0;
		virtual bt::btree_status on_update(cpx_input*, cpx_output*) const =0;
		virtual void on_stop(cpx_input*, cpx_output*) const =0;
		virtual void parse(const dynamic_vo& vo) = 0;
		cpx_action();

	private:
		std::vector<const cpx_action*> _children;
	};
}
