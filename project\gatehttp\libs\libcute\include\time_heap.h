#pragma once
#include <libcute.h>
#include <binary_heap.h>
#include <delegate_slot.h>
#include "time_tools.h"

namespace sys {
	template <typename ...T>
	class time_heap {
	};

	template <typename Timer, typename R, typename ...Args>
	class time_heap<Timer, R(Args ...)> {
	public:
		DEFAULT_MOVE_COPY(time_heap)
		using callback_t = delegate_internal::callback_t<R(Args ...)>;

		struct setting_type {
			Timer time{};
			Timer interval{};
			int priority{};
			size_t max{};
			size_t repeat_{};
			callback_t call_{};

			bool operator ==(const setting_type& rhs) const {
				return call_ == rhs.call_;
			}

			bool operator <(const setting_type& rhs) const {
				return time < rhs.time && priority < rhs.priority;
			}
		};

		time_heap() = default;

		~time_heap() {
			_heap.clear();
		}

		void add(R (*fn)(Args ...), Timer time, Timer interval, size_t repeat = 1, int priority = 0) {
			setting_type set{};
			set.call_ = callback_t(fn);
			set.time = time;
			set.interval = interval;
			set.max = repeat;
			set.priority = priority;
			_heap.add(set);
		}

		void add(R (*fn)(Args ...), const dtime_t& time, Timer interval, size_t repeat = 1, int priority = 0) {
			setting_type set{};
			set.call_ = callback_t(fn);
			set.time = static_cast<Timer>(cute_precise_maketime(&time));
			set.interval = interval;
			set.max = repeat;
			set.priority = priority;
			_heap.add(set);
		}

		void remove(R (* fn)(Args ...)) {
			for (auto& v : _heap) {
				auto* h = v.call_.holder;
				if (h && h->func == fn) {
					v.call_ = callback_t{};
				}
			}
		}

		template <typename C>
		void add(C* inst, void (C::*fn)(Args ...), Timer time, Timer interval, size_t repeat = 1, int priority = 0) {
			setting_type set{};
			set.call_ = callback_t(inst, fn);
			set.time = time;
			set.interval = interval;
			set.max = repeat;
			set.priority = priority;
			_heap.add(set);
		}

		template <typename C>
		void add(C* inst, void (C::*fn)(Args ...),
		         const dtime_t& time,
		         Timer interval, size_t repeat = 1,
		         int priority = 0) {
			setting_type set{};
			set.call_ = callback_t(inst, fn);
			set.time = static_cast<Timer>(cute_precise_maketime(&time));
			set.interval = interval;
			set.max = repeat;
			set.priority = priority;
			_heap.add(set);
		}

		template <typename C>
		void remove(C* inst, void (C::*fn)(Args ...)) {
			for (auto& v : _heap) {
				auto* h = v.call_.holder;
				if (h && h->func == fn && h->inst == inst) {
					v.call_ = callback_t{};
				}
			}
		}

		template <typename C>
		void remove(C* inst) {
			for (auto& v : _heap) {
				auto* h = v.call_.holder;
				if (h && h->inst == inst) {
					v.call_ = callback_t{};
				}
			}
		}

		void operator()(Timer now, Args&& ... args) {
			while (!_heap.empty()) {
				auto s = _heap.shift();
				if (s.time > now) {
					_heap.add(s);
					break;
				}
				auto* h = s.call_.holder;
				if (!h) {
					continue;
				}
				h->operator()(std::forward<Args>(args)...);
				++ s.repeat_;
				if (s.repeat_ < s.max) {
					s.time = now + s.interval;
					_heap.add(s);
				}
			}
		}

		void dcall(Args&& ... args) {
			for (auto& s : _heap) {
				auto* h = s.call_.holder;
				if (!h) {
					continue;
				}
				h->operator()(std::forward<Args>(args)...);
			}
		}

		void clear() {
			_heap.clear();
		}

	private:
		binary_heap<setting_type> _heap{};
	};
}
