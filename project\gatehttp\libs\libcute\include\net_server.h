#pragma once
#include "libcute.h"
#include "cute_server.h"
#include "service_slave.h"
#include "net_base.h"
#include "co_job.h"
#include "service_proc_list.h"
#include "service_struct.h"

namespace net {
	class net_server : public net_base,
	                   public proc::service_proc_list {
	public:
		int start(const std::string&, const sys::service_conf_net&);
		int start(proc::service_slave*);
		void stop(stop_cb = nullptr) override;
		service_role role() const; 

	protected:
		explicit net_server(uv_loop_t*, size_t);
		DELETE_MOVE_COPY(net_server)
		virtual int try_accept(uv_stream_t*, net_peer_id) = 0;
		~net_server() override;
		cute_host_t _host;

	private:
		void accept_master();
		void accept_slave();
		void accept_self();
		void co_stop_process();
		void shutdown();
		void on_slave_event(const proc::signal_t*);
		static void on_conn_in(cute_host_t*);
		static void on_shutdown(cute_host_t*);

	/*
		---------------------fields---------------------
	*/
		size_t _pindex{};
		net_peer_id _child_ids{};
		service_role _role;
		proc::service_slave* _slave{};

	public:
		class co_stopper final : public co_job {
		public:
			DELETE_MOVE_COPY(co_stopper)
			explicit co_stopper(net_server*);
			~co_stopper() override;

		private:
			void exe();
			net_server* _tcp;
		};

		friend co_stopper;
	};
}
