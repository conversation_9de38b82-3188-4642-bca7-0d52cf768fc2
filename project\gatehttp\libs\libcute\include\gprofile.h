#pragma once
#include "libcute.h"
#include <string>
#include <route_t.h>
#if defined(__GNUC__)
#define PROFILE_ENABLE (1)
#else
#define PROFILE_ENABLE (0)
#endif
namespace profile {
	void profile_cpu_start(const route_t&);
	void profile_cpu_start(const std::string&);
	void profile_cpu_stop();
	void profile_heap_start(const route_t&);
	void profile_heap_dump(const route_t&);
	void profile_heap_stop();
}

namespace profile {
#if DEBUG && PROFILE_ENABLE
	struct cpu_profile_guard {
		DELETE_MOVE_COPY(cpu_profile_guard)
		explicit cpu_profile_guard(const std::string&);
		~cpu_profile_guard() noexcept;

	private:
		bool _sample{};
	};

#define cpu_profile_debug(x)profile::cpu_profile_guard g(x)
#else
	#define cpu_profile_debug(x)
#endif
}
