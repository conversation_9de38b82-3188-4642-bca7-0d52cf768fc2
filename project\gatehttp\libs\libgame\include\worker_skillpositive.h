#pragma once
#include "usr_worker.h"

namespace dvo {
	struct sheet_event;
	struct property_event;

	class worker_skillpositive final : public usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_skillpositive)
		void update(const update_context*) override;
		void start() override;
		void stop() override;
	private:
		void on_char_update(const property_event*);
		void on_sheet_change(const sheet_event*);
		void fetch_property();
		bool _dirty{};
	};
}
