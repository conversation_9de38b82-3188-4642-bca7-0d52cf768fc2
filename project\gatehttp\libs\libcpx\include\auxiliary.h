#pragma once
#include <libcute.h>
#include <libcutex.h>
#include <ref_ptr.h>
#include "icpx_ticker.h"
#include <type_cast.h>

namespace cpx {
	struct time_vo;
	interface auxiliary {
	public:
		CUTE_REF_PTR_CLASS_DECL(auxiliary)
	public:
		TYPE_CAST_BASE_DEFINE(auxiliary)
		DELETE_MOVE_COPY(auxiliary)
		virtual void set_entity(cpx_entity*) = 0;
		virtual void enter() = 0;
		virtual void update(const tick_context*) = 0;
		virtual void exit() =0;
		virtual void try_endup(const time_vo&);
	protected:
		auxiliary();
	};

	template <typename T>
	interface auxiliary_impl : public auxiliary {
	public:
		TYPE_CAST_IMPLEMENT(T)
	};
}
