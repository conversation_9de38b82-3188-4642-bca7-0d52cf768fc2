#ifndef P_LOGGER_H
#define P_LOGGER_H     
#include "libcute.h"
#include "assert.h"
#define LOGGER_CFG "config/logsetting.conf"
LP_NS_BEGIN
#ifndef UL
#if defined __GNUC__
#define UL(v) v
#else
#define UL(v)(unsigned long)(v)
#endif
#endif
typedef struct log_buffer_s {
	char* buf;
	size_t size;
	size_t capacity;
} log_buffer;

#define MAX_STACK_FRAMES (6)
#define MAX_SYMBOL_NAME (256)
#ifndef SCRIPT_ROOT
#define SCRIPT_ROOT ("")
#endif

struct log_traceframe {
	char name[MAX_SYMBOL_NAME];
#if WIN32
	char file[MAX_SYMBOL_NAME];
	size_t addr;
	size_t line;
#endif
};

typedef enum {
	LOG_LEVEL_DEBUG = 20,
	LOG_LEVEL_INFO = 40,
	LOG_LEVEL_NOTICE = 60,
	LOG_LEVEL_WARN = 80,
	LOG_LEVEL_ERROR = 100,
	LOG_LEVEL_FATAL = 120
} log_level;

typedef struct log_traceinfo {
	struct log_traceframe frames[MAX_STACK_FRAMES];
} log_traceinfo;

/*

*/
LP_API const char* str_fmt(char* buf, const char* fmt, ...)CHECK_PRINTF(2, 3);
LP_API void call_log(const char* file, size_t filelen,
                     const char* func, size_t funclen,
                     long line, int level,
                     const char* format, ...)CHECK_PRINTF(7, 8);
LP_API void log_stack(const char* file, size_t filelen,
                      const char* func, size_t funclen,
                      long line, int level,
                      const char* format, ...)CHECK_PRINTF(7, 8);
LP_API void append_func(log_buffer* str,
                        const char* file, size_t filelen,
                        const char* func, size_t funclen,
                        long line);
LP_API int append_stack_info(log_buffer* str, const char* title, size_t title_size, int max_line);
LP_API int append_log(log_buffer* buffer, const char* src, size_t len);
LP_API void log_traceback(int);
LP_API int get_traceback(log_traceinfo* trace, int max_line);
/*

*/
#define log_init(category) dzlog_init(LOGGER_CFG, category)
/*

*/
#ifndef CUTE_LOG_LEVEL
#if DEBUG
		#define CUTE_LOG_LEVEL (0)
#else
		#define CUTE_LOG_LEVEL (3)
#endif
#endif
/*

*/
#if CUTE_LOG_LEVEL <= 0
#define log_debug(...)\
	call_log(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_DEBUG, __VA_ARGS__)
/**/
#define log_debug_stack(...)\
	log_stack(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_DEBUG, __VA_ARGS__)
#else
	#define log_debug(...)
	#define log_debug_stack(...)
#endif
/*

*/
#if CUTE_LOG_LEVEL <= 1
#define log_info(...)\
	call_log(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_INFO, __VA_ARGS__)
/**/
#define log_info_stack(...)\
	log_stack(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_INFO, __VA_ARGS__)
#else
	#define log_info(...)
	#define log_info_stack(...)
#endif
/*

*/
#if CUTE_LOG_LEVEL <= 2
#define log_warn(...)\
	call_log(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_WARN, __VA_ARGS__)
/**/
#define log_warn_stack(...)\
	log_stack(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_WARN, __VA_ARGS__)
#else
	#define log_warn(...)
	#define log_warn_stack(...)
#endif
/*

*/
#if CUTE_LOG_LEVEL <= 3
#define log_error(...)\
	call_log(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_ERROR, __VA_ARGS__)
/**/
#define log_error_stack(...)\
	log_stack(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_ERROR, __VA_ARGS__)
#else
	#define log_error(...)
	#define log_error_stack(...)
#endif
/*

*/
#if CUTE_LOG_LEVEL <= 4
#define log_fatal(...)\
	call_log(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_FATAL, __VA_ARGS__)
/**/
#define log_fatal_stack(...)\
	log_stack(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
	LOG_LEVEL_FATAL, __VA_ARGS__)
#else
	#define log_fatal(...)
	#define log_fatal_stack(...)
#endif
/*

*/
#define log_assert(cond, ...)\
do{if (!cond){log_fatal(__VA_ARGS__);assert(0);}}while (0)
/*

*/
LP_NS_END
#endif
