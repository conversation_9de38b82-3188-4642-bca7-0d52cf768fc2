#pragma once
#include "rpx_state.h"

namespace cpx {
	class cpx_state_any;

	class rpx_state_any final : public rpx_state {
	public:
		friend rpx_state;
		void enter() override;
		void update(const tick_context*) override;
		const cpx_state* think(const time_vo&);
		runtime_type rtype() const override;
	protected:
		bool update_transtion(const time_vo& now, cpx_transition::check_mode_t) override;
	};
}
