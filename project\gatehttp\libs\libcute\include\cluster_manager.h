#pragma once
#include "cluster_event.h"
#include "graph_central.h"
#include "graph_consistent.h"
#include "graph_route.h"
#include "graph_stat.h"
#include "service_aux.h"
#include "service_form.h"
#include "service_type.h"

namespace sys {
	class cluster_manager final : public service_aux_impl<cluster_manager> {
	public:
		typedef delegate<void (const cluster_event*)> invoke_t;
		CUTE_DELEGATE_FUNC_DECL(invoke_t, _invoker, addlistener, removelistener)
		invoke_t _invoker{};
	public:
		friend graph_central;
		friend graph_route;
		static cluster_manager& instance();
		cluster_manager();
		~cluster_manager() override;
		DELETE_MOVE_COPY(cluster_manager)
		void enter() override;
		void update(const update_context* ctx) override;
		void exit() override;
		bool ready() const;
		graph_consistent& cring();
		const graph_consistent& cring() const;
		graph_central& central();
		const graph_central& central() const;
		graph_route& routes();
		const graph_route& routes() const;
		cluster_manager& single_path(service_type);
		cluster_manager& run_stat(bool);
	private:
		void recv(const ref_ptr<service_form>&);
		static void on_central_cb(const graph_central::event_t*);
		int _single_path{};
		time_t _time{};
		graph_stat _stats{};
		graph_route _routes;
		graph_central _central{};
		graph_consistent _crings{};
	};
}
