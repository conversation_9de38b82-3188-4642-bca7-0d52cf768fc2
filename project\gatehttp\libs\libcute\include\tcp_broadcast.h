#pragma once
#include <vector>
#include  "libcute.h"
#include "ref_ptr.h"
#include "route_t.h"
class pb_base;

namespace net {
	class tcp_peer;
}

namespace net {
	class tcp_broadcast final {
	public :
		DELETE_MOVE_COPY(tcp_broadcast)
		explicit tcp_broadcast(route_t src);
		tcp_broadcast();
		int send(const pb_base*, const std::vector<ref_ptr<tcp_peer>>&) const;
		~tcp_broadcast();
	private:
		route_t _src;
	};
}
