#pragma once
#include "http_message.h"
#include "net_stream_reader.h"

namespace net {
	class http_peer;

	struct http_stream_reader final : net_stream_reader {
		DELETE_MOVE_COPY(http_stream_reader)
		explicit http_stream_reader(http_peer*);
		~http_stream_reader() override;
		http_message_t http{};
		void on_parse(http_phase) const;
		int read(ssize_t, const uv_buf_t*) override;
		void stat(stat_reader&, time_t) override;
		void reset();

	private:
		http_peer* _peer{};
	};
}
