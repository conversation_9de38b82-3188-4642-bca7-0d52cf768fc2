#pragma once
#include "libcute.h"

namespace sys {
	struct string_buffer {
		DELETE_MOVE_COPY(string_buffer)
		string_buffer();
		~string_buffer();
		string_buffer& stringfy();
		pb_buffer* buf();
		char* base() const;
		pb_slice slice() const;
		string_buffer& reserve(size_t);
		size_t size() const;
		size_t capacity() const;
		bool empty() const;
		void clear();
		string_buffer& append(pb_slice);
		string_buffer& append(char);
		string_buffer& append(const char*);
		string_buffer& append(const char*, size_t);
		string_buffer& append(const std::string&);
		string_buffer& operator <<(pb_slice);
		string_buffer& operator <<(const char*);
		string_buffer& operator <<(const std::string&);
		string_buffer& operator <<(int);

	private:
		pb_buffer _buf{};
	};
}
