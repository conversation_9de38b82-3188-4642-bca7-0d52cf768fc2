#pragma once
#include <libcute.h>
#include "http_def.h"
#include "http_parser.h"

namespace net {
	struct http_messagex;

	struct http_parser1 {
		typedef int (*on_http_parsed)(http_parser1*, const char*, size_t);
		DELETE_MOVE_COPY(http_parser1)
		http_parser1();
		~http_parser1();
		size_t recv(const char*, size_t);
		void handle_header();
		int invoke(const char* = nullptr, size_t = 0);
		bool done() const;
		int error() const;
		std::string errmsg(int = 0) const;
		http_parser parser;
		int flags{};
		http_phase state{};
		// tmp
		std::string url; // for on_url
		std::string header_field; // for on_header_field
		std::string header_value; // for on_header_value
		std::string sendbuf; // for GetSendData
		http_messagex* in{};
		http_messagex* out{};
		void* data{};
		on_http_parsed cb{};
	};
}
