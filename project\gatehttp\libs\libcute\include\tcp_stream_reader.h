#pragma once
#include "libcute.h"
#include "net_stream_reader.h"
#include "pb.h"
#include "uv.h"

namespace net {
	class tcp_peer;
	struct stat_reader;

	struct tcp_stream_reader final : net_stream_reader {
		DELETE_MOVE_COPY(tcp_stream_reader)
		explicit tcp_stream_reader(tcp_peer*);
		int read(ssize_t, const uv_buf_t*) override;
		void stat(stat_reader&, time_t) override;
		~tcp_stream_reader() override;

	private:
		struct {
			char base[4]{};
			int size{};
		} _head{};

		pb_buffer _buffer;
		uint32_t _expect{};
		time_t _stat_time{};
		tcp_peer* _peer{};

		struct {
			size_t size{};
			size_t count{};
		} _status{};
	};
}
