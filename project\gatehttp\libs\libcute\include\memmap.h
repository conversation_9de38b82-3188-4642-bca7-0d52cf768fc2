#pragma once
#include  "libcute.h"
#ifdef __GNUC__
#include <sys/mman.h>
#endif
#ifndef MS_ASYNC
#define MS_ASYNC (1)
#endif
#ifdef __GNUC__
typedef __off_t memsize_t;
#elif defined WIN32
typedef long int memsize_t ;
#define  MS_SYNC (1)
#endif
class memmap {
public:
	explicit memmap(const char* file);
	DELETE_MOVE_COPY(memmap)
	int oflag() const;
	int set_oflag(int v);
	int pflag() const;
	int set_flag(int v);
	int pshare() const;
	int set_pshare(int v);
	int flush(int mode = MS_SYNC) const;
	size_t size() const;
	char* rawdata() const;
	int map();
	int unmap();
	int resize(size_t);
	~memmap();
private:
	char _file[256];
	int _fd;
	void* _data;
	size_t _size;
	int _oflag;
	int _fpolicy;
	int _pflag;
	int _pshare;
};
