#pragma once
#include <vector>
#include <libcute.h>
#include "dvo_worker.h"
#include "dvo_property_event.h"

namespace dvo {
	class worker_propertyevent final : public worker_t {
	public:
		friend class data;
		TYPE_CAST_IMPLEMENT(worker_propertyevent)
		void start() override;
		void stop() override;
		void update(const update_context*) override;

	private:
		void push(const property_event& evt);
		void on_post_action(const update_context*);

	protected:
		void awake() override;

	private:
		std::vector<property_event> _events;
	};
}
