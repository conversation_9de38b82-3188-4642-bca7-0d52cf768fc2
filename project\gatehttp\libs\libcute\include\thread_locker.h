#pragma once
#include <mutex>
#include <libcute.h>
namespace sys {
	template <typename TLocker>
	struct thread_guard {
		DELETE_MOVE_COPY(thread_guard)

		explicit thread_guard(TLocker& d): _d(&d) {
			_d->lock();
		}

		~thread_guard() {
			_d->unlock();
		}

	private:
		TLocker* _d;
	};

	struct thead_no_lock {
		void lock() {
		}

		void unlock() {
		}
	};

	using thread_lock = std::recursive_mutex;
	using simple_lock = std::mutex;
}
