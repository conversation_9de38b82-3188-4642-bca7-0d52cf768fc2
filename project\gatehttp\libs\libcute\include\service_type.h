#ifndef SERVICE_TYPE_H
#define SERVICE_TYPE_H   
#include  "libcute.h"
LP_NS_BEGIN
#define  SERVICE_TYPES(XX)						\
	XX(0	, OVERVIEW		, "scfg:overview"	)	\
	XX(1	, NODE			, "scfg:node"		)	\
	XX(2	, CENTRAL		, "scfg:central"	)	\
	XX(3	, GATETCP		, "scfg:gatetcp"	)	\
	XX(4	, GATEHTTP		, "scfg:gatehttp"	)	\
	XX(5	, ADMIN 		, "scfg:admin"		)	\
	XX(6	, USRZONE		, "scfg:usrzone"	)	\
	XX(7	, GAME 			, "scfg:game"		)	\
	XX(8	, ROUTE			, "scfg:route"		)	\
	XX(9	, USRCENTER		, "scfg:usrcenter"	)	\
	XX(10	, PLACE_ROUTE	, "scfg:placeroute"	)	\
	XX(11	, ERR_LOG		, "scfg:error_log"	)	\
	XX(12	, MAX 			, "<unknown>"		)

typedef enum service_type {
#define  XX(num, name ,string )SVT_##name=(num),
	SERVICE_TYPES(XX)
#undef XX
} service_type;

/*
* 
* 
*/
#define PROC_TYPES(XX)							\
	XX(0, SERVICE		, "service"			)	\
	XX(1, SLAVE 		, "slave"			)	\
	XX(2, MASTER		, "master"			)

typedef enum service_role {
#define  XX(num, name ,string )SVR_##name=(num),
	PROC_TYPES(XX)
#undef XX
} service_role;

/*
* 
* 
*/
LP_API void service_type_init(void);
LP_API const char* service_type_str(int type);
LP_API void service_type_add(int type, const char* str);
#define service_type_append(num, name, string )service_type_add((num), string);
/*
* 
* 
*/
LP_NS_END
#endif
