#pragma once
#include <vector>
#include "type_enums.h"

namespace ds {
	struct thing;
	class thing_group;
}

namespace dvo {
	class usr_info;

	struct mail_context_t {
		usr_info* usr;
		tvo::server_word title;
		tvo::server_word content;
		tvo::mail_type type;
		ds::thing_group* group;
	};

	void submit_mail(const mail_context_t* ctx);

	struct gain_context_t {
		usr_info* usr;
		std::vector<ds::thing> things;
		int code;
	};

	void submit_gain(const gain_context_t* ctx);

	enum class client_error_level {
		kLog,
		kWarn,
		kError,
		kFatal
	};

	void throw_client_error(usr_info*, int code, client_error_level = client_error_level::kLog);
	void throw_client_error(usr_info*, const std::string&, client_error_level = client_error_level::kLog);
	void throw_client_error(usr_info*, int, const std::string&, client_error_level = client_error_level::kError);
}
