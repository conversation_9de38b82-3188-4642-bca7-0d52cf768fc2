#ifndef CUTE_SYS_REF_PTR_H
#define CUTE_SYS_REF_PTR_H
#pragma once
#include <cassert>
#include <cstddef>
#include <memory>
#include <atomic>
#include "sys_exception.h"
#include "type_cast.h"

template <typename T>
class ref_ptr {
public:
	using self_type = ref_ptr<T>;
	using element_type = T;
	using atomic_t = std::atomic<size_t>;

	constexpr ref_ptr() noexcept : px(nullptr) {
	}

	ref_ptr(T* p, bool add_ref = true) : px(p) {
		if (px != nullptr && add_ref) {
			ref_ptr_add_ref(px);
		}
	}

	template <typename U>
	ref_ptr(ref_ptr<U> const& rhs, std::enable_if_t<std::is_convertible_v<U*, T*>>* = nullptr) : px(rhs.get()) {
		if (px) ref_ptr_add_ref(px);
	}

	ref_ptr(self_type const& rhs) : px(rhs.px) {
		if (px) ref_ptr_add_ref(px);
	}

	~ref_ptr() {
		if (px) ref_ptr_release(px);
		px = nullptr;
	}

	template <typename U>
	friend class ref_ptr;

	template <typename U>
	ref_ptr& operator=(ref_ptr<U> const& rhs) {
		self_type(rhs).swap(*this);
		return *this;
	}

	// Move support
	ref_ptr(self_type&& rhs) noexcept : px(rhs.px) { rhs.px = nullptr; }

	ref_ptr& operator=(self_type&& rhs) noexcept {
		self_type(static_cast<self_type&&>(rhs)).swap(*this);
		return *this;
	}

	template <typename U>
	ref_ptr(ref_ptr<U>&& rhs, std::enable_if_t<std::is_convertible_v<U*, T*>>* = nullptr) noexcept
		: px(rhs.px) {
		rhs.px = nullptr;
	}

	template <typename U, typename Deleter>
	ref_ptr& operator=(std::unique_ptr<U, Deleter>&& rhs) {
		ref_ptr(rhs.release()).swap(*this);
		return *this;
	}

	ref_ptr& operator=(ref_ptr const& rhs) {
		if (this != &rhs) {
			self_type(rhs).swap(*this);
		}
		return *this;
	}

	void reset() noexcept { self_type().swap(*this); }
	void reset(element_type* rhs) { self_type(rhs).swap(*this); }
	void reset(element_type* rhs, bool add_ref) { self_type(rhs, add_ref).swap(*this); }
	element_type* get() const noexcept { return px; }

	template <typename U, std::enable_if_t<std::is_base_of_v<T, U>>* = nullptr>
	U* force_cast() const noexcept {
		return px ? sys::force_cast<U>(px) : nullptr;
	}

	element_type* detach() noexcept {
		element_type* ret = px;
		px = nullptr;
		return ret;
	}

	element_type& operator*() const {
		if (!px) {
			throw_e("using null_ptr");
		}
		return *px;
	}

	element_type* operator->() const {
		if (!px) {
			throw_e("using null_ptr");
		}
		return px;
	}

	// implicit conversion to "bool"
	operator bool() const noexcept { return px != nullptr; }
	// operator! is redundant, but some compilers need it
	bool operator!() const noexcept { return px == nullptr; }

	void swap(ref_ptr& rhs) noexcept {
		element_type* tmp = px;
		px = rhs.px;
		rhs.px = tmp;
	}

private:
	element_type* px;
};

template <typename T, typename U>
bool operator==(ref_ptr<T> const& a, ref_ptr<U> const& b) noexcept {
	return a.get() == b.get();
}

template <typename T, typename U>
bool operator==(ref_ptr<T> const& a, U* b) noexcept {
	return a.get() == b;
}

template <typename T, typename U>
bool operator==(T* a, ref_ptr<U> const& b) noexcept {
	return a == b.get();
}
#ifdef __cpp_impl_three_way_comparison
template <typename T, typename U>
inline std::strong_ordering operator<=>(ref_ptr<T> const &a, ref_ptr<U> const &b) noexcept {
  return a.get() <=> b.get();
}

template <typename T, typename U>
inline std::strong_ordering operator<=>(ref_ptr<T> const &a, U *b) noexcept {
  return a.get() <=> b;
}

template <typename T, typename U>
inline std::strong_ordering operator<=>(T *a, ref_ptr<U> const &b) noexcept {
  return a <=> b.get();
}
#else
template <typename T, typename U>
bool operator!=(ref_ptr<T> const& a, ref_ptr<U> const& b) noexcept {
	return a.get() != b.get();
}

template <typename T, typename U>
bool operator!=(ref_ptr<T> const& a, U* b) noexcept {
	return a.get() != b;
}

template <typename T, typename U>
bool operator!=(T* a, ref_ptr<U> const& b) noexcept {
	return a != b.get();
}

template <typename T, typename U>
bool operator<(ref_ptr<T> const& a, ref_ptr<U> const& b) noexcept {
	return a.get() < b.get();
}

template <typename T, typename U>
bool operator<(ref_ptr<T> const& a, U* b) noexcept {
	return a.get() < b;
}

template <typename T, typename U>
bool operator<(T* a, ref_ptr<U> const& b) noexcept {
	return a < b.get();
}

template <typename T, typename U>
bool operator<=(ref_ptr<T> const& a, ref_ptr<U> const& b) noexcept {
	return a.get() <= b.get();
}

template <typename T, typename U>
bool operator<=(ref_ptr<T> const& a, U* b) noexcept {
	return a.get() <= b;
}

template <typename T, typename U>
bool operator<=(T* a, ref_ptr<U> const& b) noexcept {
	return a <= b.get();
}

template <typename T, typename U>
bool operator>(ref_ptr<T> const& a, ref_ptr<U> const& b) noexcept {
	return a.get() > b.get();
}

template <typename T, typename U>
bool operator>(ref_ptr<T> const& a, U* b) noexcept {
	return a.get() > b;
}

template <typename T, typename U>
bool operator>(T* a, ref_ptr<U> const& b) noexcept {
	return a > b.get();
}

template <typename T, typename U>
bool operator>=(ref_ptr<T> const& a, ref_ptr<U> const& b) noexcept {
	return a.get() >= b.get();
}

template <typename T, typename U>
bool operator>=(ref_ptr<T> const& a, U* b) noexcept {
	return a.get() >= b;
}

template <typename T, typename U>
bool operator>=(T* a, ref_ptr<U> const& b) noexcept {
	return a >= b.get();
}
#endif
template <typename T>
bool operator==(ref_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() == nullptr;
}

template <typename T>
bool operator==(std::nullptr_t, ref_ptr<T> const& p) noexcept {
	return p.get() == nullptr;
}
#ifdef __cpp_impl_three_way_comparison
template <typename T>
inline std::strong_ordering operator<=>(ref_ptr<T> const &p, std::nullptr_t) noexcept {
  return p.get() <=> nullptr;
}

template <typename T>
inline std::strong_ordering operator<=>(std::nullptr_t, ref_ptr<T> const &p) noexcept {
  return p.get() <=> nullptr;
}
#else
template <typename T>
bool operator!=(ref_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() != nullptr;
}

template <typename T>
bool operator!=(std::nullptr_t, ref_ptr<T> const& p) noexcept {
	return p.get() != nullptr;
}

template <typename T>
bool operator<(ref_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() < nullptr;
}

template <typename T>
bool operator<(std::nullptr_t, ref_ptr<T> const& p) noexcept {
	return p.get() < nullptr;
}

template <typename T>
bool operator<=(ref_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() <= nullptr;
}

template <typename T>
bool operator<=(std::nullptr_t, ref_ptr<T> const& p) noexcept {
	return p.get() <= nullptr;
}

template <typename T>
bool operator>(ref_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() > nullptr;
}

template <typename T>
bool operator>(std::nullptr_t, ref_ptr<T> const& p) noexcept {
	return p.get() > nullptr;
}

template <typename T>
bool operator>=(ref_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() >= nullptr;
}

template <typename T>
bool operator>=(std::nullptr_t, ref_ptr<T> const& p) noexcept {
	return p.get() >= nullptr;
}
#endif
template <typename T>
void swap(ref_ptr<T>& lhs, ref_ptr<T>& rhs) noexcept {
	lhs.swap(rhs);
}

// mem_fn support
template <typename T>
T* get_pointer(ref_ptr<T> const& p) {
	return p.get();
}

template <typename T, typename U>
ref_ptr<T> static_pointer_cast(ref_ptr<U> const& p) {
	return static_cast<T*>(p.get());
}

template <typename T, typename U>
ref_ptr<T> const_pointer_cast(ref_ptr<U> const& p) {
	return const_cast<T*>(p.get());
}

template <typename T, typename U>
ref_ptr<T> dynamic_pointer_cast(ref_ptr<U> const& p) {
	return dynamic_cast<T*>(p.get());
}

template <typename T, typename U>
ref_ptr<T> force_pointer_cast(ref_ptr<U> const& p) {
	return sys::force_cast<T>(p.get());
}

// operator<<
template <typename E, typename T, typename Y>
std::basic_ostream<E, T>& operator<<(std::basic_ostream<E, T>& os, ref_ptr<Y> const& p) {
	os << p.get();
	return os;
}

#include <functional> // hash

namespace std {
	template <typename T>
	struct hash<ref_ptr<T>> {
		size_t operator()(const ref_ptr<T>& v) const noexcept {
			return reinterpret_cast<size_t>(v.get());
		}
	};
}

#define CUTE_SYS_REF_PTR_ATOMIC_TYPE ::std::atomic<size_t>
// -- member declare
#define CUTE_SYS_REF_PTR_MEMBER_DECL(T)									\
 public:																\
	using ptr_t = ref_ptr<T>;											\
	friend class ref_ptr<T>;											\
	size_t use_count() const { return __cute_refptr_counter__.load(); }	\
 private:																\
  CUTE_SYS_REF_PTR_ATOMIC_TYPE __cute_refptr_counter__{0};				\
  friend void ref_ptr_add_ref(T* p);									\
  friend void ref_ptr_release(T* p);
/*

*/
#define CUTE_SYS_REF_PTR_MEMBER_INIT() this->__cute_refptr_counter__.store(0)
/*
-----------------------function  declare -----------------------
 */
#define CUTE_SYS_REF_PTR_FN_DECL(T)			\
  void ref_ptr_add_ref(T* p);               \
  void ref_ptr_release(T* p);
/*
----------------------- function  define-----------------------
*/
#define CUTE_SYS_REF_PTR_FN_DEFI(T) \
  void ref_ptr_add_ref(T* p) {						\
    if (nullptr != p) {								\
      ++p->__cute_refptr_counter__;					\
    }												\
  }													\
  void ref_ptr_release(T* p) {						\
    if (nullptr == p) {								\
      return;										\
    }												\
    assert(p->__cute_refptr_counter__.load() > 0);		\
    size_t ref = --p->__cute_refptr_counter__;			\
    if (0 == ref) {									\
      delete p;										\
    }												\
  }
/*

*/
#define  CUTE_REF_PTR_CLASS_DECL(T)		\
CUTE_SYS_REF_PTR_MEMBER_DECL(T)
#endif
