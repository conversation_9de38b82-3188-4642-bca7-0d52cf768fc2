#pragma once 
#include <fp_math.hpp>
#include <bt_context.h>
#include <time_scale_type.h>
#include <composition_return_mode.h>

namespace bt {
	template <typename BaseT>
	class btree_parselector : public BaseT {
	public: 
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
		nav::float_t time{};
		time_scale_type timeScaleType{};
		composition_return_mode returnMode{};
	protected:
		void on_start(input_type* in, output_type* out) const final {
			default_context* ctx = base_type::template get_context<default_context>(in);
			ctx->status(BST_RUN);
		}

		void on_stop(input_type* in, output_type* out) const final {
		}

		btree_status on_update(input_type* in, output_type* out) const final {
			const auto st = internal_update(in, out);
			return adjust_status(st, returnMode);
		}
		 
	private:
		btree_status internal_update(input_type* in, output_type* out) const {
			default_context* ctx = base_type::template get_context<default_context>(in);
			bool running = false;
			for (size_t i = 0; i < base_type::size(); ++i) {
				const auto* c = base_type::child_at(i);
				const auto cst = c->status(in);
				switch (cst) {
					case BST_SUCCESS:
						return BST_SUCCESS;
					case BST_FAILURE:
						continue;
					case BST_IDLE:
					case BST_RUN:
					default:
						break;
				}
				const auto st = c->update(in, out);
				switch (st) {
					case BST_SUCCESS:
						c->stop(in, out);
						c->status(in, st);
						ctx->status(BST_SUCCESS);
						return st;
					case BST_FAILURE:
						c->status(in, st);
						c->stop(in, out);
						break;
					case BST_RUN:
						running = true;
						continue;
					case BST_IDLE:
					default:
						throw_e("should not be idle");
				}
			}
			return running ? BST_RUN : BST_FAILURE;
		}
	};
}
