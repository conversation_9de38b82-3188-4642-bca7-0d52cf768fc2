#pragma once
#include "net_stream_writer.h"
#include "wsdef.h"

namespace net {
	struct web_message;
	struct ws_stream_reader;

	struct ws_stream_writer final : net_stream_writer {
		int write(const char*, size_t) override;
		int write(const recycle_stream&) override;
		int dwrite(const char*, size_t);
		int dwrite(pb_slice);
		int flush() override;
		void get_uvbuf(uv_buf_t*) override;
		size_t size() override;
		void on_write_done() override;
		void stat(stat_writer&, time_t) override;
		int ping(time_t) override;
		int pong(time_t) override;
		int send_shakehand(const web_message&);
		ws_opcode opcode{WS_OPCODE_BINARY};
		ws_stream_reader* reader{};
		bool debug{};

	private:
		recycle_stream _writing{};
		recycle_stream _sending{};
		time_t _stat_time{};
		int64_t _queue{};
		bool _deflate{};

		struct {
			size_t length{};
			size_t count{};
		} _netstat;
	};
}
