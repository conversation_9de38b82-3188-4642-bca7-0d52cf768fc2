#pragma once
#include "libcute.h"
#include "route_t.h"
#include "service_form.h"
#include "tcp_peer.h"
#include "system_error.h"
#define TCP_AWAIT_TIMEOUT (30)

namespace sys {
	struct cluster_event;
}

namespace co {
	class tcp_await final : public co_job {
	public:
		DELETE_MOVE_COPY(tcp_await)
		explicit tcp_await(route_t dst);
		~tcp_await() override;

		template <typename T, std::enable_if_t<std::is_base_of_v<pb_base, T>> * = nullptr>
		int request(pb_base* in, T& out) {
			int err;
			if ((err = send(in))) {
				return err;
			}
			wait();
			if (!_bwd) {
				return ERR_USE_NULLPTR;
			}
			out = _bwd->obj<T>();
			if (!out) {
				return ERR_USE_NULLPTR;
			}
			return 0;
		}

	private:
		int send(const pb_base*);
		void wait();
		void on_route_event(const sys::cluster_event*);
		void check_timeout(time_t);
		void on_repsonse(const ref_ptr<service_form>&);
		bool _done{};
		service_form _fwd{};
		ref_ptr<service_form> _bwd{};
		time_t _wait{};
		route_t _dst;
	};
}
