#pragma once
#include <libcute.h>
#include <route_t.h>
#include <pb_scope.h>
#include <service_form.h>
#include <tcp_conn.h> 

#include "service_to_usrzone_code_wrapper.h"

namespace dvo {
	class player_vo;

	struct uz_writer {
		DELETE_MOVE_COPY(uz_writer)
		explicit uz_writer(player_vo*);
		~uz_writer();
		int find_server(route_t&) const;
		int save_fast() const;
		int save_full(const s2z_write&) const;

		template <typename Callback, typename ...Args,
		          std::enable_if_t<
			          std::is_invocable_r_v<int, Callback, player_vo*, pb_buffer*, Args...>
		          > * = nullptr>
		int write(Callback cb, const s2z_write& msg, const route_t& to, pb_buffer* buf, Args&& ... args) const {
			int err;
			mem::pb_size_scope scope_size(buf);
			{
				service_form form{};
				form.src(net::tcp_conn::get_self());
				form.dst(to);
				form.form_method = msg.wait
					                   ? service_form::PB_FUNC_REQUEST
					                   : service_form::PB_FUNC_SEND;
				form.class_id = msg.type();
				if ((err = form.encode(buf))) {
					scope_size.ignore();
					return err;
				}
				{
					mem::pb_bytes_scope scope_g(buf, service_form::kData);
					if ((err = msg.encode(buf))) {
						scope_size.ignore();
						return err;
					}
					if ((err = cb(_vo, buf, std::forward<Args>(args) ...))) {
						return err;
					}
				}
			}
			return 0;
		}

	private:
		player_vo* _vo;
	};
}
