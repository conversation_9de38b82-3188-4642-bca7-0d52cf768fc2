#ifndef SSL_BIO_H
#define SSL_BIO_H 
#include "libcute.h"
#include "pb.h"
#define SSL_BIO_SIZE 18432 /* 18 * 1024 to align the size of 17000*/
LP_NS_BEGIN
#define SSL_BIO_TYPE(XX)				\
XX(0, B<PERSON>_BUFFER	, "bio_buffer")		\
XX(1, BIO_SOCKET	, "bio_socket")		\
XX(2, BIO_SSL		, "bio_ssl")		\
XX(3, BIO_MEMORY	, "bio_memory")		\
XX(4, BIO_BIO		, "bio_bio")		\
XX(5, BIO_FILE		, "bio_file") 

//
//static const char* bio_type_str[] = {
//#define  XX(num, name ,string )#name
//	SSL_BIO_TYPE(XX)
//#undef XX
//};

enum bio_type {
#define  XX(num, name ,string )name=(num),
	SSL_BIO_TYPE(XX)
#undef XX
};

typedef struct ssl_bio ssl_bio;

typedef struct ssl_bio {
	const char* name ;
	ssl_bio* prev; /* previous in chain */
	ssl_bio* next; /* next in chain */
	ssl_bio* pair; /* BIO paired with */
	enum bio_type type; /* method */
	char* mem; /* memory buffer */
	size_t capacity;
	size_t idx_w; /* current index for write buffer */
	size_t idx_r; /* current index for read buffer */
	int req_r; /* read request */
} ssl_bio;

enum bio_error {
	SSL_SUCCESS = 0,
	SSL_BIO_MAKE_PAIR = -1,
	SSL_BIO_UNSET =-2,
	SSL_BIO_ERROR =-3,
	SSL_BIO_OUTMEM = -4
};

/* Joins two BIO_BIO types. The write of b1 goes to the read of b2 and vise
 * versa. Creating something similar to a two way pipe.
 * Reading and writing between the two BIOs is not thread safe, they are
 * expected to be used by the same thread. */
LP_API int ssl_bio_make_pair(ssl_bio* b1, ssl_bio* b2);
/* Return the number of pending bytes in read and write buffers */
LP_API size_t ssl_bio_pending(ssl_bio* bio);
LP_API int ssl_bio_read(ssl_bio* bio, char* buf, size_t len);
LP_API int ssl_bio_write(ssl_bio* bio, const unsigned char* buf, size_t len);
LP_API int ssl_bio_recv(void* ctx, unsigned char * buf, size_t len);
LP_API int ssl_bio_send(void* ctx,  const unsigned char * buf, size_t len);
LP_API ssl_bio* ssl_bio_new(enum bio_type type);
LP_API void ssl_bio_init(ssl_bio* bio);
LP_API void ssl_bio_free(ssl_bio* bio);
LP_API int ssl_bio_free_all(ssl_bio* bio);
LP_NS_END
#endif
