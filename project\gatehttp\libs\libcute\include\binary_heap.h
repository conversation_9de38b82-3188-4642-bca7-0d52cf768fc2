#pragma once
#include <vector>
#include "libcute.h"

namespace sys {
	template <typename T>
	class binary_heap {
	public:
		DEFAULT_MOVE_COPY(binary_heap)
		~binary_heap() = default;
		typedef bool (*compare_method)(const T&, const T&);
		typedef typename std::vector<T>::iterator iterator;
		typedef typename std::vector<T>::const_iterator const_iterator;

		explicit binary_heap(): _comparer(nullptr) {
			_list.emplace_back(T{});
		}

		explicit binary_heap(const compare_method method): _comparer(method) {
			_list.emplace_back(T{});
		}

		void compare_with(compare_method v) {
			_comparer = v;
		}

		void add(const T& val) {
			static T m_default{};
			if (val == m_default) {
				return;
			}
			std::size_t len = _list.size();
			std::size_t parent = len >> 1;
			_list.emplace_back(val);
			while (parent >= 1 && less_at(len, parent)) {
				swap(len, parent);
				len = parent;
				parent = len >> 1;
			}
		}

		T shift() {
			auto len = _list.size();
			if (len <= 1) {
				return T{};
			}
			std::size_t n = 1;
			T shift_obj = _list[n];
			_list[n] = _list.back();
			_list.pop_back();
			-- len;
			if (len <= 2) {
				return shift_obj;
			}
			if (len == 3) {
				if (!less_at(1, 2)) {
					swap(1, 2);
				}
			} else {
				auto left = n << 1;
				auto right = left + 1;
				while (right < len) {
					const auto min = less_at(left, right) ? left : right;
					if (less_at(n, min)) {
						// stop the binary heap resort immediator;		
						break;
					}
					swap(n, min);
					n = min;
					left = n << 1;
					right = left + 1;
				}
			}
			return shift_obj;
		}

		void clear() {
			if (_list.size() > 1) {
				_list.erase(_list.begin() + 1, _list.end());
			}
		}

		std::size_t size() { return _list.size(); }

		bool empty() {
			return _list.size() <= 1;
		}

		iterator begin() {
			return _list.begin() + 1;
		}

		iterator end() {
			return _list.end();
		}

		const_iterator begin() const {
			return _list.begin() + 1;
		}

		const_iterator end() const {
			return _list.end();
		}

		const_iterator cbegin() const {
			return _list.begin() + 1;
		}

		const_iterator cend() const {
			return _list.end();
		}

	private:
		std::vector<T> _list;
		compare_method _comparer;

		void swap(const std::size_t a, const std::size_t b) {
			if (a == b)return;
			std::swap(_list.at(a), _list.at(b));
		}

		bool less_at(const size_t a, const size_t b) const {
			if (a == b)return false;
			if (_comparer) {
				return _comparer(_list.at(a), _list.at(b));
			}
			return _list.at(a) < _list.at(b);
		}
	};
}
