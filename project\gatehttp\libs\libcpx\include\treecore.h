#pragma once
#include <vector>
#include <libcutex.h>
#include <libcute.h>
#include <sys_exception.h>
#include <type_cast.h>

namespace bt {
	template <class T>
	class treecore {
	public:
		DELETE_MOVE_COPY(treecore)
		virtual void parse(const dynamic_vo&) =0;
		virtual const char* class_n() const noexcept = 0;
		virtual int class_id() const noexcept = 0;

		void add(T* val) {
			_children.emplace_back(val);
			val->_parent = static_cast<T*>(this);
		}

		T* child_at(size_t at) const {
			if (at >= _children.size()) {
				throw_e("out of range [%lu / %lu]", at, _children.size());
			}
			return _children[at];
		}

		size_t size() const {
			return _children.size();
		}

		using const_iter = typename std::vector<T*>::const_iterator;

		const_iter begin() const {
			return _children.begin();
		}

		const_iter end() const {
			return _children.end();
		}

		void reserve(size_t v) {
			_children.reserve(v);
		}

		template <typename R, sys::is_true_type<R, treecore> * = nullptr>
		bool is() const {
			return R::assign_type() == class_id();
		}

		template <typename R, sys::is_true_type<R, treecore> * = nullptr>
		const R* as() const {
			bool ok = R::assign_type() == class_id();
			return ok ? static_cast<const R*>(this) : nullptr;
		}

		template <typename R, sys::is_true_type<R, treecore> * = nullptr>
		const R* as() {
			bool ok = R::assign_type() == class_id();
			return ok ? static_cast<R*>(this) : nullptr;
		}

		template <typename R, sys::is_virtual_type<R, treecore> * = nullptr>
		const R* as() const {
			return dynamic_cast<const R*>(this);
		}

		template <typename R, sys::is_virtual_type<R, treecore> * = nullptr>
		R* as() {
			return dynamic_cast<R*>(this);
		}

		size_t idx{};
		size_t tree_size{};
		uint32_t cnt_ignord{};
		T* root{};
		std::string cmd;

		virtual ~treecore() {
			for (auto* c : _children) {
				delete c;
			}
			_children.clear();
			_parent = nullptr;
		}

	protected:
		treecore() = default;

	private:
		std::vector<T*> _children{};
		T* _parent{};
	};
}
