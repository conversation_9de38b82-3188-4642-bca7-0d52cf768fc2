#pragma once
#include <uni_task.h>
namespace dvo {
	class usr_task_collection {
	public:
		DELETE_MOVE_COPY(usr_task_collection)
		explicit usr_task_collection(player_vo*);
		~usr_task_collection();
		void add(const ref_ptr<uni_task>&);
		void update(const cpx::time_vo&);
		ref_ptr<uni_task> find(int) const;
		size_t size() const;
		bool empty() const;
		using task_map_t = sys::hashmapx<int, ref_ptr<uni_task>>;
		using const_iterator = task_map_t::const_iterator;
		const_iterator begin() const;
		const_iterator end() const;

	private:
		sys::hashmapx<int, ref_ptr<uni_task>> _tasks;
	};
}
