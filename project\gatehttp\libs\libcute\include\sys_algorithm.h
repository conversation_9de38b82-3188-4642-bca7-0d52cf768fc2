#pragma once
#include <algorithm>
#include <functional>
#include <vector>

namespace sys {
	template <typename T, typename Rty>
	void shuffle(std::vector<T> list, Rty& seed) {
		const size_t size = list.size();
		for (size_t i = 0; i < list.size(); ++i) {
			auto idx = static_cast<size_t>(seed() * size);
			std::swap(list[i], list[idx]);
		}
	}

	template <typename T>
	int indexof(const std::vector<T>& list, T element) {
		auto it = std::find(list.begin(), list.end(), element);
		return it == list.end() ? -1 : static_cast<int>(it - list.begin());
	}

	/*
		@find the first element great-equal then value, 
		the input list must be sorted low to high
		comparer must return -1, 0, 1, 
			-1 keep search left , 
			 0 return the current value 
			 1 keep search right 
	*/
	template <typename T, typename Comparer, typename... Args,
	          std::is_invocable_r<int, Comparer, T, Args...> * = nullptr>
	T binary_fst_great_equal(const std::vector<T>& list, Comparer&& comparer, Args&&... args) {
		if (list.empty()) {
			return T{};
		}
		T result{};
		int left{};
		int right = static_cast<int>(list.size()) - 1;
		while (left <= right) {
			int mid = left + (right - left) / 2;
			const T& e = list.at(mid);
			const int s = comparer(e, std::forward<Args>(args)...);
			if (s > 0) {
				result = e;
				right = mid - 1;
			} else if (s < 0) {
				left = mid + 1;
			} else if (s == 0) {
				return e;
			}
		}
		return result;
	}

	/*
		@find the last element less-equal than value, 
		the input list must be sorted low to high
		comparer must return -1, 0, 1, 
			-1 keep search left , 
			 0 return the current value 
			 1 keep search right 
	*/
	template <typename T, typename Comparer, typename... Args,
	          std::is_invocable_r<int, Comparer, T, Args...> * = nullptr>
	T binary_last_less_equal(const std::vector<T>& list, Comparer&& comparer, Args&&... args) {
		if (list.empty()) {
			return T{};
		}
		T result{};
		int left{};
		int right = static_cast<int>(list.size()) - 1;
		while (left <= right) {
			int mid = left + (right - left) / 2;
			const T& e = list.at(mid);
			const int s = comparer(e, std::forward<Args>(args)...);
			if (s < 0) {
				result = e;
				left = mid + 1;
			} else if (s > 0) {
				right = mid - 1;
			} else if (s == 0) {
				return e;
			}
		}
		return result;
	}

	/*
	* @brief: difference from std::remove_if, the removed element will be moved to end of sequence
	*			std::remove_if (is_odd) 1,2,3,4 => 1,3,3,4
	*			sys::remove_if (is_odd) 1,2,3,4 => 1,3,2,4
	*  @param  first  A forward iterator.
	*  @param  last   A forward iterator.
	*  @param  pred   A predicate.
	*  @return   An iterator designating the end of the resulting sequence.
	*/
	template <typename FowardIterator, typename Pred>
	FowardIterator remove_if(FowardIterator first, FowardIterator last, Pred check) {
		while (first != last && !check(*first)) {
			++first;
		}
		if (first == last) {
			return last;
		}
		auto next = first + 1;
		while (next != last) {
			while (next != last && check(*next)) {
				++next;
			}
			if (next != last) {
				std::swap(*next, *first);
				++first;
				++next;
			}
		}
		return first;
	}

	template <typename FowardIterator, typename Pred>
	FowardIterator remove_except(FowardIterator first, FowardIterator last, Pred check) {
		while (first != last && check(*first)) {
			++first;
		}
		if (first == last) {
			return last;
		}
		auto next = first + 1;
		while (next != last) {
			while (next != last && !check(*next)) {
				++next;
			}
			if (next != last) {
				std::swap(*next, *first);
				++first;
				++next;
			}
		}
		return first;
	}

	/*
	* @brief: difference from std::remove_if, the removed element will be moved to end of sequence
	*			std::remove_if (is_odd) 1,2,3,4 => 1,3,3,4
	*			sys::remove_if (is_odd) 1,2,3,4 => 1,3,2,4
	*  @param  first  A forward iterator.
	*  @param  last   A forward iterator.
	*  @param  val	  A value to remove.
	*  @return   An iterator designating the end of the resulting sequence.
	*/
	template <typename FowardIterator, typename T>
	FowardIterator remove(FowardIterator first, FowardIterator last, T val) {
		while (first != last && *first != val) {
			++first;
		}
		if (first == last) {
			return last;
		}
		auto next = first + 1;
		while (next != last) {
			while (next != last && *next == val) {
				++next;
			}
			if (next != last) {
				std::swap(*next, *first);
				++first;
				++next;
			}
		}
		return first;
	}

	template <typename T, typename ValueType, std::enable_if_t<std::is_arithmetic_v<ValueType>>* = nullptr>
	T* lower_bound(std::vector<T*>& list, const size_t offset, const ValueType compare) {
		using value_type = ValueType;
		std::sort(list.begin(), list.end(), [offset](T* a, T* b) {
			value_type va = (value_type)(*((char*)a + offset));
			value_type vb = (value_type)(*((char*)b + offset));
			return va > vb;
		});
		for (auto* a : list) {
			value_type va = (value_type)(*((char*)a + offset));
			if (va <= compare) {
				return a;
			}
		}
		return list.back();
	}
}

#define find_lowerbound(T, f, list,v)sys::lower_bound(list, offsetof(T, f), v)
