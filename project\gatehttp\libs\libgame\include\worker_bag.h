#pragma once
#include <set>
#include <vector>
#include <delegate.h>
#include "data_props.h"
#include "render_interface.h"
#include "usr_worker.h"
#include "bag_event.h"
#include <bag_key.h>

namespace dvo {
	struct sheet_event;
}

namespace buff {
	class buff_runner;
}

namespace ds {
	struct render_info;
	class bag_container;

	struct bag_owner_bind {
		int owner_type{};
		int owner_class{};
		int initial{};
	};

	class worker_bag final : public dvo::usr_worker,
	                         implements irender_info_retriever {
	public:
		typedef delegate_vo<sys::thead_no_lock, void (const bag_event*)> invoker_t;
		CUTE_DELEGATE_FUNC_DECL(invoker_t, _invoke, addlistener, removelistener)
	public:
		friend bag_container;
		TYPE_CAST_IMPLEMENT(worker_bag)
		DELETE_MOVE_COPY(worker_bag)
		worker_bag();
		~worker_bag() override;
		void start() override;
		void stop() override;
		void update(const dvo::update_context*) override;
		void get_renderinfo(std::vector<render_info>& to) const override;
		void equip_buff(std::vector<buff::buff_runner*>&, int posmask);

	private:
		void push(const bag_event*);
		void on_sheet_change(const dvo::sheet_event*);
		void clear_locker();
		void release();
		void release_events();
		std::vector<bag_event> _events{};
		std::vector<bag_event> _helps{};
		std::set<bag_container*> _locked{};
		invoker_t _invoke;
		bool _invoking{};
		int _recrusive{};
		bool _started{};

	public:
		bag_container* find(int bag_type) const;
		bag_container* find(bag_key) const;
		bag_container* make(bag_key);
		using iter = sys::hashmapx<bag_key, bag_container*>::const_iterator;
		iter begin() const;
		iter end() const;
		static void register_bag_owner_bind(bag_owner_bind);

	private:
		void on_notifiction(const dvo::worker_notification*) override;
		void on_load();
		void load_to_bag(dvo::data_props*);
		void delete_bags();
		sys::hashmapx<bag_key, bag_container*> _bags;
	};
}
