#ifndef PB_H
#define PB_H
#if defined(_MSC_VER) || defined(__UNIXOS2__) || defined(__SOL64__)
typedef unsigned char uint8_t;
typedef signed char int8_t;
typedef unsigned short uint16_t;
typedef signed short int16_t;
typedef unsigned int uint32_t;
typedef signed int int32_t;
typedef unsigned long long uint64_t;
typedef signed long long int64_t;
#ifndef INT64_MIN
#define INT64_MIN LLONG_MIN
#define INT64_MAX LLONG_MAX
#endif
#elif defined(__SCO__) || defined(__USLC__) || defined(__MINGW32__)
# include <stdint.h>
#else
# include <inttypes.h>
# if (defined(__sun__) || defined(__digital__))
#   if defined(__STDC__) && (defined(__arch64__) || defined(_LP64))
typedef unsigned long int  uint64_t;
typedef signed   long int   int64_t;
#   else
typedef unsigned long long uint64_t;
typedef signed   long long  int64_t;
#   endif /* LP64 */
# endif /* __sun__ || __digital__ */
#endif
#include <stddef.h>
#include <limits.h>
#include "libcute.h"
LP_NS_BEGIN
/* types */
#define PB_WIRETYPES(X) /* X(name, index) */\
    X(VARINT, "varint", 0) X(64BIT,  "64bit", 1) X(BYTES, "bytes", 2)  \
    X(GSTART, "gstart", 3) X(GEND,   "gend",  4) X(32BIT, "32bit", 5)
#define PB_TYPES(X)           /* X(name, type, index) */\
    X(double,   double,   1)  X(float,    float,    2)  \
    X(int64,    int64_t,  3)  X(uint64,   uint64_t, 4)  \
    X(int32,    int32_t,  5)  X(fixed64,  uint64_t, 6)  \
    X(fixed32,  uint32_t, 7)  X(bool,     int,      8)  \
    X(string,   pb_Slice, 9)  X(group,    pb_Slice, 10) \
    X(message,  pb_Slice, 11) X(bytes,    pb_Slice, 12) \
    X(uint32,   uint32_t, 13) X(enum,     int32_t,  14) \
    X(sfixed32, int32_t,  15) X(sfixed64, int64_t,  16) \
    X(sint32,   int32_t,  17) X(sint64,   int64_t,  18)

typedef enum pb_WireType {
#define X(name, s, index) PB_T##name,
	PB_WIRETYPES(X)
#undef  X
	PB_TWIRECOUNT
} pb_WireType;

typedef enum pb_FieldType {
#define X(name, type, index) PB_T##name = (index),
	PB_TYPES(X)
#undef  X
	PB_TYPECOUNT
} pb_FieldType;

/* --------------------------------conversions ------------------------------------------- */
LP_API uint64_t pb_expandsig(uint32_t);
LP_API uint32_t pb_encode_sint32(int32_t);
LP_API int32_t pb_decode_sint32(uint32_t);
LP_API uint64_t pb_encode_sint64(int64_t);
LP_API int64_t pb_decode_sint64(uint64_t);
LP_API uint32_t pb_encode_float(float);
LP_API float pb_decode_float(uint32_t);
LP_API uint64_t pb_encode_double(double);
LP_API double pb_decode_double(uint64_t);

/*----------------------------------decode ------------------------------------------------*/
typedef struct pb_slice {
	const char* ptr;
	const char* end;
} pb_slice;

#define pb_gettype(v)       ((v) &  7)
#define pb_gettag(v)        ((v) >> 3)
#define pb_pair(tag, type)  ((tag) << 3 | ((type) & 7))
/*make a str slice*/
LP_API pb_slice pb_slice_str(const char* str);
/*make a slice with buf and size*/
LP_API pb_slice pb_slice_buf(const char* buf, size_t len); 
LP_API size_t pb_slice_len(pb_slice);
LP_API size_t pb_readvarint32(pb_slice* s, uint32_t* pv);
LP_API size_t pb_readvarint64(pb_slice* s, uint64_t* pv);
LP_API size_t pb_readfixed32(pb_slice* s, uint32_t* pv);
LP_API size_t pb_readfixed64(pb_slice* s, uint64_t* pv);
LP_API size_t pb_readslice(pb_slice* s, size_t len, pb_slice* pv);
LP_API size_t pb_readbytes(pb_slice* s, pb_slice* pv);
LP_API size_t pb_readgroup(pb_slice* s, uint32_t tag, pb_slice* pv);
LP_API size_t pb_skipvarint(pb_slice* s);
LP_API size_t pb_skipbytes(pb_slice* s);
LP_API size_t pb_skipslice(pb_slice* s, size_t len);
LP_API size_t pb_skipvalue(pb_slice* s, uint32_t tag);
LP_API const char* pb_wtypename(int wiretype, const char* def);
LP_API const char* pb_typename(int type, const char* def);
LP_API int pb_typebyname(const char* name, int def);
LP_API int pb_wtypebyname(const char* name, int def);
LP_API int pb_wtypebytype(int type);
/* ---------------------------encode -----------------------------------------------*/
#define PB_BUFFERSIZE   (512)

typedef struct pb_buffer {
	size_t size;
	size_t capacity;
	char* base;
	char init_buff[PB_BUFFERSIZE];
} pb_buffer;

#define pb_bufflen(b)     ((b)->size)
#define pb_addsize(b, sz) ((void)((b)->size += (sz)))
#define pb_addchar(b, ch) \
    ((void)((b)->size < (b)->capacity || pb_buf_expand((b), 1)), \
     ((b)->base[(b)->size++] = (ch)))
LP_API void pb_buf_init(pb_buffer* b);
LP_API void pb_buf_free(pb_buffer* b);
LP_API void pb_buf_clear(pb_buffer* b);
LP_API void* pb_buf_reserve(pb_buffer* b, size_t len);
LP_API void* pb_buf_expand(pb_buffer* b, size_t len);
LP_API int pb_buf_uncompress(pb_buffer* dst,   pb_slice src,size_t *outsize);
LP_API int pb_buf_compress(pb_buffer* dst,   pb_slice src, int level, size_t *outsize);
/*move a to b , and a will be empty*/
LP_API void* pb_buf_move(pb_buffer* a, pb_buffer* b);
LP_API pb_slice pb_result(const pb_buffer* b);
LP_API size_t pb_addvarint32(pb_buffer* b, uint32_t v);
LP_API size_t pb_addvarint64(pb_buffer* b, uint64_t v);
LP_API size_t pb_addfixed32(pb_buffer* b, uint32_t v);
LP_API size_t pb_addfixed64(pb_buffer* b, uint64_t v);
LP_API size_t pb_addslice(pb_buffer* b, pb_slice s);
LP_API size_t pb_addbuf(pb_buffer* b, pb_buffer* s);
LP_API size_t pb_addbytes(pb_buffer* b, pb_slice s);
LP_API size_t pb_addlength(pb_buffer* b, size_t len);
LP_API size_t pb_insertlength(pb_buffer* b, size_t len);
LP_API void pb_swapbuf(pb_buffer* a, pb_buffer* b); 


/*----------------block--------------------------------------------------------------*/
#define PB_BLOCKSIZE (4096)

typedef struct pb_block {
	char base[PB_BLOCKSIZE];
	size_t capacity;
	size_t size;
} pb_block;

LP_API void pb_block_init(pb_block* b);
LP_API void pb_block_free(pb_block* b);
/*return: size of added, -1 means overflow*/
LP_API int pb_block_addslice(pb_block* b, pb_slice slice);
/*-----------------state----------------------------------------------------------------*/
/* type info database state and name table */
typedef struct pb_state pb_state;
LP_API pb_state* pb_state_new(void);
LP_API void pb_state_init(pb_state* S);
LP_API void pb_state_free(pb_state* S);
LP_API void pb_state_output(pb_state* S);
LP_API void pb_state_detail_file(pb_state * S, const char* file);
typedef struct pb_Name pb_Name;
LP_API pb_Name* pb_newname(pb_state* S, pb_slice s);
LP_API void pb_delname(pb_state* S, pb_Name* name);
LP_API pb_Name* pb_name(const pb_state* S, const char* name);
LP_API pb_Name* pb_usename(pb_Name* name);
/* type info */
typedef struct pb_Type pb_Type;
typedef struct pb_Field pb_Field;
#define PB_OK     0
#define PB_ERROR  1
#define PB_ENOMEM 2
LP_API int pb_loadfile(pb_state* state, const char* file);
LP_API int pb_readfile(const char*, pb_buffer*);
LP_API pb_state* pb_load_version(const char* file, int8_t compress);
LP_API int pb_load(pb_state* S, pb_slice* s);
LP_API pb_Type* pb_newtype(pb_state* S, pb_Name* tname);
LP_API void pb_deltype(pb_state* S, pb_Type* t);
LP_API pb_Field* pb_newfield(pb_state* S, pb_Type* t, pb_Name* fname, int32_t);
LP_API void pb_delfield(pb_state* S, pb_Type* t, pb_Field* f);
LP_API pb_Type* pb_type(pb_state* S, pb_Name* tname);
LP_API pb_Type* pb_name_to_type(pb_state* S, const char* name);
LP_API pb_Field* pb_fname(pb_Type* t, pb_Name*);
LP_API pb_Field* pb_field(pb_Type* t, int32_t number);
LP_API pb_Name* pb_oneofname(pb_Type* t, int);
LP_API int pb_nexttype(pb_state* S, pb_Type** ptype);
LP_API int pb_nextfield(pb_Type* t, pb_Field** pfield);
/* util: memory pool */
#define PB_POOLSIZE 4096

typedef struct pb_pool {
	void* pages;
	void* freed;
	size_t obj_size;
} pb_pool;

LP_API void pb_initpool(pb_pool* pool, size_t obj_size);
LP_API void pb_freepool(pb_pool* pool);
LP_API void* pb_poolalloc(pb_pool* pool);
LP_API void pb_poolfree(pb_pool* pool, void* obj);
/* util: hash table */
typedef struct pb_table pb_table;
typedef struct pb_entry pb_entry;
typedef ptrdiff_t pb_key;
LP_API void pb_table_init(pb_table* t, size_t entrysize);
LP_API void pb_table_free(pb_table* t);
LP_API size_t pb_table_resize(pb_table* t, size_t size);
LP_API pb_entry* pb_table_get(pb_table* t, pb_key key);
LP_API pb_entry* pb_table_set(pb_table* t, pb_key key);
LP_API int pb_nextentry(pb_table* t, pb_entry** pentry);

struct pb_table {
	size_t size;
	size_t lastfree;
	unsigned entry_size : sizeof(unsigned) * CHAR_BIT - 1;
	unsigned has_zero : 1;
	pb_entry* hash;
};

struct pb_entry {
	ptrdiff_t next;
	pb_key key;
};

/* fields */
typedef struct pb_NameEntry {
	struct pb_NameEntry* next;
	unsigned hash : 32;
	unsigned length : 16;
	unsigned refcount : 16;
} pb_NameEntry;

typedef struct pb_NameTable {
	size_t size;
	size_t count;
	pb_NameEntry** hash;
} pb_NameTable;

struct pb_state {
	pb_table types;
	pb_NameTable nametable;
	pb_pool typepool;
	pb_pool fieldpool;
	//decode method ----------------
	int defs_index;
	unsigned enum_as_value : 1;
	unsigned default_mode : 2; /* lpb_DefMode */
	unsigned int64_mode : 2;
	uint32_t version;
};

struct pb_Field {
	pb_Name* name;
	pb_Type* type;
	pb_Name* default_value;
	int32_t number;
	unsigned oneof_idx : 24;
	unsigned type_id : 5; /* PB_T* enum */
	unsigned repeated : 1;
	unsigned packed : 1;
	unsigned scalar : 1;
};

struct pb_Type {
	pb_Name* name;
	const char* basename;
	pb_table field_tags;
	pb_table field_names;
	pb_table oneof_index;
	unsigned field_count : 28;
	unsigned is_enum : 1;
	unsigned is_map : 1;
	unsigned is_proto3 : 1;
	unsigned is_dead : 1;
};

/* state */
typedef struct pb_TypeEntry {
	pb_entry entry;
	pb_Type* value;
} pb_TypeEntry;

typedef struct pb_FieldEntry {
	pb_entry entry;
	pb_Field* value;
} pb_FieldEntry;

typedef struct pb_OneofEntry {
	pb_entry entry;
	pb_Name* name;
	unsigned index;
} pb_OneofEntry;

#define PB_MAX_SIZET          ((size_t)~0 - 100)
#define PB_MIN_STRTABLE_SIZE  16
#define PB_MIN_HASHTABLE_SIZE 8
#define PB_HASHLIMIT          5
LP_NS_END
#endif /* pb_h */
