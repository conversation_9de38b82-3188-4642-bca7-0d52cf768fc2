#pragma once
#include "any.h"
#include "hashmapx.h"
#include  "libcute.h"

namespace sys {
	class blackboard {
	public:
		using iter = hashmapx<int64_t, any>::const_iterator;
		DEFAULT_MOVE_COPY(blackboard)
		~blackboard();
		blackboard();

		template <typename T>
		void put(const int64_t k, const T& v) {
			_map.emplace(k, any(v));
		}

		template <typename T>
		bool tryget(const int64_t k, T& v) {
			any a;
			if (!_map.tryget(k, a) ||
				!a.is<T>()) {
				return false;
			}
			v = a.cast<T>();
			return true;
		}

		void erase(int64_t k);
		void clear();
		bool empty() const;
		size_t size() const;
		iter begin() const;
		iter end() const;
	private:
		hashmapx<int64_t, any> _map;
	};
}
