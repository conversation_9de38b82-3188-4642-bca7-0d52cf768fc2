#pragma once
#include <cpx_action.h>
#include "cpx_action_builtin_wrapper.h"

namespace cpx {
	class cpx_act_set_inited final : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_act_set_inited)
		CPXA_CPX_ACT_SET_INITED_WRAPPER()
		cpx_act_set_inited();
		~cpx_act_set_inited() override;
	protected: 
		void on_start(cpx_input*, cpx_output*) const override;
		void on_stop(cpx_input*, cpx_output*) const override;
		bt::btree_status on_update(cpx_input*, cpx_output*) const override;
	};
}
