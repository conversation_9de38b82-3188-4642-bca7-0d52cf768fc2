//gen by cpp_autogen automatically, don't modify it!
#pragma once
#include <cpx_action.h>
#include "cpx_action_builtin_wrapper.h"

namespace cpx {
	class cpx_act_sub_control final : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_act_sub_control)
		CPXA_CPX_ACT_SUB_CONTROL_WRAPPER()
		cpx_act_sub_control();
		~cpx_act_sub_control() override;
	protected:
		void on_start(cpx_input*, cpx_output*) const override;
		void on_stop(cpx_input*, cpx_output*) const override;
		bt::btree_status on_update(cpx_input*, cpx_output*) const override;
	};
}
