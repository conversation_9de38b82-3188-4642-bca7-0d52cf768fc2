#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"

namespace tvo {
	class type_hero;

	class type_slot final : public type_data {
	public:
		DELETE_MOVE_COPY(type_slot)
		static void awake_all(const awake_context_t&);
		~type_slot() override;
	public:
		//enums decl! @see TYPE_SLOT_WRAPPER() in typeclass_define.h
		enum type_t {
			SKILL,
			SKILL_POSITIVE,
			PROPS,
			ASSISTANT
		};

		//enums decl! @see TYPE_SLOT_WRAPPER() in typeclass_define.h
		enum display_mode_t {
			ALWAYS,
			DATA,
			HIDE
		};

		TYPE_SLOT_WRAPPER()
		type_slot();
	};
}
