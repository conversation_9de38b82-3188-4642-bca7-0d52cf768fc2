#pragma once
#include <fp_math.hpp>
#include <hashmapx.h>
#include  <libcute.h>

namespace cpx {
	class shader_collection {
	public:
		DELETE_MOVE_COPY(shader_collection)
		shader_collection();
		~shader_collection();

		enum property_type {
			kColor,
			kFloat,
			kBool,
		};

		struct slot_t {
			nav::float_t value{};
			int type{};
			int64_t frame{};
			int color_value() const;
			int int_value() const;
			bool bool_value() const;
			nav::float_t float_value() const;
			bool operator ==(const slot_t&) const;
			bool operator !=(const slot_t&) const;
		};

		struct collection_t {
			DELETE_MOVE_COPY(collection_t)
			collection_t();
			~collection_t();
			sys::hashmapx<int, slot_t> slots;
		};

		const collection_t& main() const;
		collection_t& main();

		struct visiblity_t {
			int bone{};
			int visible{};
			bool operator ==(visiblity_t) const;
		};

		void set_visible(int b, bool v);
		const sys::hashmapx<int, visiblity_t>& visibles() const;

	private:
		collection_t _main;
		sys::hashmapx<int, visiblity_t> _visibles;
	};
}
