#pragma once
#include "cpx_action_builtin_wrapper.h"
#include "cpx_composition.h"

namespace cpx {
	class cpx_act_selector final : public cpx_composition {
	public:
		DELETE_MOVE_COPY(cpx_act_selector)
		CPXA_CPX_ACT_SELECTOR_WRAPPER()
		cpx_act_selector();
		~cpx_act_selector() override;
	protected: 
		void on_stop(cpx_input*, cpx_output*) const override;
		void cpx_start(cpx_input*, cpx_output*) const override;
		bt::btree_status cpx_update(cpx_input*, cpx_output*) const override;
		bt::bt_context* context(cpx_input*) const override;
	};
}
