#ifndef  SYSTEM_ERRORC_H
#define  SYSTEM_ERRORC_H 
#include "libcute.h"
LP_NS_BEGIN
#define ERROR_C_CODE(XX)												\
XX(0	, OK 					, "every thing ok"					)	\
XX(501	, OUT_MEM				, "out of memory"					)	\
XX(502	, NOT_FOUND				, "no target found"					)	\
XX(503	, OUT_RANGE				, "out of range"					)	\
XX(504	, BAD_ARGS				, "bad arguments"					)	\
XX(505	, ITER_VERSION			, "iter_version"					)	\
XX(506	, USE_NULLPTR			, "using nullptr"					)	\
/*--------------------file		-----------------------------------*/	\
XX(510	, FILE_X_FAIL			, "file is not excutable"			)	\
XX(511	, FILE_R_FAIL			, "read file fail"					)	\
XX(512	, FILE_W_FAIL			, "write file fail"					)	\
XX(513	, FIL<PERSON>_LOST				, "file not exit"					)	\
XX(514	, MMAP_FAIL				, "mmap file fail"					)	\
XX(515	, MMAP_RESIZE_FAIL		, "mmap file resize fail"			)	\
/*--------------------service	----------------------------------*/	\
XX(520	, SV_NO_COMMAND			, "unknown service http command"	)	\
XX(521	, SV_CO_THREAD			, "must in co_thread "				)	\
XX(522	, SV_UNKNOWN			, "<unknown error> "				)	\
/*--------------------db		----------------------------------*/	\
XX(530	, SQL_INIT_FAIL			, "mysql init fail"					)	\
XX(531	, SQL_INVALID_URL		, "invalid mysql url"				)	\
XX(532	, SQL_CONNECT_FAIL		, "mysql connect fail"				)	\
/*--------------------http		---------------------------------*/		\
XX(540	, HTTP_REQUEST_ERROR	, "http_request_error"				)	\
/*--------------------co_thread		--------------------------------*/	\
XX(550	, CO_THREAD_LOST		, "co_thread::ptr lost"				)	\
/*--------------------http		---------------------------------*/		\
XX(560	, PB_NO_TYPE_FOUND		, "pb type not found"				)	\
XX(561	, PB_ENCODE_FAIL		, "pb encode fail"					)	\
XX(562	, PB_DECODE_FAIL		, "pb decode fail"					)	\
XX(563	, PB_ERROR_TYPE			, "pb error type"					)	\
XX(564	, PB_STATE_LOST			, "pb state lost"					)	\
XX(565	, PB_TYPE_LOST			, "pb type lost"					)	\
XX(566	, PB_FIELD_LOST			, "pb field lost"					)	\
XX(567	, COMPRESS_FAIL			, "compress fail"					)	\
XX(568	, UNCOMPRESS_FAIL		, "uncompress fail"					)	\
/*--------------------zone		---------------------------------*/		\
XX(601	, TCP_PEER_DISCONNETED		, "tcp peer disconnected"				)	\
XX(602	, NO_CONSISTENT_ROUTE		, "saux_consisitent_route not register"	)	\
XX(603	, CONSISTENT_ROUTE_EMPTY	, "saux_consisitent_route is empty"		)	\
XX(604	, TCP_REQUEST_FAIL			, "tcp conn request fail"				)	\
XX(605	, TCP_WRITE_ZERO_BUFFER		, "tcp conn write zero buffer"			)	\
XX(701	, HTTP_NOT_SUPPORT_METHOD	, "not support http method"				)	\


typedef enum errorc_code {
#define  XX(num, name , string)ERR_##name=-(num),
	ERROR_C_CODE(XX)
#undef XX
} system_error;

LP_API const char* system_strerr(int code);
LP_API void system_add_err(int err, const char* str);
#define system_error_append(num, name, string )system_add_err(-(num), string);
LP_NS_END
#endif
