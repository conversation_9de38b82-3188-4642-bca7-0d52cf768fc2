#pragma once
#include "usr_worker.h"
#include <ref_ptr.h>

namespace dvo {
	struct sheet_event;
	class data_activity;
	class data_activitynode;
}

namespace tvo {
	class type_activity;
}

namespace activity {
	class worker_activity final : public dvo::usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_activity)
		void start() override;
		void stop() override;
		void update(const dvo::update_context*) override;
		dvo::data_activity* add(const tvo::type_activity*) const;
		void remove(dvo::data_activity*) const;
		void on_notifiction(const dvo::worker_notification*) override;
	private:
		void on_sheet_event(const dvo::sheet_event*);
		void on_load() const;
		bool _dirty{};
	};
}
