#pragma once
#ifndef ENUM_FLAGS_H
#define ENUM_FLAGS_H 
#include <type_traits>
#define fwd_enum_flag_operator(opt)	\
enum_flags operator opt(enum_flags	rhs) const {return enum_flags(_value opt rhs._value);}\
enum_flags operator opt(enum_t		rhs) const {return enum_flags(_value opt static_cast<base_t>(rhs));}\
enum_flags operator opt(base_t		rhs) const {return enum_flags(_value opt rhs);}

#define self_enum_flag_operator(opt) \
enum_flags& operator opt(enum_flags	rhs) {_value opt rhs._value; return * this;}\
enum_flags& operator opt(enum_t		rhs) {_value opt static_cast<base_t>(rhs); return *this;}\
enum_flags& operator opt(base_t		rhs) {_value opt rhs;return *this;}

#define self_enum_flag_compare(opt)	\
bool operator opt(enum_flags	rhs) const {return _value opt rhs._value;}\
bool operator opt(enum_t		rhs) const {return _value opt static_cast<base_t>(rhs);}\
bool operator opt(base_t		rhs) const{return _value opt rhs;}

namespace sys {
	template <typename EnumType, typename BaseType = int>
	struct enum_flags {
		static_assert( std::is_integral_v<BaseType>, "base type should be interger");
		using enum_t = EnumType;
		using base_t = BaseType;
		enum_flags(enum_flags&&) noexcept = default;
		~enum_flags() = default;
		enum_flags& operator =(const enum_flags&) = default;
		enum_flags& operator =(enum_flags&&) noexcept = default;

		enum_flags() : _value(0) {
		}

		enum_flags(const enum_flags& rhs) {
			_value = rhs._value;
		}

		enum_flags(const base_t v) {
			_value = v;
		}

		enum_flags(const enum_t& v) {
			_value = static_cast<base_t>(v);
		}

		operator base_t() const {
			return _value;
		}

		operator enum_t() const {
			return static_cast<enum_t>(_value);
		}

		enum_flags operator ~() const {
			return enum_flags(~_value);
		}

		self_enum_flag_operator(&=)
		self_enum_flag_operator(|=)
		fwd_enum_flag_operator(&)
		fwd_enum_flag_operator(|)
		fwd_enum_flag_operator(>>)
		fwd_enum_flag_operator(<<)
		self_enum_flag_compare(==)
		self_enum_flag_compare(!=)
		self_enum_flag_compare(>)
		self_enum_flag_compare(>=)
		self_enum_flag_compare(<)
		self_enum_flag_compare(<=)
	private:
		base_t _value;
	};
}

template <typename EnumType, typename BaseType>
bool operator==(BaseType v, sys::enum_flags<EnumType, BaseType> r) noexcept {
	return r == v;
}

template <typename EnumType, typename BaseType>
bool operator!=(BaseType v, sys::enum_flags<EnumType, BaseType> r) noexcept {
	return r != v;
}

template <typename EnumType, typename BaseType>
bool operator>=(BaseType v, sys::enum_flags<EnumType, BaseType> r) noexcept {
	using ufg = sys::enum_flags<EnumType, BaseType>;
	return ufg(v) >= r;
}

template <typename EnumType, typename BaseType>
bool operator>(BaseType v, sys::enum_flags<EnumType, BaseType> r) noexcept {
	using ufg = sys::enum_flags<EnumType, BaseType>;
	return ufg(v) > r;
}

template <typename EnumType, typename BaseType>
bool operator<(BaseType v, sys::enum_flags<EnumType, BaseType> r) noexcept {
	using ufg = sys::enum_flags<EnumType, BaseType>;
	return ufg(v) < r;
}

template <typename EnumType, typename BaseType>
bool operator<=(BaseType v, sys::enum_flags<EnumType, BaseType> r) noexcept {
	using ufg = sys::enum_flags<EnumType, BaseType>;
	return ufg(v) <= r;
}

#define SYS_ENUM_FLAGS(enum_type, new_enum_type)\
using new_enum_type = sys::enum_flags<enum_type>;
#endif
