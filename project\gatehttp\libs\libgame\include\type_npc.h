#pragma once
#include <btree_list.h> 
#include <type_data.h>
#include "typedata_wrapper_libgame.h" 
#include "npc_tree.h"
#include <enumerators.h>

namespace npc {
	class npc_tree;
}

namespace tvo {
	class type_monster_type;
	class type_career;
	class type_npc_level;

	class type_npc final : public type_data {
	public: 

		enum loot_policy_t {
			All,
			Single,
		};
		 

		struct difficulty_t {
			friend class type_npc;
			int head{};
			type_npc* find(int lv);
			bool empty() const;
		private:
			std::vector<type_npc*> list_;
		};

		type_career* career_t() const;
		type_monster_type* monster_t() const;
		static void awake_all(const awake_context_t& ); 
		cpx::btree_list<npc::npc_tree> skills;
		cpx::btree_list<npc::npc_tree> buffs;
		std::shared_ptr<difficulty_t> vairant;
		std::vector<const type_npc_level*> levels;
	public:
		using race_t = ds::race_t;
		//enums decl! @see TYPE_NPC_WRAPPER() in typeclass_define.h
		TYPE_NPC_WRAPPER() 

	private: 
		type_career * _career{};
		type_monster_type* _monster{};
	};
}
