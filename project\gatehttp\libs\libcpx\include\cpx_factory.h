#pragma once
#include <string>

namespace cpx {
	class cpx_controller;
	class cpx_action;
	class cpx_condition;
	class cpx_state;

	class cpx_factory {
	public:
		typedef cpx_action* (*cpx_action_creator)(int);
		typedef cpx_condition* (*cpx_condition_creator)(int);
		typedef cpx_state* (*cpx_state_creator)(int);
		typedef const char* (*cpx_name)(int);
		static cpx_action* get_action(int);
		static cpx_condition* get_condition(int);
		static cpx_state* get_state(int);
		static const cpx_controller* find(const std::string& name);
		static int read_manifest(const char* file);
		static void add_ignore(std::string);
		static bool can_ignore(const std::string&);
		inline static cpx_action_creator create_action{};
		inline static cpx_condition_creator create_condition{};
		inline static cpx_state_creator create_state{};
		inline static cpx_name name_action{};
		inline static cpx_name name_condition{};
		inline static cpx_name name_state{};
	};
}
