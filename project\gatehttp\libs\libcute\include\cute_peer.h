#ifndef SSL_HANDLE_H
#define  SSL_HANDLE_H   
#include "web.h"
#include "cute_ssl.h"
LP_NS_BEGIN

typedef enum cute_module {
	CUTE_ERRM_UV,
	CUTE_ERRM_TLS,
	CUTE_ERRM_HTTP_PARSE
} cute_module_t;

typedef struct cute_peer_s cute_peer_t;
typedef void (*cpeer_connect_cb)(cute_peer_t*, int status);
typedef void (*cpeer_read_cb)(cute_peer_t*, ssize_t nrd, const uv_buf_t* dcrypted);
typedef void (*cpeer_getuvbuf_cb)(cute_peer_t*, uv_buf_t* buf);
typedef void (*cpeer_write_cb)(cute_peer_t*, int status);
typedef void (*cpeer_close_cb)(cute_peer_t*);
typedef void (*cpeer_async_cb)(cute_peer_t*);

typedef enum peer_role {
	CUTE_ROLE_PEER,
	CUTE_ROLE_HOST,
} peer_role_t;

typedef enum cute_tcp_family {
	CUTE_FA_TCP4,
	CUTE_FA_TCP6
} cute_tcp_family_t;

typedef enum cute_transport {
	CUTE_TRANS_TCP,
	CUTE_TRANS_PIPE,
} cute_transport_t;

typedef enum cute_side {
	CUTE_SIDE_SERVER,
	CUTE_SIDE_CLIENT
} cute_side_t;

struct cute_peer_s {
	uv_loop_t* loop;
	size_t id;
	peer_role_t role;
	cute_tcp_family_t family;
	cute_transport_t transport;
	cute_side_t side;

	union {
		uv_handle_t handle;
		uv_stream_t stream;
		uv_tcp_t tcp;
		uv_pipe_t pipe;
	};

	union {
		struct sockaddr* addr;
		struct sockaddr_in* tcp4;
		struct sockaddr_in6* tcp6;
	};

	int status;
	cssl_ctx_t ssl;
	uv_write_t w_req;
	uv_buf_t w_buf;
	uv_async_t async;
	int aflags;
	uv_mutex_t mutex;
	char buf_r[READ_BUFFER];
	uv_connect_t conn;
	cpeer_connect_cb on_connect;
	cpeer_read_cb on_read_cb;
	cpeer_write_cb on_write_cb;
	cpeer_getuvbuf_cb get_uvbuf;
	cpeer_close_cb on_close;
	cpeer_async_cb on_aysnc_cb;
	void* data;
};

LP_API int peer_init(cute_peer_t* h);
LP_API int peer_connect(cute_peer_t* h);
LP_API int peer_accept(cute_peer_t* h, uv_stream_t* serv);
LP_API int peer_write(cute_peer_t* h);
LP_API int peer_close(cute_peer_t* h);
LP_API int peer_async(cute_peer_t*);
LP_API void peer_free(cute_peer_t* h);
LP_NS_END
#endif
