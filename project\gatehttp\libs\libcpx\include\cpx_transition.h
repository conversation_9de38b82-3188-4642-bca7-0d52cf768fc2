#pragma once
#include <libcutex.h>
#include "cpx_core.h"
#include <json_macro.h>
#include <set>
#include "time_vo.h"
#include <sys_exception.h>
#include "cpx_cast.h"
#include "cpx_interface.h"

namespace cpx {
	class cpx_machine;
	class cpx_controller;
	class cpx_state;
	class cpx_condition;
	class cpx_transition;
	class rpx_controller;
	class rpx_machine;
	class rpx_state;
	class cpx_entity;

	struct cpx_transition_context {
		const cpx_transition* transition{};
		cpx_entity* entity{};
		rpx_controller* controller{};
		rpx_machine* machine{};
		rpx_state* state{};
		time_vo now{};

		template <typename T, std::enable_if_t<std::is_base_of_v<cpx_entity, T>> * = nullptr>
		T& host() const {
			if (!entity) {
				throw_e("entity is nil");
			}
			auto* v = assert_entity<T>(entity);
			if (!v) {
				throw_e("fetch %s fail", typeid(T).name());
			}
			return *v;
		}
	};

	class cpx_transition final : public cpx_core {
	public:
		DELETE_MOVE_COPY(cpx_transition)
		cpx_transition();
		~cpx_transition() override;
		void read(const dynamic_vo& vo, const std::string& file, cpx_machine*);

		union {
			int64_t source_id;
			const cpx_state* source;
		};

		union {
			int64_t destiny_id;
			const cpx_state* destiny;
		};

		const cpx_controller* controller = nullptr;

		enum select_mode_t {
			KOr,
			kAnd,
		};

		enum interupt_mode_t {
			kByPriority,
			kByAny,
			kNot,
		};

		enum check_mode_t {
			kEnter = 1 << 0,
			kRun = 1 << 1,
			kEnd = 1 << 2
		};

		select_mode_t selectMode{};
		check_mode_t checkMode{};
		bool useDefaultParameter{};
		bool canTransferToSelf{};
		bool onewayLock{};
		int priority{};
		bool force{};

	private:
		std::vector<const cpx_condition*> _conditions;
		JSON_TYPE_INLINE(cpx_transition,
		                 selectMode,
		                 checkMode,
		                 useDefaultParameter,
		                 canTransferToSelf,
		                 onewayLock,
		                 priority,
		                 force)

	public:
		bool is_valid(const cpx_transition_context* ctx) const;
		void mark_checked(int64_t);

	private:
		bool check_or(const cpx_transition_context* ctx) const;
		bool check_and(const cpx_transition_context* ctx) const;
		bool is_all_parameter_default(const cpx_transition_context* ctx) const;
		bool has_parameter_is_default(const cpx_transition_context* ctx) const;
		bool checked(int64_t p) const;
		std::set<int64_t> _checked;
	};
}
