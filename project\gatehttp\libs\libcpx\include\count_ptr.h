#ifndef CUTE_COUNT_PTR_H
#define CUTE_COUNT_PTR_H
#pragma once
#include <cstddef>
#include "sys_exception.h"
#include "cpx_interface.h"
#include "cpx_cast.h"

namespace cpx {
	template <typename T>
	class count_ptr {
	public:
		using self_type = count_ptr<T>;
		using element_type = T;

		constexpr count_ptr() noexcept : px(nullptr) {
		}

		count_ptr(T* p) : px(p) {
			if (px) {
				px->add_ref();
			}
		}

		template <typename U>
		count_ptr(count_ptr<U> const& rhs, std::enable_if_t<std::is_convertible_v<U*, T*>>* = nullptr) : px(rhs.get()) {
			if (px) {
				px->add_ref();
			}
		}

		count_ptr(self_type const& rhs) : px(rhs.px) {
			if (px) {
				px->add_ref();
			}
		}

		~count_ptr() {
			if (px) {
				px->reduce_ref();
			}
			px = nullptr;
		}

		template <typename U>
		friend class count_ptr;

		template <typename U>
		count_ptr& operator=(count_ptr<U> const& rhs) {
			self_type(rhs).swap(*this);
			return *this;
		}

		// Move support
		count_ptr(self_type&& rhs) noexcept : px(rhs.px) {
			rhs.px = nullptr;
		}

		count_ptr& operator=(self_type&& rhs) noexcept {
			self_type(static_cast<self_type&&>(rhs)).swap(*this);
			return *this;
		}

		template <typename U>
		count_ptr(count_ptr<U>&& rhs, std::enable_if_t<std::is_convertible_v<U*, T*>>* = nullptr) noexcept
			: px(rhs.px) {
			rhs.px = nullptr;
		}

		count_ptr& operator=(const count_ptr& rhs) {
			if (this != &rhs) {
				self_type(rhs).swap(*this);
			}
			return *this;
		}

		element_type* get() const noexcept { return px; }

		template <typename U, std::enable_if_t<
			          std::is_base_of_v<T, U> &&
			          std::is_base_of_v<cpx_entity, U>>* = nullptr>
		U* get() const noexcept {
			return px ? assert_entity<U>(px) : nullptr;
		}

		template <typename U, std::enable_if_t<
			          std::is_base_of_v<T, U> &&
			          ! std::is_base_of_v<cpx_entity, U>>* = nullptr>
		U* get() const noexcept {
			return px ? assert_cast<U>(px) : nullptr;
		}

		element_type& operator*() const {
			if (!px) {
				throw_e("using null_ptr");
			}
			return *px;
		}

		element_type* operator->() const {
			if (!px) {
				throw_e("using null_ptr");
			}
			return px;
		}

		// implicit conversion to "bool"
		operator bool() const noexcept { return px != nullptr; }
		// operator! is redundant, but some compilers need it
		bool operator!() const noexcept { return px == nullptr; }

		void swap(count_ptr& rhs) noexcept {
			element_type* tmp = px;
			px = rhs.px;
			rhs.px = tmp;
		}

	private:
		element_type* px;
	};
}

namespace cpx {
	template <typename T, typename U>
	bool operator==(count_ptr<T> const& a, count_ptr<U> const& b) noexcept {
		return a.get() == b.get();
	}

	template <typename T, typename U>
	bool operator==(count_ptr<T> const& a, U* b) noexcept {
		return a.get() == b;
	}

	template <typename T, typename U>
	bool operator==(T* a, count_ptr<U> const& b) noexcept {
		return a == b.get();
	}
#ifdef __cpp_impl_three_way_comparison
template <typename T, typename U>
inline std::strong_ordering operator<=>(count_ptr<T> const &a, count_ptr<U> const &b) noexcept {
  return a.get() <=> b.get();
}

template <typename T, typename U>
inline std::strong_ordering operator<=>(count_ptr<T> const &a, U *b) noexcept {
  return a.get() <=> b;
}

template <typename T, typename U>
inline std::strong_ordering operator<=>(T *a, count_ptr<U> const &b) noexcept {
  return a <=> b.get();
}
#else
	template <typename T, typename U>
	bool operator!=(count_ptr<T> const& a, count_ptr<U> const& b) noexcept {
		return a.get() != b.get();
	}

	template <typename T, typename U>
	bool operator!=(count_ptr<T> const& a, U* b) noexcept {
		return a.get() != b;
	}

	template <typename T, typename U>
	bool operator!=(T* a, count_ptr<U> const& b) noexcept {
		return a != b.get();
	}

	template <typename T, typename U>
	bool operator<(count_ptr<T> const& a, count_ptr<U> const& b) noexcept {
		return a.get() < b.get();
	}

	template <typename T, typename U>
	bool operator<(count_ptr<T> const& a, U* b) noexcept {
		return a.get() < b;
	}

	template <typename T, typename U>
	bool operator<(T* a, count_ptr<U> const& b) noexcept {
		return a < b.get();
	}

	template <typename T, typename U>
	bool operator<=(count_ptr<T> const& a, count_ptr<U> const& b) noexcept {
		return a.get() <= b.get();
	}

	template <typename T, typename U>
	bool operator<=(count_ptr<T> const& a, U* b) noexcept {
		return a.get() <= b;
	}

	template <typename T, typename U>
	bool operator<=(T* a, count_ptr<U> const& b) noexcept {
		return a <= b.get();
	}

	template <typename T, typename U>
	bool operator>(count_ptr<T> const& a, count_ptr<U> const& b) noexcept {
		return a.get() > b.get();
	}

	template <typename T, typename U>
	bool operator>(count_ptr<T> const& a, U* b) noexcept {
		return a.get() > b;
	}

	template <typename T, typename U>
	bool operator>(T* a, count_ptr<U> const& b) noexcept {
		return a > b.get();
	}

	template <typename T, typename U>
	bool operator>=(count_ptr<T> const& a, count_ptr<U> const& b) noexcept {
		return a.get() >= b.get();
	}

	template <typename T, typename U>
	bool operator>=(count_ptr<T> const& a, U* b) noexcept {
		return a.get() >= b;
	}

	template <typename T, typename U>
	bool operator>=(T* a, count_ptr<U> const& b) noexcept {
		return a >= b.get();
	}
#endif
	template <typename T>
	bool operator==(count_ptr<T> const& p, std::nullptr_t) noexcept {
		return p.get() == nullptr;
	}

	template <typename T>
	bool operator==(std::nullptr_t, count_ptr<T> const& p) noexcept {
		return p.get() == nullptr;
	}
#ifdef __cpp_impl_three_way_comparison
template <typename T>
inline std::strong_ordering operator<=>(count_ptr<T> const &p, std::nullptr_t) noexcept {
  return p.get() <=> nullptr;
}

template <typename T>
inline std::strong_ordering operator<=>(std::nullptr_t, count_ptr<T> const &p) noexcept {
  return p.get() <=> nullptr;
}
#else
	template <typename T>
	bool operator!=(count_ptr<T> const& p, std::nullptr_t) noexcept {
		return p.get() != nullptr;
	}

	template <typename T>
	bool operator!=(std::nullptr_t, count_ptr<T> const& p) noexcept {
		return p.get() != nullptr;
	}

	template <typename T>
	bool operator<(count_ptr<T> const& p, std::nullptr_t) noexcept {
		return p.get() < nullptr;
	}

	template <typename T>
	bool operator<(std::nullptr_t, count_ptr<T> const& p) noexcept {
		return p.get() < nullptr;
	}

	template <typename T>
	bool operator<=(count_ptr<T> const& p, std::nullptr_t) noexcept {
		return p.get() <= nullptr;
	}

	template <typename T>
	bool operator<=(std::nullptr_t, count_ptr<T> const& p) noexcept {
		return p.get() <= nullptr;
	}

	template <typename T>
	bool operator>(count_ptr<T> const& p, std::nullptr_t) noexcept {
		return p.get() > nullptr;
	}

	template <typename T>
	bool operator>(std::nullptr_t, count_ptr<T> const& p) noexcept {
		return p.get() > nullptr;
	}

	template <typename T>
	bool operator>=(count_ptr<T> const& p, std::nullptr_t) noexcept {
		return p.get() >= nullptr;
	}

	template <typename T>
	bool operator>=(std::nullptr_t, count_ptr<T> const& p) noexcept {
		return p.get() >= nullptr;
	}
#endif
	template <typename T>
	void swap(count_ptr<T>& lhs, count_ptr<T>& rhs) noexcept {
		lhs.swap(rhs);
	}
}
#endif
