#pragma once
#include "net_server.h"

namespace net {
	struct web_parser;
	struct web_message;
	class web_session;

	class web_server final : public net_server {
	public:
		struct message_t {
			ref_ptr<web_session> session{};
			web_message* msg{};
			web_parser* parser{};
		};

		typedef int (*web_server_callback)(const web_server&, const message_t&);
		friend class web_session;
		DELETE_MOVE_COPY(web_server)
		explicit web_server(uv_loop_t*);
		web_server_callback cb{};
		void* data{};

	protected:
		int try_accept(uv_stream_t*, net_peer_id) override;
		int notify(const message_t&) const;

	private:
		~web_server() override;
	};
}
