#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"

namespace tvo {
	class type_mark_code final : public type_data {
	public:
		enum mark_code_t {
			kmissionSubmit = 20001,
			klevelUpSubmit = 20002,
		};
		DELETE_MOVE_COPY(type_mark_code)
		type_mark_code();
		~type_mark_code() override;
		static void awake_all(const awake_context_t&);
		static const type_mark_code* find_config(const std::string& platform, int code);
		TYPE_MARK_CODE_WRAPPER()
	};
}
