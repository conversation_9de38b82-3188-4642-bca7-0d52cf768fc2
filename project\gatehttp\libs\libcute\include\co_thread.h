#pragma once
#ifndef CO_THREAD_H
#define CO_THREAD_H 
#include <chrono>
#include <libcopp/coroutine/coroutine_context_container.h>
#include <libcopp/coroutine/coroutine_context.h>
#include <libcopp/stack/stack_allocator.h>
#include  "logger.h"
#include <co_context.h>

using co_thread = co::co_context;
#define CO_THREAD_INTERNAL_CALL_(func)											\
do {\
auto __pctx__ = this_ptr_t();													\
callback_t bin = [=](void*) {													\
	const auto  __lpctx__ = __pctx__;											\
	const auto ctx = this_ptr_t();												\
	try {(func)((args)...); return 1;}											\
	catch (std::exception& e) {	log_error("co:start error :%s", e.what());	}	\
	catch (...) {log_error("co:start <unknown error> ");					}	\
	return 1;										 						 	\
};																				\
return co_thread::create(std::forward<callback_t>(bin), 0)->start(nullptr);		\
}while(0)

namespace co {
	//public:
	using callback_t = copp::coroutine_context::callback_t;
	co_thread* this_thread();
	co_thread::ptr_t this_ptr_t();
	/*lambda */
	template <typename F, typename ...Args,
	          std::enable_if_t<std::is_invocable_v<F, Args...>>* = nullptr>
	int start(F fn, Args ... args) {
		CO_THREAD_INTERNAL_CALL_(fn);
	}

	/*class member func ptr*/
	template <typename TInst, typename R, typename Ty, typename ...Args>
	int start(TInst* inst, R (Ty::*func)(Args ...), const Args& ... args) {
		CO_THREAD_INTERNAL_CALL_(inst->*func);
	}

	/*class member func ptr*/
	template <typename TInst, typename R, typename Ty, typename ...Args>
	int start(TInst* inst, R (Ty::*func)(const Args& ...), const Args& ... args) {
		CO_THREAD_INTERNAL_CALL_(inst->*func);
	}

	/*class member func ptr*/
	template <typename TInst, typename R, typename Ty, typename ...Args>
	int start(TInst* inst, R (Ty::*func)(const Args& ...) const, const Args& ... args) {
		CO_THREAD_INTERNAL_CALL_(inst->*func);
	}

	void pend_(int64_t time);
	/// sleep_for
	template <typename Rep, typename Period>
	void sleep_for(const std::chrono::duration<Rep, Period>& rtime) {
		if (rtime <= rtime.zero()) return;
		const int64_t millitime = std::chrono::duration_cast<std::chrono::milliseconds>(rtime).count();
		pend_(millitime);
	}
};
#endif
