#pragma once
#include <fp_math.hpp>
#include <json_macro.h>
#include <vector2.h>
#include <vector3.h>
#include <cpx_struct.h>

namespace nav {
	inline void to_json(json_t& ja, const float_t& jb) {
		ja = static_cast<float>(jb);
	}

	inline void from_json(const json_t& ja, float_t& jb) {
		using value_t = nlohmann::detail::value_t;
		const auto t = ja.type();
		switch (t) {
			case value_t::null:
			case value_t::object:
			case value_t::array:
			case value_t::string:
			case value_t::binary:
			case value_t::discarded:
				break;
			case value_t::boolean:
				jb = ja ? static_cast<float_t>(1) : static_cast<float_t>(0);
				break;
			case value_t::number_integer: {
				const int64_t v = ja;
				jb = static_cast<float_t>(v);
			}
			break;
			case value_t::number_unsigned: {
				const uint64_t v = ja;
				jb = static_cast<float_t>(v);
				break;
			}
			case value_t::number_float: {
				const double v = ja;
				jb = static_cast<float_t>(v);
				break;
			}
		}
	}

	JSON_TYPE_INLINE(vector2, x, y)
	JSON_TYPE_INLINE(vector3, x, y, z)
}

namespace tvo {
	using vector2 = nav::vector2;
	using vector3 = nav::vector3;
	using float_t = nav::float_t;

	struct rect {
		float_t x;
		float_t y;
		float_t width;
		float_t height;
		JSON_TYPE_INCRUSIVE(rect, x, y, width, height)

		bool contains(const nav::vector2 v) const {
			return v.x > x &&
				v.x < x + width &&
				v.y > y &&
				v.y < y + height;
		}
	};
}

namespace ds {
	using float_t = nav::float_t;
	using double_t = nav::double_t;
	using int32_t = int32_t;
}
#if FP_FIX_MATH_FLOAT

#include <ecs_traits.h>
namespace ecs {
	template <>
	struct is_floating_point<ds::float_t>
		: std::true_type {
	};
}

#endif
