#pragma once
#include <cpx_action.h>
#include "cpx_action_builtin_wrapper.h" 

namespace cpx {
	class cpx_act_rewind_parameter final : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_act_rewind_parameter)
		CPXA_CPX_ACT_REWIND_PARAMETER_WRAPPER()
		cpx_act_rewind_parameter();
		~cpx_act_rewind_parameter() override;
	protected: 
		void on_start(cpx_input*, cpx_output*) const override;
		void on_stop(cpx_input*, cpx_output*) const override;
		bt::btree_status on_update(cpx_input*, cpx_output*) const override;
	};
}
