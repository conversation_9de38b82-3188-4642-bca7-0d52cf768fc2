#pragma once
#include "libcute.h" 
#include <pb_base.h>
#include "service_common_code_wrapper.h"

class hash128 final : public pb_base {
public:
	DEFAULT_MOVE_COPY(hash128)
	hash128();
	~hash128() override;
	explicit hash128(const std::string&);
	explicit hash128(uint64_t);
	explicit operator std::string() const;
	explicit operator uint64_t() const;
	bool operator ==(const hash128&) const;
	bool operator !=(const hash128&) const;
	bool operator >(const hash128&) const;
	bool operator >=(const hash128&) const;
	bool operator <(const hash128&) const;
	bool operator <=(const hash128&) const;
	bool empty() const;
	hash128(const ecs::object_t&);
	operator ecs::object_t() const;
	PB_WRAPPER_hash128()
};

#include <functional> // hash
namespace std {
	template <>
	struct hash<hash128> {
		size_t operator()(const hash128& v) const noexcept;
	};
}
