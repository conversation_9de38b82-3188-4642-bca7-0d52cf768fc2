#pragma once
#include "player_info.h"
#include <service_to_gatetcp_code_wrapper.h>
#include <set>
class pb_base;

namespace dvo {
	class usr_info final : public player_info {
	public:
		friend player_vo;
		DELETE_MOVE_COPY(usr_info)
		ref_ptr<data_character> character() const;
		usr_info();
		~usr_info() override;
		int uztype() const override;
		int send(int, pb_slice);
		int send(const pb_base&);
		int send(data& d, const ecs::encode_context& = {ecs::PB_ENCODE_FULL, ecs::VAR_NONE});
		int append(pb_slice);
		bool module_open(int);
		void recv(t2s_gate_to_game&&);
		int fast_save(pb_buffer*);
		size_t cnt_sdata() const;

	protected:
		void on_start() override;
		void on_stop() override;
		void on_update(const update_context*) override;
		void on_data_dirty(const ref_ptr<data>&) override;

	private:
		void handle_cmd(const cpx::time_vo&) override;
		std::mutex _msg_lock;
		std::vector<t2s_gate_to_game> _messages{};
		std::set<ref_ptr<data>> _uzone_vdata{};

	public:
		using server_handler = void(*)(const servcmd::usr_context*);
		static void set_handler(server_handler);
		static void add_insenstive_data(int);
	};
}
