#ifndef CUTE_ENCRYPTION_H
#define CUTE_ENCRYPTION_H  
#include "libcute.h"
#include "pb.h"
LP_NS_BEGIN

typedef enum ase_key_bit_t {
	BYTES_16 = 128,
	BYTES_24 = 192,
	BYTES_32 = 256,
} ase_key_bit;

typedef struct aes_context_t {
	unsigned char keys[32];
	unsigned char expends[60][4];
	int cnt_round; // count encrupty round;
	int cnt_keys; // count keys per round; 
#if DEBUG
	int debug;
#endif
} aes_context;

LP_API int aes_encrupt(const aes_context*, pb_slice, pb_buffer*);
LP_API int aes_decrupt(const aes_context*, pb_slice, pb_buffer*);
LP_API int aes_init(aes_context*, const char* key, ase_key_bit bits);
/* 
*/
LP_API int base64_encode(const pb_slice* in, pb_buffer* buf);
LP_API int base64_decode(const pb_slice* in, pb_buffer* buf);
/*

*/
LP_API int md5_encode(pb_slice, pb_buffer*);
/*


*/
typedef struct sh1_context_t {
	uint32_t state[5];
	uint32_t count[2];
	unsigned char buffer[64];
} sh1_context;

LP_API void cute_sha1_init_context(sh1_context*);
LP_API void cute_sha1_update(sh1_context*, const unsigned char*, uint32_t);
LP_API void cute_sha1_final(unsigned char digest[20], sh1_context* context);
LP_API int cute_sha1(const char*, size_t, unsigned char digest[20]);
LP_API void cute_sha1_hex(const unsigned char* in, uint32_t in_size, char* out, uint32_t out_size);
 
/*


*/
LP_NS_END
#endif
