#pragma once
#include  <libcute.h>
#include <ref_ptr.h>
#include "usr_worker.h"
#include <data_level.h>

namespace tvo {
	class type_hero_level;
	struct level_queue_t;
	struct level_group_t;
	class type_currency;
}

namespace dvo {
	class data_level;
	struct property_event;
	class data_currency;
}

namespace dvo {
	interface iusr_level_limiter {
	public:
		DELETE_MOVE_COPY(iusr_level_limiter)
		iusr_level_limiter();
		virtual ~iusr_level_limiter();
		virtual void limite(const tvo::type_hero_level&, int& lv) = 0;
	};

	class worker_level final : public usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_level)
		void start() override;
		void stop() override;
		int get_level(int method);
		int get_level(const std::string& pname);
		ref_ptr<data_level> get_data(int method);

		const ref_ptr<data_level>& main()  ;

	protected:
		void on_notifiction(const worker_notification*) override;

	private:
		void on_load();

		struct runner_t {
			DELETE_MOVE_COPY(runner_t)
			runner_t();
			~runner_t();
			void on_currency_change(const property_event* evt);
			void caculate() const;
			tvo::level_queue_t* queue;
			usr_info* usr;
			ref_ptr<data_level> lvd;
			ref_ptr<data_currency> currency;
		};

		sys::hashmapx<int, ref_ptr<data_level>> _levels{};
		std::vector<runner_t*> _runners;
		ref_ptr<data_level> _main{};
	};
}
