#pragma once
#include <libcute.h>
#include <ref_ptr.h>
#include "dvo_worker.h"

namespace dvo {
	class data_character;

	class usr_worker : public worker_t {
	public:
		enum phase_t {
			kOnDataLoad,
			kOnEnterCluster,
			kOnEnterScene,
			kOnExitScene,
			kOnExitCluster,
			kOnLogout,
			kOnSave, 
			kOnDynamicTvoRotate,
#if DEBUG  
			kOnDebugTimeChange
#endif 
		};

		DELETE_MOVE_COPY(usr_worker)
		~usr_worker() override;
		usr_info& usr() const;
		void update(const update_context*) override;

	protected:
		void awake() final;
		explicit usr_worker();
		database& dbase() const;
		ref_ptr<data_character> character() const;
		int mirror() const;
		bool module_open(int) const;
		void mark_load_dirty() const;

	private:
		usr_info* _usr;
	};
}
