#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "property_group.h"
#include "thing_group.h"
#include "dlink_make.h"

namespace tvo {
	class type_buff;

	class type_skill_positive final : public type_data {
	public:
		static void awake_all(const awake_context_t&);
		void awake(const awake_context_t&) override;
		UPLEVEL_LINK_FIELDS(type_skill_positive)
		const type_buff* buff() const;

	private:
		const type_buff* _buff{};

	public:
		TYPE_SKILL_POSITIVE_WRAPPER()
	};
}
