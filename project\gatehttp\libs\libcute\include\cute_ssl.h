#ifndef CUTE_SSL_H
#define CUTE_SSL_H  
#include "libcute.h"
#if !defined(WITH_OPENSSL) && !defined(WITH_GNUTLS) && !defined(WITH_MBEDTLS)
#ifdef WIN32
#define SSL_WITH_MBEDTLS
#elif __GNUC__
#define SSL_WITH_MBEDTLS 
#elif defined(OS_DARWIN)
#define SSL_WITH_APPLETLS
#else
#define SSL_WITHOUT_SSL
#endif
#endif
struct cute_peer_s;
LP_NS_BEGIN
typedef void* cssl_ctx_t;

typedef enum {
	CSSL_CLIENT = 0,
	CSSL_SERVER = 1,
} cssl_endpoint;

enum {
	CSSL_OK = 0,
	CSSL_ERROR = -1,
	CSSL_WANT_READ = -2,
	CSSL_WANT_WRITE = -3,
	CSSL_WOULD_BLOCK = -4,
};

typedef struct {
	const char* crt_file;
	const char* key_file;
	const char* ca_file;
	const char* ca_path;
	short verify_peer;
	cssl_endpoint endpoint;
} cssl_ctx_opt_t;

LP_API const char* cssl_backend(void);
LP_API cssl_ctx_t cssl_new(cssl_ctx_opt_t*);
LP_API void cssl_free(cssl_ctx_t);
LP_API int cssl_read(struct cute_peer_s*, ssize_t, const uv_buf_t*);
LP_API int cssl_write(const struct cute_peer_s*, uv_buf_t**);
LP_API int cssl_handshake(struct cute_peer_s*);
LP_API int cssl_close(const struct cute_peer_s*);
LP_API int cssl_set_sni_hostname(cssl_ctx_t ssl, const char* hostname);
LP_NS_END
#endif
