#pragma once
#include <treecore.h>

namespace cpx {
	struct btree_make_context_t {
		const char* class_n{nullptr};
		const char* name{nullptr};
		int id{0};
	};

	template <typename TNode>
	class btree_list {
		static_assert(std::is_base_of_v<bt::treecore<TNode>, TNode>, "node must be typeof treecore");

	public:
		DELETE_MOVE_COPY(btree_list)
		using node_type = TNode;
		using list_type = std::vector<node_type*>;
		using list_iter = typename list_type::iterator;
		using const_iter = typename list_type::const_iterator;
		typedef node_type* (*create_node)();
		static std::unordered_map<std::string, create_node> g_factory;

		static void reg_creator(const std::string& cmd, const create_node c) {
			g_factory.emplace(cmd, c);
		}

		list_type nodes{};
		int treehash{};
		size_t max_nodes{};
		btree_list() = default;

		~btree_list() {
			for (const auto* n : nodes) {
				delete n;
			}
			nodes.clear();
		}

		void make(const btree_make_context_t* ctx, dynamic_vo&& x) {
			auto vo = std::move(x);
			const auto vlist = vo["treeData"];
			const auto vhash = vo["treeHash"];
			if (vhash.is_number()) {
				vhash.get_to(treehash);
			}
			if (vlist.is_array()) {
				const size_t size = vlist.size();
				nodes.reserve(size);
				for (size_t i = 0; i < size; ++i) {
					node_type* v = make_child(ctx, vlist[i], max_nodes);
					if (v) {
						nodes.emplace_back(v);
					}
				}
			}
		}

		list_iter begin() noexcept { return nodes.begin(); }
		list_iter end() noexcept { return nodes.end(); }
		const_iter begin() const noexcept { return nodes.begin(); }
		const_iter end() const noexcept { return nodes.end(); }
		const_iter cbgin() const noexcept { return nodes.cbegin(); }
		const_iter cend() const noexcept { return nodes.cend(); }
		size_t size() const noexcept { return nodes.size(); }
		bool empty() const noexcept { return nodes.empty(); }

		template <typename Fn, typename... Args,
		          std::enable_if_t<std::is_invocable_v<Fn, node_type*, Args...>>* = nullptr>
		void foreach(Fn call, Args&&... args) {
			for (auto* c : nodes) {
				dfs(call, c, std::forward<Args>(args)...);
			}
		}

		template <typename Fn, typename... Args,
		          std::enable_if_t<std::is_invocable_v<Fn, const node_type*, Args...>>* = nullptr>
		void foreach(Fn call, Args&&... args) const {
			for (const auto* c : nodes) {
				dfs(call, c, std::forward<Args>(args)...);
			}
		}

		template <typename U>
		U* get() const {
			for (auto* v : nodes) {
				auto* u = sys::type_cast<U>(v);
				if (u) {
					return u;
				}
			}
			return nullptr;
		}

		template <typename... Args>
		void awake(Args&&... args) {
			for (auto* n : nodes) {
				awake(n, std::forward<Args>(args)...);
			}
		}

	private:
		template <typename Fn,
		          typename... Args,
		          std::enable_if_t<std::is_invocable_v<Fn, node_type*, Args...>>* = nullptr>
		static void dfs(Fn call, node_type* node, Args&&... args) {
			call(node, std::forward<Args>(args)...);
			for (size_t i = 0; i < node->size(); ++i) {
				auto* c = node->child_at(i);
				dfs(call, c, std::forward<Args>(args)...);
			}
		}

		template <typename Fn,
		          typename... Args,
		          std::enable_if_t<std::is_invocable_v<Fn, node_type*, Args...>>* = nullptr>
		static void dfs(Fn call, const node_type* node, Args&&... args) {
			call(node, std::forward<Args>(args)...);
			for (size_t i = 0; i < node->size(); ++i) {
				const auto* c = node->child_at(i);
				dfs(call, c, std::forward<Args>(args)...);
			}
		}

		template <typename... Args>
		static void awake(node_type* n, Args&&... args) {
			n->awake(std::forward<Args>(args)...);
			for (size_t i = 0; i < n->size(); ++i) {
				awake(n->child_at(i), std::forward<Args>(args)...);
			}
		}

		node_type* make_child(const btree_make_context_t* ctx,
		                      const dynamic_vo& vo,
		                      size_t& deep) {
			std::string cmd = vo.at("cmd");
			auto it = g_factory.find(cmd);
			if (it == g_factory.end()) {
				log_error("%s [%d: %s] cell [%s] not register",
				          ctx->class_n,
				          ctx->id,
				          ctx->name,
				          cmd.c_str());
				return nullptr;
			}
			auto* n = it->second();
			n->parse(vo);
			n->idx = vo["idx"];
			if (vo.contains("children")) {
				const auto children = vo["children"];
				if (children.is_array()) {
					const size_t size = children.size();
					n->reserve(size);
					for (size_t i = 0; i < size; ++i) {
						node_type* c = make_child(ctx, children[i], deep);
						if (c) {
							n->add(c);
						}
					}
				}
			}
			return n;
		}
	};

	template <typename TNode>
	std::unordered_map<std::string, typename btree_list<TNode>::create_node>
	btree_list<TNode>::g_factory{};
}
