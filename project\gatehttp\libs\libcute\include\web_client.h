#pragma once
#include "web_peer.h"

namespace net {
	struct web_stream_reader;
	struct web_parser11;

	class web_client final : public web_peer {
	public:
		friend struct web_client_request;
		friend struct web_client_response;
		DELETE_MOVE_COPY(web_client)
		~web_client() override;

	protected:
		void on_self_close() override;

	private:
		explicit web_client(uv_loop_t*);
		bool used() const;
		void recv(pb_slice);
		void release() const;
		web_stream_reader& stream_r() const;
		static ref_ptr<web_client> get(const char*);
	};
}
