#pragma once
#include <cpx_action.h>

namespace cpx {
	class cpx_action_ignore final : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_action_ignore)
		void parse(const dynamic_vo& jo) override;
		int class_id() const noexcept override;
		const char* class_n()  const noexcept override;
		cpx_action_ignore();
		~cpx_action_ignore() override; 

	protected:
		void on_start(cpx_input*, cpx_output*) const override;
		void on_stop(cpx_input*, cpx_output*) const override;
		bt::btree_status on_update(cpx_input*, cpx_output*) const override;
	};
};
