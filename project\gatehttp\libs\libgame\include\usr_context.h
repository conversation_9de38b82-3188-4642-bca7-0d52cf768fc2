#pragma once
#include <time_vo.h>
#include "service_to_gatetcp_code_wrapper.h"

namespace dvo {
	class usr_info;
}

namespace servcmd {
	struct usr_context {
		dvo::usr_info* usr{nullptr};
		t2s_gate_to_game source;
		cpx::time_vo now;

		template <typename T, std::enable_if_t<std::is_base_of_v<pb_base, T>>* = nullptr>
		T obj() const {
			T v;
			if (v.type() != source.code) {
				throw_e("wrong code");
			}
			int err;
			const pb_slice s = source.data;
			pbd_slice ds{s, s.ptr};
			if ((err = v.decode(&ds))) {
				throw_e("decode fail");
			}
			return v;
		}
	};
}
