#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "render_setting.h"

namespace tvo {
	class type_body_position final : public type_data {
	public:
		DELETE_MOVE_COPY(type_body_position)
		type_body_position();
		static void awake_all(const awake_context_t&);
		~type_body_position() override;
		using display_t = ds::render_display;
		TYPE_BODY_POSITION_WRAPPER()
	};
}
