#pragma once
#include "web_message.h"

namespace net {
	struct web_parser;
	class web_session;

	struct web_session_request {
		friend struct web_worker;
		friend web_session_response;
		DELETE_MOVE_COPY(web_session_request)
		explicit web_session_request(const ref_ptr<web_session>&);
		~web_session_request();
		bool done() const;
		int error() const;
		int read(pb_buffer&) const;
		const web_message& msg() const;

	private:
		ref_ptr<web_session> _conn{};
	};

	struct web_session_response {
		friend struct web_session_request;
		DELETE_MOVE_COPY(web_session_response)
		web_session_response(const ref_ptr<web_session>&, const web_session_request&);
		~web_session_response();
		int write(const char*, size_t);
		int write(pb_slice);
		int write(const std::string&);
		int write(const dynamic_vo&);
		int flush() const;
		int error() const;
		web_message msg{};

	private:
		int send_head();
		ref_ptr<web_session> _conn{};
	};
}
