#pragma once
#include "libcute.h"
#include <functional>
#include "memmap.h"
#define  MM_MAP_MAGIC (0xFA1BB1AF)

class mmap_stream {
public :
	mmap_stream(const char* file, size_t block, size_t capacity);
	typedef std::function<bool (const char*)> predicator;
	DELETE_MOVE_COPY(mmap_stream)
	char* add();
	int remove(size_t index) const;
	int remove_range(size_t index, size_t count) const;
	int remove_on(const predicator&) const;
	char* select(const predicator&) const;
	char* get(size_t index) const;
	int set(size_t at, const char* ptr) const;
	size_t size() const;
	uv_sem_t* sem() const;
	int flush() const;
	void clear() const;
	~mmap_stream();
private:
	struct head_t {
		uv_sem_t sem{};
		size_t magic = MM_MAP_MAGIC;
		size_t ver{0};
		size_t size{0};
		size_t capacity{0};
		size_t block{0};
	};

	union head_u {
		head_t t;
		char u[512]{0};
	};

	void remap();
	void extend_capacity(size_t size);
	void reblock(size_t);
	memmap* _file;
	head_t* _head;
};

template <typename T>
class mmap_vector {
public:
	typedef std::function<bool (const T*)> predicator;

	explicit mmap_vector(const char* file, const size_t capacity = 32) {
		_stream = new mmap_stream(file, sizeof(T), capacity);
	}

	DELETE_MOVE_COPY(mmap_vector)

	T* add() {
		return reinterpret_cast<T*>(_stream->add());
	}

	int remove(const size_t index) const {
		return _stream->remove(index);
	}

	int remove_range(const size_t index, const size_t len) const {
		return _stream->remove_range(index, len);
	}

	T* get(const size_t index) const { return reinterpret_cast<T*>(_stream->get(index)); }

	int set(const size_t index, T* v) {
		return _stream->set(index, reinterpret_cast<char*>(v));
	}

	int remove_on(const predicator& fn) const {
		auto bfn = [fn](const char* c) {
			const T* v = reinterpret_cast<const T*>(c);
			return fn(v);
		};
		return _stream->remove_on(bfn);
	}

	template <typename Fn, typename ...Args,
	          std::enable_if_t<std::is_invocable_r_v<bool, Fn, const T*, Args ...>> * = nullptr>
	T* select(Fn fn, Args ... args) const {
		for (size_t i = 0; i < _stream->size(); ++i) {
			auto* v = reinterpret_cast<T*>(_stream->get(i));
			if (fn(v, (args)...)) {
				return v;
			}
		}
		return nullptr;
	}

	void clear() const { _stream->clear(); }
	size_t size() const { return _stream->size(); }
	int flush() const { return _stream->flush(); }
	uv_sem_t* sem() const { return _stream->sem(); }

	~mmap_vector() {
		delete _stream;
	}

private:
	mmap_stream* _stream;
};
