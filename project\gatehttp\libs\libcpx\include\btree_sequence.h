#pragma once 
#include <fp_math.hpp>
#include <bt_context.h>
#include <time_scale_type.h>
#include <composition_return_mode.h>

namespace bt {
	template <typename BaseT>
	class btree_sequence : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
		nav::float_t time{};
		time_scale_type timeScaleType{};
		composition_return_mode returnMode{};
	protected:
		void on_start(input_type* in, output_type*) const final {
			index_context* ctx = base_type::template get_context<index_context>(in);
			ctx->index = 0;
			ctx->status(BST_RUN);
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type* in, output_type* out) const final {
			const auto st = internal_update(in, out);
			return adjust_status(st, returnMode);
		}

	private:
		btree_status internal_update(input_type* in, output_type* out) const {
			index_context* ctx = base_type::template get_context<index_context>(in);
			while (ctx->index < base_type::size()) {
				const auto* c = base_type::child_at(ctx->index);
				const auto st = c->update(in, out);
				switch (st) {
					case BST_RUN:
						return st;
					case BST_SUCCESS:
						c->status(in, st);
						c->stop(in, out);
						++ctx->index;
						continue;
					case BST_FAILURE:
						c->status(in, st);
						c->stop(in, out);
						ctx->status(st);
						return st;
					case BST_IDLE:
						throw_e("should not be idle");
					default: ;
				}
			}
			ctx->status(BST_SUCCESS);
			return ctx->status();
		}
	};
}
