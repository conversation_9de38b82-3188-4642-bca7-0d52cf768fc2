#pragma once
#include <cpx_action.h>
#include "cpx_action_builtin_wrapper.h" 
#include <cpx_struct.h>
namespace cpx {
	class cpx_act_delay final : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_act_delay)
		CPXA_CPX_ACT_DELAY_WRAPPER()
		cpx_act_delay();
		~cpx_act_delay() override;
	protected: 
		void on_start(cpx_input*, cpx_output*) const override;
		void on_stop(cpx_input*, cpx_output*) const override;
		bt::btree_status on_update(cpx_input*, cpx_output*) const override;
	};
};
