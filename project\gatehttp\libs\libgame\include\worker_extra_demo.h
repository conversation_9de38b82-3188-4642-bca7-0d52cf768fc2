#pragma once
#include "libcute.h"
#include "usr_worker.h"

namespace tvo {
	struct extra_server;
	class type_currency;
}

namespace dvo {
	class worker_extra_demo final : public usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_extra_demo)
		void start() override;
		void stop() override;
		void on_notifiction(const worker_notification*) override;
		void update(const update_context*) override; 
	private:
		void start_module();
		void reset_module();
		void on_tvo_change();
		bool _ok{};
	};
}
