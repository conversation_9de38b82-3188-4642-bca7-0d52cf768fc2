//gen by cpp_autogen automatically, don't modify it!
#pragma once
#ifndef CPX_CONDITION_BUILTIN_WRAPPER_H
#define CPX_CONDITION_BUILTIN_WRAPPER_H

#define CPXC_CPX_CONDITION_ANY_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_id() const noexcept override {\
return -1202668847;\
}\
const char* class_n() const noexcept override {\
return "cpx_condition_any";\
}\

#define CPXC_CPX_CONDITION_IS_CPX_INITED_WRAPPER()\
bool compare{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="compare"){v.get_to(compare);}\
}\
}\
int class_id() const noexcept override {\
return -108400545;\
}\
const char* class_n() const noexcept override {\
return "cpx_condition_is_cpx_inited";\
}\

#define CPXC_CPX_CONDITION_OR_WRAPPER()\
bool compare{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="compare"){v.get_to(compare);}\
}\
}\
int class_id() const noexcept override {\
return -1202675550;\
}\
const char* class_n() const noexcept override {\
return "cpx_condition_or";\
}\

#define CPXC_CPX_CONDITION_AND_WRAPPER()\
bool compare{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="compare"){v.get_to(compare);}\
}\
}\
int class_id() const noexcept override {\
return 1371750136;\
}\
const char* class_n() const noexcept override {\
return "cpx_condition_and";\
}\

#define CPXC_CPX_CONDITION_PARAMETER_WRAPPER()\
int64_t paramId{};\
nav::float_t threshold{};\
bt::value_compare_type compare{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="paramId"){v.get_to(paramId);}\
else if(k=="threshold"){v.get_to(threshold);}\
else if(k=="compare"){v.get_to(compare);}\
}\
}\
int class_id() const noexcept override {\
return 1409768428;\
}\
const char* class_n() const noexcept override {\
return "cpx_condition_parameter";\
}\



#endif //CPX_CONDITION_BUILTIN_WRAPPER_H
