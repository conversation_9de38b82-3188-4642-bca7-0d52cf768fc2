#pragma once
#include "logger.h"
#include "pb_base.h"
#include "pb_types.h"
#include "ref_ptr.h"
#include "route_t.h"
#include "service_common_code_wrapper.h"

class service_form final : public pb_base {
	CUTE_REF_PTR_CLASS_DECL(service_form)
public:
	DELETE_MOVE_COPY(service_form)

	enum func_type {
		PB_FUNC_SEND,
		PB_FUNC_REQUEST,
		PB_FUNC_RESPONSE,
		PB_FUNC_MULTICAST
	};

	int decode(const pb_buffer*);
	int encode(const pb_base*, pb_buffer*);
	int encode(pb_slice, pb_buffer*);
	route_t src() const;
	void src(const route_t& v);
	route_t dst() const;
	void dst(const route_t&);
	std::string obj_name() const;

	template <typename T, std::enable_if_t<std::is_convertible_v<T*, pb_base*>>* = nullptr>
	T obj() {
		T v = T();
		read_value(&v);
		return v;
	}

	template <typename T, std::enable_if_t<std::is_convertible_v<T*, pb_base*>>* = nullptr>
	void get_to(T& v) {
		read_value(&v);
	}

	int read_head(pbd_slice*);
	static int read_data(pbd_slice* s, pb::bytes_t& out);
private:
	/*heavy work*/
	void read_value(pb_base*) const;
public:
	service_form();
	~service_form() override;
	PB_WRAPPER_service_form()
};

typedef service_form::ptr_t service_form_t;
