#pragma once
#include "cpx_state.h"
#include <json_macro.h>

namespace cpx {
	class rpx_state_any;

	class cpx_state_any final : public cpx_state {
	public:
		DELETE_MOVE_COPY(cpx_state_any)
		cpx_state_any();
		~cpx_state_any() override;
		JSON_TYPE_INLINE(cpx_state_any,
		                 stateName,
		                 priority,
		                 isDefault,
		                 isEnter)
		state_type_t state_type() const override;
		rpx_state* obtain() const override;
		void release(rpx_state*) const override;
	protected:
		void parse(const dynamic_vo&) override;
	};
}
