#ifndef CUTE_SERVER_H
#define CUTE_SERVER_H
#include "web.h"
LP_NS_BEGIN
typedef struct cute_host_s cute_host_t;
typedef void (*uvserv_cb)(cute_host_t*);

enum uv_transport {
	SERV_TYPE_TCP = 0,
	SERV_TYPE_UCP = 1
};

enum uv_tcp_family {
	SERV_TCP4 = 0x0,
	SERV_TCP6 = 0x1,
};

struct cute_host_s {
	uv_loop_t* loop;

	union {
		uv_stream_t stream;
		uv_handle_t handle;
		uv_tcp_t tcp;
		uv_udp_t udp;
	};

	enum uv_transport transport;
	enum uv_tcp_family family;
	uvserv_cb on_conn_cb;
	uvserv_cb on_shut_cb;
	void* data;
	int ssl;
	char file_pk[PATH_SIZE];
	char file_ce[PATH_SIZE];
};

LP_API int cute_server_init(cute_host_t* serv);
LP_API int cute_server_tcp(cute_host_t* serv, const char* ip, int port, enum uv_tcp_family family);
LP_API void cute_server_sslcert(cute_host_t* serv, const char* pk, const char* cert);
LP_API int cute_server_shutdown(cute_host_t* serv);
LP_API void cute_server_free(cute_host_t* serv);
LP_NS_END
#endif
