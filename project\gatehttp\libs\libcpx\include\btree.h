#pragma once
#include "btree_status.h"
#include "bt_input_t.h" 
#include "treecore.h"

namespace bt {
	class bt_context;
	class output_t;
	class input_t;

	class btree : public treecore<btree> {
	public:
		DELETE_MOVE_COPY(btree)
		btree_status update(input_t*, output_t*) const;
		void pause(input_t*, output_t*) const;
		void resume(input_t*, output_t*) const;
		void stop(input_t*, output_t*) const;
		void reset(const input_t*, output_t*) const;
		btree_status status(const input_t*) const;
		void status(const input_t*, btree_status) const;

		template <typename T, std::enable_if_t<std::is_base_of_v<bt_context, T>>* = nullptr>
		T* get_context(const input_t* in) const {
			return in->cluster.context<T>(idx);
		}

		~btree() override;
	protected:
		virtual void self_start(input_t* in, output_t* out) const =0;
		virtual btree_status self_update(input_t* in, output_t* out) const = 0;
		virtual void self_stop(input_t* in, output_t* out) const = 0;
		virtual void self_pause(input_t* in, output_t* out) const;
		virtual void self_resume(input_t* in, output_t* out) const;
		btree();
	};
}
