#pragma once
#include "route_t.h"
#include "libcute.h"
#include "recycle_stream.h"
class pb_base;
class pb_object;

namespace net {
	class route_multicast final {
	public:
		DELETE_MOVE_COPY(route_multicast)
		explicit route_multicast(route_t src, route_t dst);
		explicit route_multicast(int service_mask);
		~route_multicast();
		/*single send */
		int send(const pb_base*) const;
		/*batch send call*/
		int batch(const pb_base*);
		int flush() const;

	private:
		route_t _src;
		route_t _dst;
		recycle_stream _batch{};
	};
}
