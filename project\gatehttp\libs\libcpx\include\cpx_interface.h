#pragma once
#include <libcute.h>
#include <libcutex.h>
#include <randseed.h>
#include <time_vo.h>
#include "icpx_ticker.h"

namespace nav {
	class vector3;
}

namespace cpx {
	class anim_collection;
	struct tick_context;
	class rpx_controller;
	class auxiliary_collection;
	interface cpx_entity {
	public:
		DELETE_MOVE_COPY(cpx_entity)
		virtual id_64 id() const = 0;
		virtual std::string name() = 0;
		virtual time_vo time() const = 0;
		virtual void pre_tick(const tick_context*) = 0;
		virtual void tick(const tick_context*) = 0;
		virtual void post_tick(const tick_context*) =0;
		virtual nav::randseed& seed() = 0;
		virtual void initialize() = 0;
		virtual ~cpx_entity();
		virtual void mark_will_dispose() = 0;
		virtual bool will_dispose() const = 0;
		virtual void finalize() = 0;
#if DEBUG
		INTERFACE_GETTER_SETTER(in_debug_scope, bool)
#endif
	protected:
		cpx_entity();
	};

	interface icomplex_entity : implements cpx_entity {
	public:
		DELETE_MOVE_COPY(icomplex_entity)
		virtual rpx_controller& rpx() = 0;
		virtual auxiliary_collection& auxs() = 0;
	protected:
		icomplex_entity();
		~icomplex_entity() override;
	};

	interface irunner : extends iticker {
	public:
		DELETE_MOVE_COPY(irunner)
		virtual void pause() = 0;
		virtual void resume() = 0;
		virtual bool done() const = 0;
		virtual int error() const = 0;
		INTERFACE_GETTER_SETTER(source_id, int)
		INTERFACE_GETTER_SETTER(runner_id, int)
		~irunner() override;
	protected:
		irunner();
	};
}
