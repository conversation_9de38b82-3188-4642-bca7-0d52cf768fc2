#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include <json_macro.h>
#include <nav_struct.h>

namespace mono {
	class scene_asset;
}

namespace tvo {
	class type_place final : public type_data {
		enum class machine_config_type_t {
			kScene,
			kHost,
			kPlayer,
			kNpc,
		};

		struct machine_config {
			machine_config_type_t type;
			std::string file;
			JSON_TYPE_INCRUSIVE(machine_config, type, file)
		};

		struct simple_config {
			std::string sceneFile;
			int sceneType{};
			bool isDefault{};
			std::vector<machine_config> machineConfigs;
			nav::vector3 enterPoint;
			ds::float_t orient{};
			JSON_TYPE_INCRUSIVE(simple_config, sceneFile,
			                    sceneType,
			                    isDefault,
			                    machineConfigs,
			                    enterPoint,
			                    orient)
		};

	public:
		DELETE_MOVE_COPY(type_place)
		type_place();
		static void awake_all(const awake_context_t&);
		~type_place() override;
		int load(mono::scene_asset* &);
		const simple_config& config() const;

	private:
		simple_config _config{};
		mono::scene_asset* _asset{};
		int _voerr{};

	public:
		TYPE_PLACE_WRAPPER()
	};
}
