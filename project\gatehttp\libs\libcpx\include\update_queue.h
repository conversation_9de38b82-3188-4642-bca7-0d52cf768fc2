#pragma once
#include "libcutex.h"
#include "logger.h"
#include "ref_ptr.h"
#include <sys_algorithm.h>
#include <time_vo.h>

namespace sys {
	template <typename ...T>
	class update_queue {
	};

	template <typename T, typename C, typename ...Args>
	class update_queue<T, void (C::*)(Args ...)> {
	public:
		using type_ptr = ref_ptr<T>;
		using func_ptr = void (C::*)(Args ...);
		using iterator = typename std::vector<type_ptr>::iterator;
		using const_iterator = typename std::vector<type_ptr>::const_iterator;

		explicit update_queue(const func_ptr c) : _call(c) {
		}

		update_queue(const update_queue& rhs) = delete;
		update_queue& operator =(const update_queue&) = delete;

		update_queue(update_queue&& rhs) noexcept {
			swap(std::forward<update_queue>(rhs));
		}

		update_queue& operator =(update_queue&& rhs) noexcept {
			if (this != &rhs) {
				swap(std::forward<update_queue>(rhs));
			}
			return *this;
		}

		void swap(update_queue&& rhs) noexcept {
			std::swap(rhs._updating, _updating);
			std::swap(rhs._clearing, _clearing);
			std::swap(rhs._purge, _purge);
			std::swap(rhs._list, _list);
			std::swap(rhs._call, _call);
		}

		bool add(type_ptr obj) {
			for (const auto& a : _list) {
				if (a == obj) {
					return false;
				}
			}
			_list.emplace_back(obj);
			return true;
		}

		bool remove(type_ptr obj) {
			auto it = std::find(_list.begin(), _list.end(), obj);
			if (it == _list.end()) {
				return false;
			}
			return remove(it - _list.begin());
		}

		type_ptr remove(size_t idx) {
			if (idx >= _list.size()) {
				return nullptr;
			}
			type_ptr item = _list.at(idx);
			_list.at(idx) = nullptr;
			_purge = true;
			purge();
			return item;
		}

		template <typename Pred, typename ...PArgs,
		          std::enable_if_t<std::is_invocable_r_v<bool, Pred, T*, PArgs ...>> * = nullptr>
		int remove_if(Pred predicator, PArgs ... args) {
			int q = 0;
			for (size_t i = 0; i < _list.size(); ++i) {
				type_ptr v = _list[i];
				if (predicator(v.get(), (args) ...)) {
					_list[i] = nullptr;
					_purge = true;
					++q;
				}
			}
			purge();
			return q;
		}

		type_ptr operator[](const size_t pos) const {
			return at(pos);
		}

		type_ptr at(size_t pos) const {
			if (pos > _list.size()) {
				throw_e("out of range");
			}
			return _list.at(pos);
		}

		size_t size() const {
			return _list.size();
		}

		void invoke(Args ... args) {
			_updating = true;
			on_execute(std::forward<Args>(args)...);
			_updating = false;
			purge();
		}

		void try_endup(const cpx::time_vo& v) {
			_updating = true;
			for (const auto& a : _list) {
				a->try_endup(v);
			}
			_updating = false;
			purge();
		}

		iterator begin() {
			return _list.begin();
		}

		iterator end() {
			return _list.end();
		}

		const_iterator begin() const {
			return _list.begin();
		}

		const_iterator end() const {
			return _list.end();
		}

		const_iterator cbegin() const {
			return _list.cbegin();
		}

		const_iterator cend() const {
			return _list.cend();
		}

		void clear() {
			_clearing = true;
			_list.clear();
			_clearing = false;
		}

		~update_queue() {
			_call = nullptr;
			_list.clear();
		}

	private:
		void on_execute(Args&& ... args) {
			size_t at = 0;
			for (;;) {
				if (at >= _list.size()) {
					break;
				}
				const auto p = _list.at(at);
				if (p) {
					try_call(
						(p.get()->*_call)(args...);
					);
				}
				++ at;
			}
		}

		func_ptr _call{};
		std::vector<type_ptr> _list{};
		bool _updating{};
		bool _clearing{};
		bool _purge{};

		void purge() {
			if (_updating)return;
			if (!_purge)return;
			_purge = false;
			auto it = sys::remove(_list.begin(), _list.end(), nullptr);
			_list.erase(it, _list.end());
		}
	};
}
