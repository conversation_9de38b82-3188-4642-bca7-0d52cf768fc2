#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "property_group.h"

namespace tvo {
	class type_fxsource final : public type_data {
	public:
		DELETE_MOVE_COPY(type_fxsource)
		type_fxsource();
		static void awake_all(const awake_context_t&);
		std::string get_fx(int) const;
		~type_fxsource() override; 

	private: 
		sys::hashmapx<int,   type_fxsource*> _subs{};

	public:
		//@see TYPE_FXSOURCE_WRAPPER() in typeclass_define.h
		enum element_t {
		};

		TYPE_FXSOURCE_WRAPPER()
	};
}
