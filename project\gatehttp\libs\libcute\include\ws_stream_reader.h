#pragma once
#include "http_parser1.h"
#include "net_stream_reader.h"
#include "pb_stream.h"
#include "tcp_stream_reader.h"
#include "websocket_parser.h"
#include "web_message.h"
#include "web_parser.h"

namespace net {
	struct ws_stream_reader;
	struct ws_stream_writer;
	class tcp_peer;

	struct ws_stream_reader final : net_stream_reader {
		enum websocket_parser_state {
			WS_FRAME_BEGIN,
			WS_FRAME_HEADER,
			WS_FRAME_BODY,
			WS_FRAME_END,
			WS_FRAME_FIN,
		};

		DELETE_MOVE_COPY(ws_stream_reader)
		explicit ws_stream_reader(tcp_peer*);
		~ws_stream_reader() override;
		int read(ssize_t, const uv_buf_t*) override;
		void stat(stat_reader&, time_t) override;
		void on_fin();
		int shake_hand();
		void close() const;
		int opcode{};
		websocket_parser_state state;
		pb_stream buf{};
		websocket_parser parser{};
		web_message req{};
		web_parser11 http1;
		bool hshake{};
		bool unpack{};
		bool debug;

	private:
		tcp_peer* _peer;
		std::string _key{};
	};
}
