#pragma once
#include "pb.h"
#include "uv.h"

namespace sys {
	typedef void (*shell_exit_cb)(int64_t exit, int sig);

	enum class shell_output_type {
		Info,
		Error,
	};

	typedef void (*shell_stream_cb)(void*, pb_slice);
	int call_shell(const char* sh, shell_exit_cb);

	struct shell_starter {
		shell_exit_cb cb_exit{};
		shell_stream_cb cb_stdout{};
		shell_stream_cb cb_stderr{};
		const char* sh{};
		void* data{};
	};

	int call_shell(const shell_starter&);
}
