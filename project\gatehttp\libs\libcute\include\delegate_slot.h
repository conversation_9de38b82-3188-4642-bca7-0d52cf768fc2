#pragma once
#include <functional>
#include <mutex>
#include <utility>
#include <vector>
#include <type_traits>
#include "libcute.h"
#include "sys_exception.h"

namespace delegate_internal {
	template <typename ... T>
	struct callback_t {
	};

	template <typename R, typename ... Args>
	struct callback_t<R(Args ...)> {
		struct base_type {
			DELETE_MOVE_COPY(base_type)
			virtual const std::type_info* tinfo() const = 0;
			virtual base_type* clone(char*) const = 0;
			virtual R operator()(Args&& ...) const = 0;

			virtual ~base_type() {
				inst = nullptr;
				func = nullptr;
			}

			template <typename T>
			bool is_type() const {
				static const std::type_info& t = typeid(T);
				auto* s = tinfo();
				return s && *s == t;
			}

			void* inst{};
			void* func{};
		protected:
			base_type() = default;
		};

		struct direct_call final : base_type {
			using method_t = R(*)(Args ...);

			static void* void_cast(R (*fn)(Args ...)) {
				union {
					R (*pf)(Args ...);
					void* p;
				};
				pf = fn;
				return p;
			}

			explicit direct_call(R (*fn)(Args ...)) {
				base_type::func = void_cast(fn);
			}

			explicit direct_call(void* m) {
				base_type::func = m;
			}

			const std::type_info* tinfo() const override {
				return nullptr;
			}

			base_type* clone(char* m) const override {
				auto* c = new(m)direct_call(base_type::func);
				return c;
			}

			R operator()(Args&&... args) const override {
				return ((method_t)base_type::func)(std::forward<Args>(args)...);
			}
		};

		template <typename C>
		struct member_call final : base_type {
			using method_t = R(C::*)(Args ...);

			static void* void_cast(R (C::*fn)(Args ...)) {
				union {
					R (C::*pf)(Args ...);
					void* p;
				};
				pf = fn;
				return p;
			}

			member_call(C* i, R (C::* fn)(Args ...)) {
				base_type::func = void_cast(fn);
				base_type::inst = i;
			}

			member_call(void* i, void* f) {
				base_type::func = f;
				base_type::inst = i;
			}

			const std::type_info* tinfo() const override {
				static const std::type_info& i = typeid(C);
				return &i;
			}

			base_type* clone(char* m) const override {
				return new(m)member_call(base_type::inst, base_type::func);
			}

			R operator()(Args&&... args) const override {
				return (((C*)(base_type::inst))->*(*((method_t*)(&(base_type::func)))))(std::forward<Args>(args)...);
			}
		};

		char mem[sizeof(base_type)]{};
		base_type* holder{};
		constexpr callback_t() noexcept = default;

		void swap(callback_t&& rhs) noexcept {
			auto* a = rhs.holder ? rhs.holder->clone(mem) : nullptr;
			auto* b = holder ? holder->clone(rhs.mem) : nullptr;
			if (!a && holder) {
				holder->~base_type();
				holder = nullptr;
			} else {
				holder = a;
			}
			if (!b && rhs.holder) {
				rhs.holder->~base_type();
				rhs.holder = nullptr;
			} else {
				rhs.holder = b;
			}
		}

		void copy(const callback_t& rhs) {
			auto* a = rhs.holder ? rhs.holder->clone(mem) : nullptr;
			if (!a && holder) {
				holder->~base_type();
				holder = nullptr;
			} else {
				holder = a;
			}
		}

		bool operator ==(const callback_t& rhs) const {
			if (holder == rhs.holder)return true;
			if (holder && !rhs.holder)return false;
			if (!holder && rhs.holder)return false;
			return holder->func == rhs.holder->func &&
				holder->inst == rhs.holder->inst;
		}

		explicit callback_t(R (* fn)(Args ...)): holder(new(mem)direct_call(fn)) {
		}

		template <typename C>
		explicit callback_t(C* i, R (C::*fn)(Args ...)):
			holder(new(mem)member_call<C>(i, fn)) {
		}

		~callback_t() {
			if (holder) {
				holder->~base_type();
			}
			holder = nullptr;
		}

		callback_t(const callback_t& rhs) {
			copy(rhs);
		}

		callback_t& operator =(const callback_t& rhs) {
			if (this != &rhs) {
				copy(rhs);
			}
			return *this;
		}

		callback_t(callback_t&& rhs) noexcept {
			swap(std::forward<callback_t>(rhs));
		}

		callback_t& operator =(callback_t&& rhs) noexcept {
			if (this != &rhs) {
				swap(std::forward<callback_t>(rhs));
			}
			return *this;
		}

		operator bool() const {
			return holder && holder->func;
		}

		R operator()(Args&& ... args) {
			if (!holder) {
				throw_e("no holder");
			}
			return holder->operator()(std::forward<Args>(args)...);
		}
	};

	template <typename ...T>
	struct delegate_slot {
	};

	template <typename R, typename ... Args>
	struct delegate_slot<R(Args ...)> {
		using caller_type = callback_t<R(Args ...)>;
		using base_type = typename caller_type::base_type;
		using direct_call = typename caller_type::direct_call;
		template <typename C>
		using member_call = typename caller_type::template member_call<C>;
		bool cancel_{};
		char mem[sizeof(base_type)]{};
		base_type* holder{};

		delegate_slot() noexcept : holder(nullptr) {
		}

		explicit delegate_slot(const R (*d)(Args ...)): holder(new(mem)direct_call(d)) {
		}

		template <typename C>
		explicit delegate_slot(C* i, R (C::*fn)(Args ...)):
			holder(new(mem)member_call<C>(i, fn)) {
		}

		~delegate_slot() {
			if (holder) {
				holder->~base_type();
			}
			holder = nullptr;
		}

		void swap(delegate_slot&& rhs) noexcept {
			auto* a = rhs.holder ? rhs.holder->clone(mem) : nullptr;
			auto* b = holder ? holder->clone(rhs.mem) : nullptr;
			if (!a && holder) {
				holder->~base_type();
				holder = nullptr;
			} else {
				holder = a;
			}
			if (!b && rhs.holder) {
				rhs.holder->~base_type();
				rhs.holder = nullptr;
			} else {
				rhs.holder = b;
			}
			std::swap(cancel_, rhs.cancel_);
		}

		void copy(const delegate_slot& rhs) {
			auto* a = rhs.holder ? rhs.holder->clone(mem) : nullptr;
			if (!a && holder) {
				holder->~base_type();
				holder = nullptr;
			} else {
				holder = a;
			}
			cancel_ = rhs.cancel_;
		}

		delegate_slot(const delegate_slot& rhs) {
			copy(rhs);
		}

		delegate_slot& operator =(const delegate_slot& rhs) {
			if (this != &rhs) {
				copy(rhs);
			}
			return *this;
		}

		delegate_slot(delegate_slot&& rhs) noexcept {
			swap(std::forward<delegate_slot>(rhs));
		}

		delegate_slot& operator =(delegate_slot&& rhs) noexcept {
			if (this != &rhs) {
				swap(std::forward<delegate_slot>(rhs));
			}
			return *this;
		}

		bool operator ==(const delegate_slot& rhs) const {
			if (holder && !rhs.holder) {
				return false;
			}
			if (!holder && rhs.holder)return false;
			if (holder == rhs.holder)return true;
			return holder->func == rhs.holder->func &&
				holder->inst == rhs.holder->inst;
		}

		operator bool() const {
			return holder && holder->func;
		}
	};
}
