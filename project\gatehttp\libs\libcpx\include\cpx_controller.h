#pragma once 
#include <libcutex.h>
#include <vector>
#include "cpx_core.h"

namespace cpx {
	class cpx_machine;
	class cpx_parameter;

	class cpx_controller final : public cpx_core {
	public:
		DELETE_MOVE_COPY(cpx_controller)
		cpx_controller();
		~cpx_controller() override;
		std::vector<cpx_parameter*> parameters;
		std::vector<cpx_machine*> machines;
		cpx_parameter* get_param(int pname)const ;
		cpx_parameter* get_param(int64_t pid)const;
		void read(const dynamic_vo& vo, const std::string& file);
	};
}
