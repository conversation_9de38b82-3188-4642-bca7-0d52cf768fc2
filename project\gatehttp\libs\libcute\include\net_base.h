#pragma once
#include "attachboard.h"
#include "net_variable.h"
#include "service_form.h"
#include "socket_form.h"
#include "web.h"
#include "net_status.h"
#include "delegate.h"

namespace net {
	class net_base {
	public:
		enum evt_type {
			CONN,
			CLOSE,
			READ,
			WRITE,
			ERR,
		};

		struct event_t {
			event_t(net_base*, evt_type, int);
			net_base* target;
			evt_type type;
			int error;
			service_form_t form;

			union {
				pb_slice buf{};
				void* msg;
			};
		};

		typedef std::function<void (const event_t*)> stop_cb;
		using invoker_t = delegate<void(const event_t*)>;
		CUTE_DELEGATE_FUNC_DECL(invoker_t,
		                        _invoke,
		                        addlistener,
		                        removelistener)
	public:
		using net_peer_id = size_t;
		DELETE_MOVE_COPY(net_base)
		net_status status() const;
		int error() const;
		size_t id() const;
		virtual void stop(stop_cb = nullptr) = 0;
		time_t alive_time() const;
		void alive_time(time_t);
		attachboard<net_variable>& vars();
		virtual ~net_base();

	protected:
		explicit net_base(uv_loop_t*, net_peer_id);
		void set_status(net_status v);
		int check_result(int code);
		void invoke(evt_type);
		void invoke(const event_t*);
		void set_stopcb(const stop_cb& cb);
		void update_alivetime();
		uv_loop_t* _loop{};
		uv_mutex_t _mutex{};
		size_t _id{};
		int _error{};
		time_t _alivetime{};

	private:
		invoker_t _invoke{};
		net_status _status{};
		stop_cb _stopcb{};
		attachboard<net_variable> _attach{};
	};
}

namespace net {
	typedef net_base::event_t net_event;
}
