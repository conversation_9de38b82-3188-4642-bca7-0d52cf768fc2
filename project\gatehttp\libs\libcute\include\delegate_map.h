#pragma once
#include <delegate_internal.h>
#include <thread_locker.h>
#include <hashmapx.h>
#include <logger.h>
template <typename ...T>
class delegate_map_vo {
};

template <typename TLocker, typename Ky, typename R, typename ...Args>
class delegate_map_vo<<PERSON><PERSON><PERSON><PERSON>, Ky, R(Args ...)> {
public:
	using this_type = delegate_map_vo<TL<PERSON><PERSON>, Ky, R(Args ...)>;
	typedef Ky key_t;
	typedef R (*delegate_call)(Args ...);
	template <typename C>
	using delegate_mcall = R(C::*)(Args ...);
	using entry_t = delegate_internal::delegate_entry<R(Args ...)>;
	using call_t = delegate_internal::call_type; 
	using locker_type = TLocker;
	using thread_guard_t = sys::thread_guard<locker_type>;
	delegate_map_vo() = default;

	delegate_map_vo(const delegate_map_vo& rhs) {
		_entries = rhs._entries;
	}

	delegate_map_vo(delegate_map_vo&& rhs) noexcept {
		_entries = std::move(rhs._entries);
	}

	delegate_map_vo& operator =(const delegate_map_vo& rhs) {
		_entries = rhs._entries;
		return *this;
	}

	delegate_map_vo& operator =(delegate_map_vo&& rhs) noexcept {
		if (&rhs != this) {
			_entries = std::move(rhs._entries);
		}
		return *this;
	}

	this_type& add(const Ky& key, delegate_call fn) {
		thread_guard_t g(_mutex);
		_entries.emplace(key, entry_t(fn));
		return *this;
	}

	template <class C>
	this_type& add(const Ky& key, C* obj, delegate_mcall<C> fn) {
		thread_guard_t g(_mutex);
		auto func = delegate_internal::make_class_member(fn, obj);
		_entries.emplace(key, entry_t(func, obj));
		return *this;
	}

	this_type& remove(const Ky& key) {
		thread_guard_t g(_mutex);
		auto it = _entries.find(key);
		if (it == _entries.end())return *this;
		_entries.erase(it);
		return *this;
	}

	bool contains(const Ky& key) {
		thread_guard_t g(_mutex);
		auto it = _entries.find(key);
		return it != _entries.end();
	} 

 

	R operator()(const Ky& key, Args&&...args) noexcept {
		thread_guard_t g(_mutex);
		auto it = _entries.find(key);
		if (it == _entries.end()) {
			return R();
		}
		entry_t& e = it->second;
		if (e.type_ == call_t::FUNC_PTR) {
			try {
				return e.dcall_(std::forward<Args>(args)...);
			} catch (std::exception& err) {
				log_error("error:%s", err.what());
				return R();
			} catch (...) {
				log_error("<unknown error>");
				return R();
			}
		}
		return e.mcall_(std::forward<Args>(args)...);
	}

	~delegate_map_vo() {
		thread_guard_t g(_mutex);
		_entries.clear();
	}

private :
	sys::hashmapx<key_t, entry_t> _entries{};
	locker_type _mutex;
};

template <typename Ky, typename R, typename ...Args>
using delegate_map = delegate_map_vo<sys::thread_lock, Ky, R, Args...>;
#ifndef CUTE_DELEGATEMAP_FUNC_DECL
#define CUTE_DELEGATEMAP_FUNC_DECL(T , MEMBER, ADD, REMOVE)								\
public:																					\
/*---add----	*/																		\
void ADD(const T::key_t& k, T::delegate_call fn){(MEMBER).add(k,fn);}					\
template<typename C>																	\
void ADD(const T::key_t& k, C* obj, T::delegate_mcall<C> fn){(MEMBER).add(k,obj,fn);}	\
/*---remove----	*/																		\
void REMOVE(const T::key_t& k){(MEMBER).remove(k);}										\
private:
#endif
#ifndef CUTE_STATIC_DELEGATEMAP_FUNC_DECL
#define CUTE_STATIC_DELEGATEMAP_FUNC_DECL(T , MEMBER, ADD, REMOVE)								\
public:																							\
/*---add----	*/																				\
static void ADD(const T::key_t& k, T::delegate_call fn){(MEMBER).add(k,fn);}					\
template<typename C>																			\
static void ADD(const T::key_t& k, C* obj, T::delegate_mcall<C> fn){(MEMBER).add(k,obj,fn);}	\
/*---remove----	*/																				\
static void REMOVE(const T::key_t& k){(MEMBER).remove(k);}										\
private:
#endif
