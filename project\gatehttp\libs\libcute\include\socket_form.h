#pragma once
#include "service_common_code_wrapper.h"
#include "pb_types.h"
#include "libcute.h"
#include "pb_base.h"
#include "pb_stream.h"
#include "ref_ptr.h"

class SocketForm final : public pb_base {
public:
	CUTE_REF_PTR_CLASS_DECL(SocketForm)
public:
	DELETE_MOVE_COPY(SocketForm)
	PB_WRAPPER_SocketForm()
	SocketForm();
	~SocketForm() override;
	std::string obj_name() const;
	int decode(const pb_buffer*);

	template <typename T, std::enable_if_t<std::is_convertible_v<T*, pb_base*>>* = nullptr>
	T obj() {
		T v = T();
		read_value(&v);
		return v;
	}
	int encode(pb_stream&, const pb_base*);

private:
	void read_value(pb_base*) const;
};

typedef SocketForm socket_form;
typedef SocketForm::ptr_t socket_form_t;
