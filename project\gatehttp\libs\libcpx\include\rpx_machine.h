#pragma once
#include <any.h>
#include <set>
#include <string>
#include <vector>
#include "rpx_core.h"

namespace cpx {
	class cpx_transition;
	class rpx_state_any;
	class cpx_state;
	class rpx_state;
	class cpx_machine;

	class rpx_machine final : public rpx_core {
	public:
		DELETE_MOVE_COPY(rpx_machine)
		void enter() override;
		void update(const tick_context*) override;
		bool change_state(const cpx_state*, const cpx_transition* = nullptr);
		void exit() override;
		explicit rpx_machine(const cpx_machine*);
		~rpx_machine() override;
		size_t layer{};
		const cpx_machine* source() const;
		rpx_state* current_state() const;
		rpx_state* runing_state() const;
		rpx_state_any* any() const;

	private:
		void change_state(rpx_state*);
		const cpx_machine* _source{};
		const cpx_state* _block{};
		rpx_state_any* _any{};
		rpx_state* _current{};
		int _recrusive{};
		void clear_recrusive();
		int update_recrusive(const rpx_state*);
		std::string recruisive_error(const rpx_state* state, bool clear);
#if DEBUG
		std::vector<const cpx_state*> _recrusive_states;
		std::set<id_64> _block_states;
		std::set<int64_t> _transition_lastframe;

	public:
		void lastframe_transition(std::vector<int64_t>& to) const;
		void recrusive_state(std::set<int64_t>& to) const;
		void block_state(std::set<int64_t>& to) const;
#endif
	};
}
