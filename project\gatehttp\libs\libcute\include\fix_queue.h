#pragma once
#include <array>
#include <libcute.h>

namespace sys {
	template <typename T, size_t Size>
	class fix_queue {
	public:
		template <typename QueueType, typename ElementType>
		struct iterator_type {
			using element_type = ElementType;
			using iterator_category = std::forward_iterator_tag;
			using value_type = element_type;
			using difference_type = ptrdiff_t;
			using pointer = element_type*;
			using reference = element_type&;
			using queue_type = QueueType;
			friend fix_queue;
			iterator_type() = default;
			~iterator_type() = default;

			iterator_type(queue_type* e, const int p): _queue(e), _pos(p) {
			}

			iterator_type(const iterator_type& rhs) {
				copy(rhs);
			}

			iterator_type(iterator_type&& rhs) noexcept {
				if (&rhs != this) {
					swap(std::forward<iterator_type>(rhs));
				}
			}

			iterator_type& operator =(const iterator_type& rhs) {
				if (&rhs != this) {
					copy(rhs);
				}
				return *this;
			}

			iterator_type& operator =(iterator_type&& rhs) noexcept {
				if (&rhs != this) {
					swap(std::forward<iterator_type>(rhs));
				}
				return *this;
			}

			bool operator ==(const iterator_type& rhs) const {
				return rhs._queue == _queue &&
					rhs._pos == _pos;
			}

			bool operator !=(const iterator_type& rhs) const {
				return !(*this == rhs);
			}

			iterator_type& operator ++() {
				++_pos;
				if (_queue && _queue->_size <= _pos) {
					_queue = nullptr;
					_pos = 0;
				}
				return *this;
			}

			iterator_type& operator ++(int) {
				auto temp = *this;
				++(*this);
				return temp;
			}

			void swap(iterator_type&& rhs) noexcept {
				std::swap(_queue, rhs._queue);
				std::swap(_pos, rhs._pos);
			}

			void copy(const iterator_type& rhs) {
				_queue = rhs._queue;
				_pos = rhs._pos;
			}

			operator bool() const {
				return _queue;
			}

			element_type& operator *() const {
				if (!_queue) {
					throw_e("out of range");
				}
				size_t at = (_pos + _queue->_head) % Size;
				return _queue->_list.at(at);
			}

			element_type* operator ->() const {
				if (!_queue) {
					throw_e("out of range");
					return nullptr;
				}
				size_t at = (_pos + _queue->_head) % Size;
				return &_queue->_list.at(at);
			}

		private:
			queue_type* _queue{};
			size_t _pos{};
		};

	public:
		fix_queue() = default;
		~fix_queue() = default;
		DEFAULT_MOVE_COPY(fix_queue)

		void emplace_back(const T& v) {
			_list.at(_tail % Size) = v;
			++_tail;
			++_size;
			if (_size >= Size) {
				_size = Size;
				_head = (_tail - _size) % Size;
			}
		}

		size_t size() const {
			return _size;
		}

		bool empty() const {
			return _tail == 0;
		}

		using iterator = iterator_type<fix_queue, T>;
		using const_iterator = iterator_type<const fix_queue, const T>;

		iterator begin() {
			if (_size == 0)return iterator{};
			return iterator(this, 0);
		}

		iterator end() {
			return iterator(nullptr, 0);
		}

		const_iterator begin() const {
			if (_size == 0)return const_iterator{};
			return const_iterator(this, 0);
		}

		const_iterator end() const {
			return const_iterator(nullptr, 0);
		}

	private:
		size_t _head{};
		size_t _tail{};
		size_t _size{};
		std::array<T, Size> _list{};
	};
}
