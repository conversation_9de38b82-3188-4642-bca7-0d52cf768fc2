#pragma once
#include "http_head_type.h"
#include "libcutex.h"
#include "ref_ptr.h"
#include "web_message.h"
#include "web_parser.h"

namespace net {
	class web_client;
	struct web_client_request;

	struct web_client_response {
		void init(web_client_request*, const web_client&);
		int read(pb_buffer&);
		bool done() const;
		int error() const;
		void recv(pb_slice) const;
		web_client_request* req{};
		web_message msg{};

	private:
		web_parser11 _parser{};
	};

	struct web_client_request {
		DELETE_MOVE_COPY(web_client_request)
		web_client_request();
		~web_client_request();
		int connect(const char*);
		int write(const char*, size_t);
		int write(pb_slice);
		int write(const std::string&);
		int write(const dynamic_vo&);
		int send_head();
		int flush() const;
		const ref_ptr<web_client>& conn();
		web_client_response& response();
		int error() const;
		web_message msg{};

	private:
		ref_ptr<web_client> _conn{};
		web_client_response _response{};
		bool _headout{};
	};
}
