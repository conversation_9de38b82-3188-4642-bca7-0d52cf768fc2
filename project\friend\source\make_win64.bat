@echo off
setlocal enabledelayedexpansion

REM 检查构建类型参数
set BUILD_TYPE=Debug
if "%1"=="Release" set BUILD_TYPE=Release
if "%1"=="release" set BUILD_TYPE=Release

echo 构建类型: %BUILD_TYPE%

if not exist "build_win64" (
	mkdir build_win64
)
pushd build_win64
cmake ../ ^
		-G "Visual Studio 16 2019" -A x64 ^
		-DCMAKE_BUILD_TYPE=%BUILD_TYPE%^
		-DMAKE_INSTALL=ON ^
		-DCMAKE_INSTALL_PREFIX=../../cute_root ^
		..

cmake --build ./ --config %BUILD_TYPE%
cmake --install ./ --config %BUILD_TYPE%
popd

echo "install done (%BUILD_TYPE%)"
