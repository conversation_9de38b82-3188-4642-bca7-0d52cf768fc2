#ifndef HTTP_MESSAGE_H
#define HTTP_MESSAGE_H
#define  URL_SIZE 512
#define  BODY_SIZE 2048
#define  MAX_CHUNK 16
#include "web.h"
#include "http_def.h"
LP_NS_BEGIN 

/*define the interesting head and parse it only*/
#define HEAD_TYPE(XX)								\
	XX(0, CONNECTION		, "CONNECTION"			)	\
	XX(1, CONTENT_LENGTH	, "CONTENT-LENGTH"		)	\
	XX(2, CONTENT_TYPE		, "CONTENT-TYPE"		)

enum http_head_type {
#define  XX(num, name, string)HHT_##name=(num),
	HEAD_TYPE(XX)
#undef XX
};

static const char* http_head_field[] = {
#define XX(num, name, string)string,
	HEAD_TYPE(XX)
#undef XX
};

typedef struct http_head_str {
	char base[URL_SIZE];
	size_t size;
} http_head_str;

typedef struct http_head {
	enum http_method method;
	unsigned status_code;
	int should_keepalive;
	http_head_str url;
	http_head_str reponse_status;
	int body_final;
	http_head_str fields[array_size(http_head_field)];
	http_head_str field;
	int field_idx;
	signed last_head_t;
	int num_chunck;
	int num_chunck_complete;
	uint64_t chunk_lengths[MAX_CHUNK];
} http_head;

typedef struct http_message_s http_message_t;
typedef void (*http_phase_cb)(http_message_t* stream, http_phase phase);

typedef struct http_message_s {
	http_head head;
	pb_buffer body;
	http_parser parser;
	http_phase_cb phase_cb;
	void* data;
} http_message_t;

#define http_head_struct_core	\
int headout ;					\
size_t content_length ;			\
enum http_content_type content_type ;

typedef struct head_request_s {
	http_head_struct_core
	enum http_method method;
} head_request_t;

typedef struct head_response_s {
	http_head_struct_core
	enum http_status status;
} head_response_t;

LP_API void http_message_init(http_message_t* stream, void* data, http_phase_cb cb);
LP_API void http_message_reset(http_message_t* stream);
LP_API int http_message_parse(http_message_t* stream, const char* buf, ssize_t size);
LP_API void http_message_free(http_message_t*);
LP_API int http_message_read_url(const struct http_parser_url* url, const char* urlbuf, int beg, int end, char* to);
LP_API void http_message_slice_url(const struct http_parser_url* url, const char* usl_str, int beg, int end,
                                   uv_buf_t* to);  
/*

*/
LP_API void http_head_response_init(head_response_t*);
LP_API void http_head_request_init(head_request_t*);
LP_API int http_message_cpy_url(char* dst,
                                const char* src,
                                const struct http_parser_url* url,
                                enum http_parser_url_fields field);
LP_API int http_message_parse_ipport(const char* src, char* ip, u_short* port);
LP_NS_END
#endif
