#pragma once
#include <libcute.h>
#include <libcutex.h>
#include <type_data.h>
#include "property_group.h"
#include "thing_group.h"
#include "typedata_wrapper_libgame.h"

namespace tvo {
	class type_npc_level final : public type_data {
	public:
		DELETE_MOVE_COPY(type_npc_level)
		type_npc_level();
		~type_npc_level() override;
		static void awake_all(const awake_context_t&);
		static const type_npc_level* find(int npc, int lv);
	public:
		TYPE_NPC_LEVEL_WRAPPER()
	};
}
