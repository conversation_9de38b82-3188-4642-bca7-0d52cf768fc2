#pragma once
#include "pb.h"
#include "pb_context.h"
#include <ref_ptr.h>
struct route_t;
class pb_base;

namespace mem {
	class ref_stream {
	public:
		CUTE_REF_PTR_CLASS_DECL(ref_stream)

	public:
		DELETE_MOVE_COPY(ref_stream)
		ref_stream();
		~ref_stream();
		pb_slice slice() const;
		pbd_slice sliced() const;
		void reserv(size_t);
		/*compress src into self*/
		int compress(pb_slice src, int level = -1, size_t* outsize = nullptr);
		/*uncompress src into self*/
		int uncompress(pb_slice src, size_t* outsize = nullptr);
		pb_buffer* buf();
		char* base() const;
		size_t size() const;
		bool empty() const;
		int append(const pb_buffer*);





		/*append send obj to buffer*/
		int send(const pb_base*, const route_t&);
		void clear();

		bool ws{};

	private:
		pb_buffer _buf;
	};
}
