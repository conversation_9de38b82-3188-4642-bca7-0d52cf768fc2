#pragma once
#include "libcutex.h"
#include "service_type.h"
#include <string>

namespace sys {
	struct service_args {
		service_args();
		explicit service_args(const char*);
		int64_t id{};
		int sub{};
		int type{};
		service_role role{};
		int cnt_reboot{};
		int cnt_dump{};
		std::string name{};
		std::string str() const;
		dynamic_vo read();
		int error() const;
		void read_file(const char*);
	private :
		int _error{};
	};
}
