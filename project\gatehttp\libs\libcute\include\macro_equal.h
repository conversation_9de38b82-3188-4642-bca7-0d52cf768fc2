#pragma once
#include "macro_tool.h"
#include <vector>
template <typename T>
bool operator==(std::vector<T> const& a, std::vector<T> const& b) noexcept {
	if (a.size() != b.size()) {
		return false;
	}
	for (size_t i = 0; i < a.size(); ++i) {
		if (a.at(i) != b.at(i)) {
			return false;
		}
	}
	return true;
}

#define cute_compare_fn(n)if(ja.n!=jb.n){return false;}
#define CUTE_COMPARE_FUNC(T ,...) 										\
inline bool operator == (const T& ja, const T& jb) { 					\
CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_compare_fn, __VA_ARGS__)) \
return true;															\
}																		\
inline bool operator!=(const T& ja, const T& jb){ 						\
	return !(ja == jb); 												\
}
/*



*/
#define cute_set_fun(n)dst.n = src.n;
#define CUTE_SET_FUNC(...) CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_set_fun, __VA_ARGS__))
/*
*/
#define CUTE_TRANSFER_DEFINE(method, SRC, DST, ...)						\
inline DST method(const SRC& src) {										\
DST dst{} ;																\
CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_set_fun, __VA_ARGS__))	\
return dst; }
