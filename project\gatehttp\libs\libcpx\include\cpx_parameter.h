#pragma once
#include <fp_math.hpp>
#include <libcutex.h>
#include "cpx_core.h"
#include <json_macro.h>
namespace cpx {
	class cpx_parameter final : public cpx_core {
	public:
		DELETE_MOVE_COPY(cpx_parameter)
		cpx_parameter();
		~cpx_parameter() override;
		void read(const dynamic_vo& v, const std::string& file);

		enum cpx_parameter_type {
			kBool,
			kFloat,
			kInt,
			kTrigger
		};

		cpx_parameter_type type;
		std::string name;
		nav::float_t defaultValue{};
		int nameHash{};
	};
}
