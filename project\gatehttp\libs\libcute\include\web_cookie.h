#pragma once
#include <string>

namespace net {
	struct web_cookie {
		void reset();
		bool parse(const std::string&);
		std::string dump() const;
		void dump(std::stringstream&) const;
		std::string name;
		std::string value;
		std::string domain;
		std::string path;
		std::string expires;
		int max_age{};
		bool secure{};
		bool httponly{};

		enum e_same_site {
			Default,
			Strict,
			Lax,
			None
		} samesite{};

		enum e_priority {
			NotSet,
			Low,
			Medium,
			High,
		} priority{};

		std::map<std::string, std::string> values; // for multiple names
	};
}
