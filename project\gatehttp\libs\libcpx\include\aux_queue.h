#pragma once
#include "libcutex.h"
#include "logger.h"
#include "ref_ptr.h"
#include <time_vo.h>
#include "auxiliary.h"

namespace sys {
	class aux_queue {
	public:
		using type_ptr = ref_ptr<cpx::auxiliary>;
		using iterator = std::vector<type_ptr>::iterator;
		using const_iterator = std::vector<type_ptr>::const_iterator;
		DELETE_MOVE_COPY(aux_queue)
		explicit aux_queue();
		~aux_queue();
		bool add(const type_ptr&);
		bool remove(const type_ptr&);
		type_ptr remove(size_t);

		template <typename Pred, typename ...PArgs,
		          std::enable_if_t<std::is_invocable_r_v<bool, Pred, cpx::auxiliary*, PArgs ...>> * = nullptr>
		int remove_if(Pred predicator, PArgs&&... args) {
			int q = 0;
			for (size_t i = 0; i < _list.size(); ++i) {
				type_ptr v = _list[i];
				if (predicator(v.get(), std::forward<PArgs>(args) ...)) {
					_list[i] = nullptr;
					_purge = true;
					++q;
				}
			}
			purge();
			return q;
		}

		type_ptr operator[](size_t) const;
		type_ptr at(size_t) const;
		size_t size() const;
		void invoke(const cpx::tick_context*);
		void try_endup(const cpx::time_vo&);
		iterator begin();
		iterator end();
		const_iterator begin() const;
		const_iterator end() const;
		const_iterator cbegin() const;
		const_iterator cend() const;
		void clear();
		void abort_frame(int64_t);
	private:
		void internal_call(const cpx::tick_context*);
		void purge();
		std::vector<type_ptr> _list{};
		bool _updating{};
		bool _clearing{};
		bool _purge{};
		int64_t _break_frame{};
	};
}
