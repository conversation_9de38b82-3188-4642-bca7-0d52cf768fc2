#pragma once
#include <string>
#include "service_type.h"
#include "json_macro.h"

namespace sys {
	struct service_conf_sql {
		std::string server{};
		std::string db{};
		std::string usr{};
		std::string passwd{};
		std::string schema{};
		unsigned short port{};
		JSON_TYPE_INCRUSIVE(service_conf_sql,
		                    server, db,
		                    usr, passwd,
		                    port,
		                    schema)
	};

	struct service_conf_retry {
		int delay{};
		int max_retry{};
		int cris_time{};
		NLOHMANN_DEFINE_TYPE_INTRUSIVE(service_conf_retry,
		                               delay,
		                               max_retry,
		                               cris_time)
	};

	struct service_conf_net {
		id_64 id{};
		int type{};
		int port{};
		int family{};
		int threadcount{};
		int slavecount{};
		std::string slavepath{};
		service_conf_retry slaveretry{};
		int ws{};
		int wzip{};
		int rzip{};
		bool ssl{};
		std::string file_pk{};
		std::string file_cr{};
		NLOHMANN_DEFINE_TYPE_INTRUSIVE(service_conf_net,
		                               port, family, threadcount,
		                               slavecount, slavepath, slaveretry,
		                               ws, wzip, rzip,
		                               ssl, file_pk, file_cr)
	};

	struct tcp_simple_info {
		std::string ip{};
		int port{};
		int ws{};
		int wzip{};
		int rzip{};
		bool ssl{};
		JSON_TYPE_INCRUSIVE(tcp_simple_info,
		                    ip,
		                    port,
		                    ws, wzip, rzip, ssl)
	};

	struct net_address {
		std::string ip{};
		int port{};
	};

	struct net_zip {
		int write{};
		int read{};
	};

	using service_conf_http = service_conf_net;
	using service_conf_tcp = service_conf_http;

	struct service_conf_idpool {
		int64_t capacity;
		float ratio;
		NLOHMANN_DEFINE_TYPE_INTRUSIVE(service_conf_idpool,
		                               capacity,
		                               ratio)
	};
}
