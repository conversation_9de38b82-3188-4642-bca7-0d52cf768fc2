#pragma once
#include <data.h>
#include <pb_scope.h>
#include <ref_ptr.h>
#include <set>
#include <system_error.h>
#include <tcp_conn.h>
#include "pthread_game.h"
#include "uz_enum.h"
#include <cluster_manager.h>

namespace app {
	struct pipe_signal;
	class pipe_thread;
}

namespace dvo {
	struct offline_data {
		id_64 acc_id{};
		id_64 usr_id{};
		std::set<ref_ptr<data>> list{};
	};

	void send_offline(const offline_data&);
	forceinline int get_usrzone(const id_64 acc_id, route_t& uzone) {
		return sys::cluster_manager::instance().cring().
		                                        find(SVT_USRZONE, acc_id, uzone);
	}
}

namespace dvo {
	template <typename Callback, typename Message, typename... Args,
	          std::enable_if_t<
		          std::is_invocable_r_v<int, Callback, pb_buffer*, Args...> &&
		          std::is_base_of_v<pb_base, Message>
	          > * = nullptr>
	int send_uzone(Callback cb, const Message& msg, const route_t& to, pb_buffer* buf, Args&&... args) {
		int err;
		mem::pb_size_scope scope_size(buf);
		{
			service_form form{};
			form.src(net::tcp_conn::get_self());
			form.dst(to);
			form.form_method = service_form::PB_FUNC_SEND;
			form.class_id = msg.type();
			if ((err = form.encode(buf))) {
				scope_size.ignore();
				return err;
			}
			{
				mem::pb_bytes_scope scope_g(buf, service_form::kData);
				if ((err = msg.encode(buf))) {
					scope_size.ignore();
					return err;
				}
				if ((err = cb(buf, std::forward<Args>(args)...))) {
					return err;
				}
			}
		}
		return 0;
	}

	template <typename Message, std::enable_if_t<std::is_base_of_v<pb_base, Message>> * = nullptr>
	int send_uzone(const Message& msg, const route_t& to, pb_buffer* buf) {
		int err;
		mem::pb_size_scope scope_size(buf);
		{
			service_form form{};
			form.src(net::tcp_conn::get_self());
			form.dst(to);
			form.form_method = service_form::PB_FUNC_SEND;
			form.class_id = msg.type();
			if ((err = form.encode(buf))) {
				scope_size.ignore();
				return err;
			}
			{
				mem::pb_bytes_scope scope_g(buf, service_form::kData);
				if ((err = msg.encode(buf))) {
					scope_size.ignore();
					return err;
				}
			}
		}
		return 0;
	}

	template <typename Message, std::enable_if_t<std::is_base_of_v<pb_base, Message>> * = nullptr>
	int send_uzone(const Message& msg) {
		int err;
		route_t to{};
		if ((err = get_usrzone(msg.acc_id, to))) {
			log_info("save fail: %d", err);
			return err;
		}
		const auto stream = app::pthread_game::mq.stream(to);
		if (!stream) {
			return ERR_USE_NULLPTR;
		}
		auto* buf = stream->buf();
		mem::pb_size_scope scope_size(buf);
		{
			service_form form{};
			form.src(net::tcp_conn::get_self());
			form.dst(to);
			form.form_method = service_form::PB_FUNC_SEND;
			form.class_id = msg.type();
			if ((err = form.encode(buf))) {
				scope_size.ignore();
				return err;
			}
			{
				mem::pb_bytes_scope scope_g(buf, service_form::kData);
				if ((err = msg.encode(buf))) {
					scope_size.ignore();
					return err;
				}
			}
		}
		return 0;
	}
}

namespace dvo {
	int write_db(pb_buffer*, const mem::message_collection_head*, const database*, uzone_save_mode);
	int write_datas(pb_buffer*, const mem::message_collection_head*, const std::set<ref_ptr<data>>&, uzone_save_mode);
}

namespace uz {
	void start();
}
