//gen by cpp_autogen automatically, don't modify it!
#pragma once
#ifndef BTREE_BUILTIN_WRAPPER_H
#define BTREE_BUILTIN_WRAPPER_H

#define BTREE_SCENE_COMPOSITE_SEQUENCE_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return 1728580721;\
}\
const char* class_n() const override {\
return "scene_composite_sequence";\
}\

#define BTREE_SCENE_COMPOSITE_SEQUENCE_SELECTOR_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return 1384601484;\
}\
const char* class_n() const override {\
return "scene_composite_sequence_selector";\
}\

#define BTREE_SCENE_COMPOSITE_PARALLEL_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return 1728577709;\
}\
const char* class_n() const override {\
return "scene_composite_parallel";\
}\

#define BTREE_SCENE_COMPOSITE_PARALLEL_SELECTOR_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return -1397043768;\
}\
const char* class_n() const override {\
return "scene_composite_parallel_selector";\
}\

#define BTREE_SCENE_DECO_REPEAT_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="count"){v.get_to(count);}\
else if(k=="min"){v.get_to(min);}\
else if(k=="max"){v.get_to(max);}\
}\
}\
int class_t() const override {\
return 1734428780;\
}\
const char* class_n() const override {\
return "scene_deco_repeat";\
}\

#define BTREE_SCENE_DECO_NEVER_STOP_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_t() const override {\
return -835514951;\
}\
const char* class_n() const override {\
return "scene_deco_never_stop";\
}\

#define BTREE_SCENE_DECO_UNTIL_SUCCESS_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_t() const override {\
return -969596610;\
}\
const char* class_n() const override {\
return "scene_deco_until_success";\
}\

#define BTREE_SCENE_DECO_UNTIL_FAILURE_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_t() const override {\
return 787798832;\
}\
const char* class_n() const override {\
return "scene_deco_until_failure";\
}\

#define BTREE_PERSONALITY_COMPOSITION_SEQ_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return 677905082;\
}\
const char* class_n() const override {\
return "personality_composition_seq";\
}\

#define BTREE_PERSONALITY_COMPOSITION_PAL_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return -925858192;\
}\
const char* class_n() const override {\
return "personality_composition_pal";\
}\

#define BTREE_PERSONALITY_COMPOSITION_SELECTOR_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return 2095345252;\
}\
const char* class_n() const override {\
return "personality_composition_selector";\
}\

#define BTREE_FIREFX_COMPOSITE_SEQUENCE_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return -729199358;\
}\
const char* class_n() const override {\
return "firefx_composite_sequence";\
}\

#define BTREE_FIREFX_COMPOSITE_PARALLEL_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return -1293970513;\
}\
const char* class_n() const override {\
return "firefx_composite_parallel";\
}\

#define BTREE_FIREFX_COMPOSITE_SEQUENCE_SELECTOR_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return -1273800313;\
}\
const char* class_n() const override {\
return "firefx_composite_sequence_selector";\
}\

#define BTREE_FIREFX_COMPOSITE_PARALLEL_SELECTOR_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="time"){v.get_to(time);}\
else if(k=="timeScaleType"){v.get_to(timeScaleType);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_t() const override {\
return 1430572206;\
}\
const char* class_n() const override {\
return "firefx_composite_parallel_selector";\
}\

#define BTREE_FIREFX_DECO_REPEAT_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="count"){v.get_to(count);}\
else if(k=="min"){v.get_to(min);}\
else if(k=="max"){v.get_to(max);}\
}\
}\
int class_t() const override {\
return -1991844541;\
}\
const char* class_n() const override {\
return "firefx_deco_repeat";\
}\

#define BTREE_FIREFX_DECO_NEVER_STOP_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_t() const override {\
return 1584548221;\
}\
const char* class_n() const override {\
return "firefx_deco_never_stop";\
}\

#define BTREE_FIREFX_DECO_UNTIL_SUCCESS_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_t() const override {\
return 1037917690;\
}\
const char* class_n() const override {\
return "firefx_deco_until_success";\
}\

#define BTREE_FIREFX_DECO_UNTIL_FAILURE_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_t() const override {\
return -1403767308;\
}\
const char* class_n() const override {\
return "firefx_deco_until_failure";\
}\



#endif //BTREE_BUILTIN_WRAPPER_H
