#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "nav_struct.h"

namespace tvo {
	class type_currency final : public type_data {
	public:
		DELETE_MOVE_COPY(type_currency)
		type_currency();
		static void awake_all(const awake_context_t&);
		~type_currency() override;
		static type_currency* find(const std::string& key);
		TYPE_CURRENCY_WRAPPER()
	};
}
