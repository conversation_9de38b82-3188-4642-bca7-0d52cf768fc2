#pragma once
#include <fp_math.hpp>
#include <bt_context.h>
#include <time_scale_type.h>
#include <composition_return_mode.h>

namespace bt {
	template <typename BaseT>
	class btree_parallel : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
		nav::float_t time{};
		time_scale_type timeScaleType{};
		composition_return_mode returnMode{};
	protected:
		void on_start(input_type* in, output_type*) const final {
			auto* ctx = base_type::template get_context<default_context>(in);
			ctx->status(BST_RUN);
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type* in, output_type* out) const final {
			const auto st = internal_update(in, out);
			return adjust_status(st, returnMode);
		}

	private:
		btree_status internal_update(input_type* in, output_type* out) const {
			bool running = false;
			bool all_fail = true;
			for (size_t i = 0; i < base_type::size(); ++i) {
				const auto* c = base_type::child_at(i);
				const auto cst = c->status(in);
				switch (cst) {
					case BST_FAILURE: {
						continue;
					}
					case BST_SUCCESS: {
						all_fail = false;
						continue;
					}
					case BST_IDLE:
					case BST_RUN:
					default: ;
				}
				const auto st = c->update(in, out);
				switch (st) {
					case BST_SUCCESS:
						c->stop(in, out);
						c->status(in, st);
						all_fail = false;
						break;
					case BST_RUN:
						running = true;
						all_fail = false;
						break;
					case BST_FAILURE:
						c->stop(in, out);
						c->status(in, st);
						break;
					case BST_IDLE:
					default:
						throw_e("should not be idle");
				}
			}
			auto* ctx = base_type::template get_context<default_context>(in);
			btree_status r;
			if (running) {
				r = BST_RUN;
			} else if (all_fail) {
				ctx->status(BST_FAILURE);
				r = BST_FAILURE;
			} else {
				ctx->status(BST_SUCCESS);
				r = BST_SUCCESS;
			}
			return r;
		}
	};
}
