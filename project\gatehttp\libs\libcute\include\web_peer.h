#pragma once
#include "net_peer.h"

namespace net {
	struct web_message;

	class web_peer : public net_peer {
	public:
		DELETE_MOVE_COPY(web_peer)

	protected:
		web_peer(uv_loop_t*, net_peer_id);
		~web_peer() override;

	public:
		class co_read final : public co_job {
		public:
			DELETE_MOVE_COPY(co_read)
			explicit co_read(ptr_t ptr);
			~co_read() override;

		private:
			ptr_t _ptr;
			void on_read(const event_t*);
		};
	};
}
