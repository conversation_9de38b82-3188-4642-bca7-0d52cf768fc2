#pragma once
#include "rpx_state.h"

namespace cpx {
	class cpx_state_machine;

	class rpx_state_machine final : public rpx_state {
	public:
		DELETE_MOVE_COPY(rpx_state_machine)
		rpx_state_machine();
		~rpx_state_machine() override;
		void enter() override;
		void update(const tick_context*) override;
		void exit() override; 
		runtime_type rtype() const override;
		rpx_machine* current() const;
	private:
		rpx_machine* _machine{};
	};
}
