#pragma once
#include "net_base.h"
#include "co_job.h"
#include "cute_peer.h"
#include "cute_server.h"
#include "ip_addr.h"
#include "recycle_stream.h"
#include  "ref_ptr.h"
#include "service_struct.h"

namespace net {
	struct net_stream_reader;
	struct net_stream_writer;
}

namespace net {
	class net_peer : public net_base {
	public:
		CUTE_REF_PTR_CLASS_DECL(net_peer)

	public:
		DELETE_MOVE_COPY(net_peer)
		int peer_ip_port(char*, u_short*) const;
		int sock_ip_port(char*, u_short*) const;
		void stop(stop_cb = nullptr) override;
		void goodbye(const char*, size_t, const stop_cb& = nullptr);
		int accept(uv_stream_t*, cute_host_t*);
		void connect(const ip_addr*);
		int write(const char*, size_t) const;
		int write(const pb_buffer*) const;
		int write(const pb_slice&) const;
		int write(const sys::raw_string&) const;
		int write(const recycle_stream&) const;
		int keep_alive(bool enable, unsigned delay);
		int nodelay(int enable);
		int flush() const;
		ip_addr* addr();
		route_t route() const;
		void route(const route_t&);
		const cute_peer_t* core() const;
		cute_peer_t* sslc();
		void writer(net_stream_writer*);
		void reader(net_stream_reader*);
		int ping(time_t = 0) const;
		int pong(time_t = 0) const;
		net_stream_writer& writer() const;
		net_stream_reader& reader() const;
		sys::net_zip zip{};

	private:
		route_t _route;

	protected:
		net_stream_writer* _writer{};
		net_stream_reader* _reader{};
		/*
		
		*/
		friend class net_server;
		net_peer(uv_loop_t*, net_peer_id id);
		/*kill--------------------------*/
		void kill();
		void release_ptr();
		virtual void on_self_close();
		~net_peer() override;

	private:
		void on_recv(ssize_t, const uv_buf_t*);
		void on_close_();
		void on_connect_(int);
		void on_write_(int);
		static void uv_connect_cb(cute_peer_t*, int);
		static void uv_recv_cb(cute_peer_t*, ssize_t, const uv_buf_t*);
		static void uv_write_cb(cute_peer_t*, int);
		static void uv_close_cb(cute_peer_t*);
		static void get_uvbuf(cute_peer_t*, uv_buf_t*);
		cute_peer_t _core;
		ptr_t _self;
		ip_addr _addr;
		bool _goodbye{};

	public:
		class co_conn final : co_job {
		public:
			DELETE_MOVE_COPY(co_conn)
			explicit co_conn(ptr_t p);
			int error() const;
			~co_conn() override;

		private:
			ptr_t _p;
			void on_conn(const event_t*);
		};

		class co_write final : co_job {
		public:
			DELETE_MOVE_COPY(co_write)
			explicit co_write(ptr_t p);
			~co_write() override;

		private:
			ptr_t _p;
			void on_write(const event_t*);
		};

		class co_stop final : co_job {
		public:
			DELETE_MOVE_COPY(co_stop)
			explicit co_stop(ptr_t p);
			~co_stop() override;

		private:
			ptr_t _p;
		};
	};
}
