#pragma once
#include <cpx_action.h>
#include "cpx_action_builtin_wrapper.h" 
#include "cpx_composition.h"

namespace cpx {
	class cpx_act_sequence final : public cpx_composition {
	public:
		DELETE_MOVE_COPY(cpx_act_sequence)
		CPXA_CPX_ACT_SEQUENCE_WRAPPER()
		cpx_act_sequence();
		~cpx_act_sequence() override;
	protected: 
		void on_stop(cpx_input*, cpx_output*) const override;
		void cpx_start(cpx_input*, cpx_output*) const override;
		bt::btree_status cpx_update(cpx_input*, cpx_output*) const override;
		bt::bt_context* context(cpx_input*) const override;
	};
}
