#pragma once
#include "net_status.h"
#include "libcute.h"
#include "libcutex.h"
#include "logger.h"
#include "route_t.h"
#include "service_args.h"
#include "service_slave.h"
#include "type_cast.h"
#include "xtick.h"

namespace sys {
	struct service_notification;
	class service_aux;
}

namespace sys {
	class service_entity {
	public:
		static service_entity& instance();
		DELETE_MOVE_COPY(service_entity)
		virtual int start(service_args*);
		id_64 id() const;
		std::string name() const;
		int type() const;
		net::net_status status() const;
		void set_status(net::net_status v);
		int error() const;
		void set_error(int v);

		template <typename T>
		void get_value(const std::string& key, T& val) const {
			if (_config.empty()) {
				log_error("no configure set");
			} else if (!_config.contains(key)) {
				log_error("configure has not key :%s", key .c_str());
			} else {
				_config.at(key).get_to(val);
			}
		}

		template <typename T>
		T get_value(const std::string& key) const {
			T v{};
			get_value<T>(key, v);
			return v;
		}

		template <typename T>
		bool tryget_conf(const std::string& key, T& val) const {
			if (_config.empty()) {
				return false;
			}
			if (!_config.contains(key)) {
				return false;
			}
			_config.at(key).get_to(val);
			return true;
		}

		route_t route() const;
		service_role role() const;
		const dynamic_vo& config() const;
		proc::service_slave* get_slave() const;
		int cnt_reboot() const;
		int cnt_dump() const;
		int64_t workload() const;
		void workload(int64_t);
		xtick tick() const;
		void notify(service_notification&) const;
		void send_async();

		template <typename T>
		T* get_aux() {
			for (auto* s : _auxes) {
				T* f = sys::type_cast<T>(s);
				if (f) {
					return f;
				}
			}
			return nullptr;
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<service_aux, T>> * = nullptr>
		T& attach(T* c) {
			_auxes.emplace_back(c);
			c->entity(this);
			return *c;
		}

		void start_aux() const;
		void terminate();
		const std::vector<service_aux*>& auxes() const;
	protected:
		explicit service_entity(uv_loop_t* loop);
		virtual void on_stop() = 0;
		virtual void on_start() = 0;
		void start_self();
		void start_ticker(size_t);
		void stop_ticker();
		void start_async();
		void stop_aux();
		int start_slave();
		void set_config(const dynamic_vo& cfg);
		virtual void on_update();
		virtual ~service_entity();
		int _error{};
	private :
		void stop();
		void co_stop();
		void co_start();
		void on_slave_event(const proc::signal_t*);
		proc::service_slave* _slave{};
		net::net_status _status{};
		dynamic_vo _config{};
		std::string _name{};
		xtick _tick;
		std::vector<service_aux*> _auxes{};
		uv_timer_t* _timer{};
		int64_t _workload{};
		service_args _args{};
		uv_async_t _async{};
		static void time_cb(uv_timer_t*);
		static void async_cb(uv_async_t*);
	};
}
