#pragma once 
#include "co_job.h"
#include  "libcute.h"
#include "pb.h"
#include  "uv.h"
#include  "delegate.h"
#include "proc_signal.h"

namespace proc {
	class service_slave {
	public:
		typedef std::function<void()> on_shut_cb;
		typedef delegate<void(const signal_t*)> invoker_t;
		CUTE_DELEGATE_FUNC_DECL(invoker_t, _invoker, addlistener, removelistener)
		invoker_t _invoker{};
	public:
		DELETE_MOVE_COPY(service_slave)
		explicit service_slave(uv_loop_t* loop);
		int error() const;
		void stop(const on_shut_cb& = nullptr);
		uv_stream_t* stream();
		~service_slave();
	private:
		uv_loop_t* _loop{};

		union {
			uv_pipe_t pipe;
			uv_stream_t stream;
			uv_handle_t handle;
		} _ctx;

		int _error{};
		int _fin{};
		pb_buffer _buf{};
		on_shut_cb _shutcb{};
		void invoke(signal_code t);
		static void on_alloc(uv_handle_t*, size_t, uv_buf_t*);
		static void on_read(uv_stream_t*, ssize_t, const uv_buf_t*);
		static void on_disconnect(uv_handle_t*);
	public:
		class co_stopper final : public co_job {
		public:
			DELETE_MOVE_COPY(co_stopper)
			explicit co_stopper(service_slave*);
			~co_stopper() override;
		private:
			void exe();
			service_slave* _slave;
		};

		friend co_stopper;
	};
}
