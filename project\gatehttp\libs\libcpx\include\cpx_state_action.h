#pragma once
#include "cpx_state.h"
#include <json_macro.h>
#include <mutex>
#include "context_cluster.h"

namespace bt {
	class bt_workdata;
}

namespace cpx {
	class rpx_state_action;
	class cpx_action;

	class cpx_state_action final : public cpx_state {
	public:
		DELETE_MOVE_COPY(cpx_state_action)
		cpx_state_action();
		~cpx_state_action() override;
	protected:
		void parse(const dynamic_vo&) override;
	public:
		bool useStatePriority{};
		int loops{};
		std::vector<cpx_action*> actions;
		JSON_TYPE_INLINE(cpx_state_action,
		                 useStatePriority,
		                 stateName,
		                 priority,
		                 isDefault,
		                 isEnter,
		                 loops)
		state_type_t state_type() const override;
		rpx_state* obtain() const override;
		void release(rpx_state*) const override;
		int forcast_priority() const override;
		bt::context_cluster_pool& pool();
	private:
		int _forcast{};
		std::mutex _mutex{};
		bt::context_cluster_pool _cpool{};
	};
}
