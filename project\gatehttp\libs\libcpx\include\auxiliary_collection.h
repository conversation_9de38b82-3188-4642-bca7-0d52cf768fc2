#pragma once
#include <ref_ptr.h>
#include "auxiliary.h"

namespace cpx {
	class icomplex_entity;
	struct tick_context;
	class auxiliary;

	class auxiliary_collection {
	public:
		using list_type = std::vector<ref_ptr<auxiliary>>;
		using iterator = list_type::iterator;
		using const_iterator = list_type::const_iterator;
		DELETE_MOVE_COPY(auxiliary_collection)
		explicit auxiliary_collection(icomplex_entity*);
		~auxiliary_collection();

		template <typename T, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		ref_ptr<T> attach(T* v) {
			ref_ptr p(v);
			add(p);
			return p;
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		ref_ptr<T> attach(const ref_ptr<T>& p) {
			add(p);
			return p;
		}

		template <typename T, typename ... Args, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		ref_ptr<T> attach(Args&&...args) {
			ref_ptr p(new T(std::forward<Args>(args)...));
			add(p);
			return p;
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		ref_ptr<T> attach() {
			ref_ptr p(new T());
			add(p);
			return p;
		}

		ref_ptr<auxiliary> attach(const ref_ptr<auxiliary>&);
		void detach(const ref_ptr<auxiliary>&);
		void detach(auxiliary*);
		ref_ptr<auxiliary> detach(size_t at);

		template <typename T, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		void detach_all() {
			for (size_t i = 0; i < _list.size(); ++i) {
				const auto x = _list.at(i);
				auto* v = sys::type_cast<T>(x.get());
				if (!v) {
					continue;
				}
				_list[i] = nullptr;
				_purge = true;
				x->exit();
			}
			purge();
		}

		template <typename Fn, typename ...Args,
		          std::enable_if_t<std::is_invocable_r_v<bool, Fn, auxiliary*, Args ...>>* = nullptr>
		void detach_if(Fn&& predict, Args&&... args) {
			for (size_t i = 0; i < _list.size(); ++i) {
				auto x = _list.at(i);
				if (!x) {
					continue;
				}
				if (!predict(x.get(), std::forward<Args>(args)...)) {
					continue;;
				}
				_list[i] = nullptr;
				_purge = true;
				x->exit();
			}
			purge();
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		ref_ptr<T> get_aux() const {
			for (const auto& p : _list) {
				auto* v = sys::type_cast<T>(p.get());
				if (v) {
					return ref_ptr(v);
				}
			}
			return nullptr;
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		void get_aux(std::vector<T*>& to) {
			for (const auto& p : _list) {
				auto* v = sys::type_cast<T>(p.get());
				if (v) {
					to.emplace_back(v);
				}
			}
		}

		ref_ptr<auxiliary> get_aux(size_t at) const;

		template <typename T, std::enable_if_t<std::is_base_of_v<auxiliary, T>>* = nullptr>
		size_t size() const {
			size_t cnt = 0;
			for (const auto& p : _list) {
				T* v = sys::type_cast<T>(p.get());
				if (v) {
					++cnt;
				}
			}
			return cnt;
		}

		iterator begin();
		iterator end();
		const_iterator begin() const;
		const_iterator end() const;
		size_t size() const;
		void update(const tick_context*);
		void abort_frame(int64_t);
		void try_endup(const time_vo&);
		void clear();
	private:
		void add(const ref_ptr<auxiliary>&);
		void purge();
		void internal_call(const tick_context*);
		std::vector<ref_ptr<auxiliary>> _list{};
		icomplex_entity* _entity;
		int64_t _abort{};
		bool _updating{};
		bool _clearing{};
		bool _purge{};
	};
}
