#pragma once
#include <map>
#include "http_def.h"
#include "http_head_type.h"
#include "pb_stream.h"
#include "str_tools.h"
#include "web_cookie.h"
#include "http_parser.h"

namespace sys {
	struct string_buffer;
}

namespace net {
	using http_headers = std::map<std::string, std::string, sys::string_case_less>;

	struct web_message {
		void add_cookie(const web_cookie&);
		using cookie_name = std::string;
		const web_cookie& get_cookie(const cookie_name&) const;
		/*-------------header method-------------*/
		int tryhead(const std::string&, std::string&) const;
		const std::string& head(const std::string&) const;
		web_message& head(httphead_request, const std::string&);
		web_message& head(httphead_response, const std::string&);
		web_message& head(const std::string&, const std::string&);
		web_message& append(httphead_request, const std::string&);
		web_message& append(httphead_response, const std::string&);
		web_message& append(const std::string&, const std::string&);
		void dump(sys::string_buffer&) const;
		std::string str() const;
		http_parser_type type{};

		struct {
			uint16_t major{1};
			uint32_t minor{1};
		} http;

		union {
			http_method method{HTTP_GET};
			http_status status_code;
		};

		enum compress_option {
			None = 0,
			/*GNU zip*/
			kGzip = 1 << 0,
			/*Unix File Compress*/
			kCompress = 1 << 1,
			/*Zlib*/
			kDeflat
		};

		std::string url{}; // for on_url
		http_headers headers{};
		std::vector<web_cookie> cookies{};
		pb_stream stream{};
		std::string response_status{};
		mutable bool keep_alive{true};
		size_t content_length{};
		compress_option zipopt{};
		http_content_type content_type{TEXT_PLAIN};
	};
}
