#pragma once
#include <chrono>
#include <sstream>
#include <string>

#include  "libcute.h"

class stop_watcher {
public:
	stop_watcher();
	void start();
	double duration() const;
	void reset();
private:
	typedef std::chrono::high_resolution_clock::time_point time_stamp_t;
	time_stamp_t _start;
};

namespace sys {
	class step_watcher {
	public:
		step_watcher();
		DELETE_MOVE_COPY(step_watcher)
		void start();
		void start(const std::string & title);
		void movenext(const std::string& description);
		void logout();
		~step_watcher();
	private: 
		void fin();
		stop_watcher _watch;
		stop_watcher _total;
		int _done;
		std::stringstream _os;
	};
}
