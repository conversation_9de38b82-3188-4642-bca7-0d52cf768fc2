#pragma once
#include "libcute.h"
#include "pb.h" 
#include <iterator>
struct pbd_slice; 

 
namespace ecs {
	struct decode_context;
	struct encode_context;
	struct fieldinfo_t;
	struct value_t;
	class classinfo_t;

	class object_t {
	public:
		friend class classinfo_t;
		friend class array_t;
		using pos_t = uint32_t;
		object_t();
		object_t(const object_t&);
		object_t(object_t&&) noexcept;
		object_t& operator =(const object_t&);
		object_t& operator =(object_t&&) noexcept;
		bool operator ==(const object_t&&) const;
		bool operator ==(nullptr_t) const;
		value_t& operator[](pos_t pos) const;
		value_t* at(pos_t pos) const;
		classinfo_t* classinfo() const; 
		int encode(pb_buffer*, const encode_context&) const;
		int decode(pbd_slice*, const decode_context&) const;
		bool ok() const;
		~object_t();
	private:
		void swap(object_t&) noexcept; 
		classinfo_t* _class{};
		char* _addr{};
	public:
		struct iter_v {
			const fieldinfo_t* field{};
			value_t* value{};
		};

		struct iterator {
			using iterator_category = std::forward_iterator_tag;
			using value_type = iter_v;
			using difference_type = ptrdiff_t;
			using pointer = iter_v*;
			using reference = iter_v&;
			iterator(object_t*, uint32_t p);
			iterator();
			iterator(const iterator&);
			iterator(iterator&&) noexcept;
			iterator& operator =(const iterator&);
			iterator& operator =(iterator&&) noexcept;
			iterator& operator ++();
			bool operator ==(const iterator&) const;
			bool operator !=(const iterator&) const;
			iterator operator ++(int);
			iter_v operator *() const;
			~iterator();
		private:
			uint32_t _pos;
			object_t* _obj;
			void copy(const iterator&);
			void swap(iterator&&) noexcept;
		};

		typedef iterator::iterator_category iterator_category;
		typedef iterator::difference_type difference_type;
		typedef iterator::pointer pointer;
		typedef iterator::reference reference;

		struct const_iterator {
			using iterator_category = std::forward_iterator_tag;
			using value_type = iter_v;
			using difference_type = ptrdiff_t;
			using pointer = iter_v*;
			using reference = iter_v&;
			const_iterator(const object_t*, uint32_t p);
			const_iterator();
			const_iterator(const const_iterator&);
			const_iterator(const_iterator&&) noexcept;
			const_iterator& operator =(const const_iterator&);
			const_iterator& operator =(const_iterator&&) noexcept;
			const_iterator& operator ++();
			const_iterator operator ++(int);
			bool operator ==(const const_iterator&) const;
			bool operator !=(const const_iterator&) const;
			iter_v operator *() const;
			~const_iterator();
		private:
			uint32_t _pos;
			const object_t* _obj;
			void copy(const const_iterator&);
			void swap(const_iterator&&) noexcept;
		};

		iterator begin();
		iterator end();
		const_iterator begin() const;
		const_iterator end() const;
	};
}
