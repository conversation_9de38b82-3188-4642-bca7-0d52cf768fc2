#ifndef LIB_CUTE_H
#define LIB_CUTE_H  
#ifndef LP_NS_BEGIN
# ifdef __cplusplus
#   define LP_NS_BEGIN extern "C" {
#   define LP_NS_END   }
# else
#   define LP_NS_BEGIN
#   define LP_NS_END
# endif
#endif /* PB_NS_BEGIN */
#if defined __GNUC__
#include <unistd.h>
#define HAS_MMAP 1
#elif defined WIN32
#pragma comment(lib,"ws2_32.lib")
#define HAS_MMAP 0
#define WIN32_LEAN_AND_MEAN
#endif
//-------------------end of ns brace------------  
#ifndef LP_STATIC
# if defined __GNUC__
#   define LP_STATIC static __attribute((unused))
# else
#   define LP_STATIC  
# endif
#endif
#ifdef LP_STATIC_API
# ifndef PB_IMPLEMENTATION
		#  define PB_IMPLEMENTATION
# endif
	# define LP_API LP_STATIC
#endif
#if !defined(LP_API) && defined(_WIN32)
#define LP_API __declspec(dllexport)
#endif
/*
*/
#ifndef container_of
#define container_of(ptr, type, member) \
  ((type *) ((char *) (ptr) - offsetof(type, member)))
#endif
/*

*/
#ifndef array_size
# define array_size(a) (int)(sizeof(a) / sizeof((a)[0]))
#endif
#ifndef LP_API
# define LP_API extern
#endif
/*
		CUTE_DEPRECATED
*/
#if defined(HV_NO_DEPRECATED)
#define CUTE_DEPRECATED
#elif defined(__GNUC__) || defined(__clang__)
#define CUTE_DEPRECATED   __attribute__((deprecated))
#elif defined(_MSC_VER)
#define CUTE_DEPRECATED   __declspec(deprecated)
#else
#define CUTE_DEPRECATED
#endif
/*
		CUTE_UNUSED
*/
#if defined(__GNUC__)
    #define CUTE_UNUSED   __attribute__((visibility("unused")))
#else
#define CUTE_UNUSED
#endif
/*

*/
#ifndef DEBUG
#ifdef NDEBUG
#define DEBUG (0)
#else
#define DEBUG (1)
#endif
#endif //end of DEBUG 
/*

*/
#define DELETE_MOVE_COPY(T)					\
T(const T &) 					= delete;	\
T(T &&) noexcept 				= delete;	\
T& operator =(const T &) 		= delete;	\
T& operator =(T &&) noexcept 	= delete;
#define DEFAULT_MOVE_COPY(T)				\
T(const T &) 					= default;	\
T(T &&) noexcept 				= default;	\
T& operator =(const T &) 		= default;	\
T& operator =(T &&) noexcept 	= default;
//------------------------------------endof delete_move_copy 
#define IPC		(1)
#define NOIPC	(0)
//------------------------------------endof ipc define
#define STDIN   (0)
#define STDOUT  (1)
#define STDERR  (2)
#define CONFIG_OVERVIEW  "config/service/overall.conf"
//------------------------------------endof stdio define    
LP_NS_BEGIN
#include "stdlib.h"
#include "stdio.h"
#include  "uv.h"
#include  "string.h"
#define ct_free free
#define ct_malloc malloc
#define ct_calloc calloc
#define ct_memset memset
#define ct_realloc realloc
#define ct_memcpy memcpy
#define ct_new_c(T) (T*)ct_calloc(1, sizeof(T))
/*


*/
#ifdef WIN32
#include  <io.h>
#define access _access
typedef long long ssize_t;
#endif
typedef int64_t id_64;
typedef unsigned long long thread_id;
#define get_thread_id(id) *(thread_id*)(&(id))
/*


*/
#if defined __GNUC__
#define vprint_t vsnprintf
#define CHECK_PRINTF(m,n) __attribute__((format(printf,m,n)))
#define NO_RETURN	__attribute__((noreturn))
#else
#define CHECK_PRINTF(m,n)
#define NO_RETURN
#define vprint_t vsnprintf
#endif
/*
	FORCE INLINE
*/
#ifdef WIN32 // for MSVC
#define forceinline __forceinline
#elif defined __GNUC__ // for gcc on Linux/Apple OS X
#define forceinline __inline__ __attribute__((always_inline))
#else
#define forceinline
#endif
/*
	DEFAULT 
*/
#ifndef DEFAULT
#ifdef __cplusplus
#define DEFAULT(x)  = x
#else
#define DEFAULT(X)
#endif
#endif
/*


*/
LP_NS_END
#undef min
#undef max
//------------------------------------endof memory define    
#endif
