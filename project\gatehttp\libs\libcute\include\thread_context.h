#pragma once
class thread_context; 
#include <functional>
#include <uv.h>
#include <vector>
#include "libcute.h"
#include  "thread_pool.h"

class thread_context {
public: 
	friend thread_pool;
	typedef std::function<void()> asycn_cb;
	DELETE_MOVE_COPY(thread_context)
	static int await(thread_id, const asycn_cb&);
	static thread_id this_id();
	static thread_context* this_context();
	static void set_main(uv_loop_t*);
	static thread_id main_id();
	static bool is_main();
private:
	void await_(const asycn_cb&);
	static int add_sub(thread_pool* pool, thread_id);
	static void remove_sub(thread_id);
	explicit thread_context(uv_loop_t* l, thread_id id);
	~thread_context();
	std::vector<asycn_cb> _async_cbs;
	uv_loop_t* _loop;
	uv_async_t _async;
	uv_mutex_t _mutex;
	thread_pool* _sub;
	thread_id _subid;
	static void on_async_call(uv_async_t*);
	static void stop();
};
