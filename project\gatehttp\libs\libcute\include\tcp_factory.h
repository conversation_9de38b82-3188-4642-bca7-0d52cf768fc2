#pragma once
namespace net {
	struct net_stream_reader;
	struct net_stream_writer;
	class tcp_peer;
	typedef net_stream_writer* (*writer_factory)(tcp_peer*);
	typedef net_stream_reader* (*reader_factory)(tcp_peer*);
	net_stream_writer* default_writer(tcp_peer*);
	net_stream_reader* default_reader(tcp_peer*);
	inline reader_factory tcp_read_factory = default_reader;
	inline writer_factory tcp_write_factory = default_writer;
}
