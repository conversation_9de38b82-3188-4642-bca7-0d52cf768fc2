//#pragma once
//#include <libcute.h>
//#include "usr_worker.h"
//#include "worker_bag.h"
//
//namespace cpx {
//	class imodel_entity;
//}
//
//namespace dvo {
//	struct property_event;
//	struct sheet_event;
//}
//
//namespace ds {
//	class worker_render final : public dvo::usr_worker {
//	public:
//		DELETE_MOVE_COPY(worker_render)
//		TYPE_CAST_IMPLEMENT(worker_render) 
//		void start() override;
//		void stop() override;
//		void update(const dvo::update_context*) override;
//	private:
//		void add_sheet(int dc);
//		void remove_sheet(int dc);
//		void on_sheet_change(const dvo::sheet_event*);
//		void on_post_call(const dvo::update_context*);
//		void on_bag_change(const bag_event*);
//		void on_char_change(const dvo::property_event*);
//		void draw() const;  
//		cpx::imodel_entity * _entity{};
//	};
//}
