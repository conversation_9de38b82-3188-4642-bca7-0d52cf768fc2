#ifndef HASHMAP_C_H
#define HASHMAP_C_H 
#include "libcute.h"
LP_NS_BEGIN
typedef void* hmc_kt;
typedef void* hmc_vt;

typedef struct hash_entry {
	hmc_kt key;
	hmc_vt value;
	int next;
	int code;
} entry_t;

typedef struct hashmapc hashmapc;
typedef int (*hashmap_code)(hashmapc*, hmc_kt key);
typedef int (*hashmap_equal)(hashmapc*, hmc_kt a, hmc_kt b);
typedef void (*hashmap_iterator)(hashmapc*, hmc_kt k, hmc_vt v);

struct hashmapc {
	int size;
	int capacity;
	hashmap_code hashcode;
	hashmap_equal equal;
	size_t size_k;
	size_t size_v;
	int* bunkets;
	entry_t* entries;
	int free_index;
	int remove_index;
};

typedef struct hashmapc_iterator {
	int index;
	const void* first;
	const void* second;
	hashmapc* map;
} hashmapc_iterator_t;

LP_API int hashmapc_init(hashmapc* map, size_t key_size, size_t value_size);
LP_API int hashmapc_free(hashmapc* map);
LP_API int hashmapc_find(hashmapc* map, hmc_kt key, hmc_vt* out);
LP_API int hashmapc_emplace(hashmapc* map, hmc_kt key, hmc_vt value);
LP_API int hashmapc_erase(hashmapc* map, hmc_kt key);
LP_API int hashmapc_clear(hashmapc* map);
LP_API int hashmapc_foreach(hashmapc* map, hashmap_iterator);
LP_API int hashmapc_iter_init(hashmapc* map, hashmapc_iterator_t* iter);
LP_API int hashmapc_iter_next(hashmapc_iterator_t* iter);
LP_NS_END
#endif
