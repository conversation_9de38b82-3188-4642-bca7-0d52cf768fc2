#pragma once
#include "cpx_state.h"
#include <json_macro.h>

namespace cpx {
	class rpx_state_machine;
	class cpx_machine;

	class cpx_state_machine final : public cpx_state {
	public:
		DELETE_MOVE_COPY(cpx_state_machine)
		JSON_TYPE_INLINE(cpx_state_machine,
		                 stateName,
		                 priority,
		                 isDefault,
		                 isEnter)
		cpx_state_machine();
		~cpx_state_machine() override;
		const cpx_machine* machine() const;
		state_type_t state_type() const override;
		rpx_state* obtain()const override;
		void release(rpx_state*)const override;
	protected:
		void parse(const dynamic_vo&) override;
	private:
		cpx_machine* _machine{}; 
	};
}
