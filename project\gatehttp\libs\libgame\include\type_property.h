#pragma once
#include <libcute.h>
#include "typedata_wrapper_libgame.h"
#include <type_data.h>
#define PT_RUNTIME			"runtime"
#define PT_PERSONALITY		"personality"
#define PT_ATTACK			"attack"
#define PT_DEFENCE			"defence"

namespace tvo {
	class type_property_type;

	class type_property final : public type_data {
	public:
		DELETE_MOVE_COPY(type_property)
		type_property();
		
		~type_property() override;
		const type_property_type* typedata() const;

		static void awake_all(const awake_context_t&);
		static const type_property* find(const std::string& key);
		static const type_property* find(int pid);
		static const std::string& id_to_key(int pid);
		static bool to_max(int pid, int& max_id);
		static bool to_maxadd(int pid, int& maxadd_id);
		/*hp->to_siblings( Max) => hpMax*/
		static bool to_sibling(int family, const std::string& suffix, int& sibling_id);
		/*hpMax->to_runtime(Max) => hp*/
		static bool to_runtime(int family, const std::string& suffix, int& runtime_id);
		static int key_to_id(const std::string& key);
		static const type_property* is_static(int pid);
		static const type_property* is_static(const std::string& key);
		static const type_property* is_runtime(int pid);
		static const type_property* is_runtime(const std::string& key);
		static const type_property* is_range(int pid);
		static const type_property* is_range(const std::string& key);
		static const std::vector<const type_property*>& get_attacks();
		static const std::vector<const type_property*>& get_defines();
		const static std::vector<const type_property*>& get_runtime_types();
		static const std::vector<const type_property*>& get_type(const std::string& type);

	private:
		const type_property_type* _type{};

	public:
		enum overlap_t {
			kAdd,
			kMulti
		};

		TYPE_PROPERTY_WRAPPER()
	};
}
