#pragma once
#include <bt_context.h>
#include <fp_math.hpp>
#include <libcute.h>
#include <sys_exception.h>
#include <vector2.h>
#include "time_scale_type.h"
#include "process_mode.h"
#include "value_compare_type.h"

namespace bt {
	template <typename BaseT>
	class btree_never_stop : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
	protected:
		void on_start(input_type* in, output_type*) const final {
			base_type::template get_context<default_context>(in)->status(BST_RUN);
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type*, output_type*) const final {
			return BST_RUN;
		}
	};
}

namespace bt {
	class repeat_context_t final : public default_context {
	public:
		int max_loops{};
		int current_loops{};
		bool is_paused{};
	protected:
		void clean() override;
	};

	template <typename BaseT>
	class btree_repeat : public BaseT {
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
	public:
		int max{0};
		int min{0};
		int count{0};
	protected:
		void on_start(input_type* in, output_type* _) const final {
			repeat_context_t* ctx = base_type::template get_context<repeat_context_t>(in);
			ctx->status(BST_RUN);
			ctx->current_loops = 0;
			ctx->is_paused = false;
			const nav::float_t nd = in->nextd();
			const int r = min + static_cast<int>((max - min) * nd);
			const int loops = count + r;
			ctx->max_loops = loops <= 0 ? INT_MAX : loops;
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type* in, output_type* out) const final {
			repeat_context_t* ctx = base_type::template get_context<repeat_context_t>(in);
			if (ctx->is_paused)return BST_RUN;
			if (ctx->status() == BST_RUN) {
				for (size_t i = 0; i < base_type::size(); ++i) {
					const auto* c = base_type::child_at(i);
					const auto st = c->update(in, out);
					if (st == BST_RUN) {
						return st;
					}
					c->status(in, st);
					c->stop(in, out);
				}
				++ctx->current_loops;
				if (ctx->current_loops < ctx->max_loops) {
					for (size_t i = 0; i < base_type::size(); ++i) {
						base_type::child_at(i)->reset(in, out);
					}
				} else {
					ctx->status(BST_SUCCESS);
				}
			}
			return ctx->status();
		}
	};
}

namespace bt {
	template <typename BaseT>
	class btree_until_fail : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
	protected:
		void on_start(input_type* in, output_type*) const final {
			base_type::template get_context<default_context>(in)->status(BST_RUN);
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type* in, output_type* out) const final {
			if (base_type::size() != 1) {
				throw_e("unitl fail children.size() != 1");
			}
			auto* ctx = base_type::template get_context<default_context>(in);
			const auto* child = base_type::child_at(0);
			const auto st = child->update(in, out);
			switch (st) {
				case BST_FAILURE: {
					child->status(in, st);
					ctx->status(st);
					return BST_SUCCESS;
				}
				case BST_RUN: {
					return st;
				}
				case BST_SUCCESS: {
					child->reset(in, out);
					return BST_RUN;
				}
				case BST_IDLE:
				default: break;
			}
			return BST_RUN;
		}
	};
}

namespace bt {
	template <typename BaseT>
	class btree_until_success : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
	protected:
		void on_start(input_type* in, output_type*) const final {
			base_type::template get_context<default_context>(in)->status(BST_RUN);
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type* in, output_type* out) const final {
			if (base_type::size() != 1) {
				throw_e("unitl fail children.size() != 1");
			}
			auto* ctx = base_type::template get_context<default_context>(in);
			const auto* child = base_type::child_at(0);
			const auto st = child->update(in, out);
			switch (st) {
				case BST_SUCCESS: {
					child->status(in, st);
					ctx->status(st);
					return BST_SUCCESS;
				}
				case BST_RUN: {
					return st;
				}
				case BST_FAILURE: {
					child->reset(in, out);
					return BST_RUN;
				}
				case BST_IDLE:
				default: break;
			}
			return BST_RUN;
		}
	};
}

namespace bt {
	class wait_context final : public default_context {
	public:
		nav::double_t start{};
		nav::double_t pass{};
		nav::double_t due{};
	protected:
		void clean() override;
	};

	template <typename BaseT>
	class btree_wait : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
		nav::double_t duration{};
		nav::vector2 durationRange{};
		time_scale_type durationScaleType{};
	protected:
		void on_start(input_type* in, output_type*) const final {
			wait_context* ctx = base_type::template get_context<wait_context>(in);
			if (in->mode == process_mode::kQuick) {
				ctx->status(BST_SUCCESS);
				return;
			}
			const nav::float_t d = in->nextd();
			ctx->due = duration + durationRange.x + (durationRange.y - durationRange.x) * d;
			if (ctx->due == nav::double_t(0)) {
				ctx->status(BST_SUCCESS);
				return;
			}
			ctx->status(BST_RUN);
			ctx->pass = 0;
			ctx->start = in->now.time;
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type* in, output_type*) const final {
			wait_context* ctx = base_type::template get_context<wait_context>(in);
			const time_scale ts = in->timescale();
			const nav::double_t now = in->now.time;
			nav::float_t delta;
			ts.calc_time(now, durationScaleType, ctx->start, ctx->pass, delta);
			if (ctx->pass > ctx->due) {
				return BST_SUCCESS;
			}
			return BST_RUN;
		}
	};
}

namespace bt {
	template <typename BaseT>
	class btree_random : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
		nav::float_t value{};
		value_compare_type method{};
	protected:
		void on_start(input_type* in, output_type*) const final {
			default_context* ctx = base_type::template get_context<default_context>(in);
			const auto rand = in->nextd();
			const bool ok = value_compare(rand, value, method);
			ctx->status(ok ? BST_SUCCESS : BST_FAILURE);
		}

		void on_stop(input_type*, output_type*) const final {
		}

		btree_status on_update(input_type* in, output_type*) const final {
			const default_context* ctx = base_type::template get_context<default_context>(in);
			return ctx->status();
		}
	};
}
