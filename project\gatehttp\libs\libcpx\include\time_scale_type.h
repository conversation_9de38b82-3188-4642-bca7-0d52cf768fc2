#pragma once
#include <fp_math.hpp>

namespace bt {
	enum time_scale_type {
		kAnimation = 1 << 0,
		kBulletFly = 1 << 1
	};

	struct time_scale {
		DEFAULT_MOVE_COPY(time_scale)
		explicit time_scale(nav::float_t a = 1, nav::float_t b = 1);
		~time_scale();
		nav::float_t c0{1};
		nav::float_t c1{1};
		time_scale operator +(const time_scale&) const;
		time_scale operator -(const time_scale&) const;
		time_scale operator -() const;
		time_scale operator *(const time_scale&) const;
		time_scale operator *(nav::float_t) const;
		time_scale operator /(const time_scale&) const;
		time_scale operator /(nav::float_t) const;
		bool operator ==(const time_scale&) const;
		bool operator !=(const time_scale&) const;
		void calc_time(nav::double_t now, time_scale_type type,
		               nav::float_t& prev,
		               nav::float_t& total,
		               nav::float_t& delta) const;
		nav::float_t scale(time_scale_type) const;
		void scale(time_scale_type, nav::float_t);
		static time_scale min(const time_scale&, const time_scale&);
		static time_scale max(const time_scale&, const time_scale&);
		static const time_scale one;
		static const time_scale zero;
	};

	struct time_scale_data {
		time_scale scale{1, 1};
		nav::double_t due{};
	};
}
