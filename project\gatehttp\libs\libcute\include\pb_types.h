#pragma once
#include "raw_string.h"

namespace pb {
	typedef int32_t int32_t;
	typedef uint32_t uint32_t;
	typedef int64_t int64_t;
	typedef uint64_t uint64_t;
	typedef double double_t;
	typedef float float_t;
	typedef bool bool_t;
	typedef sys::raw_string string_t;
	typedef sys::raw_string bytes_t;
}

class pb_abstract_object {
public:
	virtual int type() const = 0;
	pb_abstract_object() = default;
	virtual ~pb_abstract_object() = default;
};
