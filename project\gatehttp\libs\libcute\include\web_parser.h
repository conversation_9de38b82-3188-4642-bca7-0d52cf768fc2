#pragma once
#include <string>
#include "http_def.h"
#include "http_parser.h"

namespace net {
	struct web_message;

	struct web_parser {
		typedef int (*web_parse_callback)(web_parser*, const char*, size_t);
		DELETE_MOVE_COPY(web_parser)
		virtual ~web_parser();
		virtual size_t parse(const char*, size_t) = 0;
		virtual int error() const = 0;
		int invoke(const char* = nullptr, size_t = 0);
		http_phase state{};
		web_message* msg{};
		void* data{};
		web_parse_callback cb{};

	protected:
		web_parser() = default;
	};

	struct web_parser11 final : web_parser {
		DELETE_MOVE_COPY(web_parser11)
		web_parser11();
		~web_parser11() override;
		size_t parse(const char*, size_t) override;
		void handle_header();
		bool done() const;
		int error() const override;
		size_t nread()const;
		std::string errmsg(int = 0) const;
		int flags{};

		struct {
			std::string field{};
			std::string value{};

			void clear() {
				field.clear();
				value.clear();
			}
		} header;

	private:
		http_parser _parser;
	};
}
