#pragma once
#include "net_stream_writer.h"
#include "pb_stream.h"

namespace net {
	class smtp_peer;

	class smtp_write_stream final : public net_stream_writer {
	public:
		int write(const char*, size_t) override;
		int write(const recycle_stream&) override;
		int flush() override;
		void get_uvbuf(uv_buf_t*) override;
		size_t size() override;
		void on_write_done() override;
		int ping(time_t) override;
		int pong(time_t) override;

	private:
		pb_stream _buf{};
	};
}
