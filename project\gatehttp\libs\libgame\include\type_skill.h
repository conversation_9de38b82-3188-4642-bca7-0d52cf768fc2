#pragma once
#include <btree_list.h>
#include <libcute.h>
#include <type_data.h>
#include <tvo_enums.h>
#include <range.h>
#include <thing_group.h>
#include "dlink_make.h"
#include "type_skill_override.h"
#include "nav_struct.h"
#include "skill_limit.h"
#include "typedata_wrapper_libgame.h"
#include <property_group.h>

namespace ds {
	class property_group;
}

namespace tvo {
	class type_buff;
	class type_fxsource;
	class type_skill_chip;

	class type_skill final : public type_data {
	public:
		static void awake_all(const awake_context_t&);
		void awake(const awake_context_t &) override;
		type_fxsource* fxsource() const;
		type_buff* buff() const;
		cpx::btree_list<skill::skill_limit> limits;
		UPLEVEL_LINK_FIELDS(type_skill)

	public:
		using open_mode_t = skill_open_mode_t;
		using locate_mode_t = skill_locate_mode_t;
		tvo::type_buff * _buff{}; 
		type_fxsource * _fxsource{};

		enum tag_t {
		};

		TYPE_SKILL_WRAPPER()
	};
}
