#pragma once
#include  <libcute.h>
#include "btree_status.h"

namespace bt {
	class bt_context {
	public:
		DELETE_MOVE_COPY(bt_context)
		virtual btree_status status() const = 0;
		virtual void status(btree_status v) = 0;
		virtual void reset() = 0;
		virtual void stop() = 0;
		virtual ~bt_context();
	protected:
		bt_context();
	};

	class default_context : public bt_context {
	public:
		btree_status status() const override;
		void status(btree_status v) override;
		void reset() override;
		void stop() override;
	protected:
		virtual void clean();
	private:
		void clean_up();
		btree_status _status{};
		bool _dirty{};
	};

	class index_context final : public default_context {
	public:
		size_t index{};
		void clean() override;
	};
}
