#pragma once
#include <string>
#include "pb.h"
#include "service_struct.h"
#if defined NDEBUG
#define PROC_STDOUT_FLAGS (UV_IGNORE)
#else
#define PROC_STDOUT_FLAGS (UV_INHERIT_FD)
#endif
#define  PROC_STDIN_FLAGS (uv_stdio_flags)(UV_CREATE_PIPE | UV_READABLE_PIPE)
/*

*/
#define PROC_SIGNAL(XX)			\
XX( 0  ,	stop 			)	\
XX( 1  ,	tcp_in 			)	\
XX( 2  ,	shutdown 		)	\
XX( 3  ,	master_close 	)	\
XX( 4  ,	hprofile_start 	)	\
XX( 5  ,	hprofile_stop 	)	\
XX( 6  ,	hprofile_dump 	)	\
XX( 7  ,	cprofile_start 	)	\
XX( 8  ,	cprofile_stop 	)	\
XX( 9  ,	service_async 	)	\
XX( 10  ,	external		)
/*
*/
namespace proc {
	class service_slave;

	enum signal_code {
#define XX(num, key)PSIG_##key = (num),
		PROC_SIGNAL(XX)
#undef XX
	};

	signal_code str_to_signal(const std::string&);
	const char* signal_to_str(signal_code);
	signal_code slice_to_signal(pb_slice);
	pb_slice signal_to_slice(signal_code);
}

namespace proc {
	struct signal_t {
		signal_t(service_slave*, signal_code);
		service_slave* slave;
		signal_code type;
		pb_slice buf{};
	};

	struct child_config {
		id_64 id{};
		int type{};
		int sub{};
		std::string name;
		std::string exepath;
		service_role role{};
		sys::service_conf_retry retry{};
	};

	struct child_health {
		int cnt_reboot{};
		int cnt_dump{};
		time_t shuttime{};
	};

	struct reboot_info {
		child_config config{};
		child_health health{};
		time_t reboot{};
	};
}
#ifdef DEBUG
#ifdef WIN32
#define ITER_SEARCH_EXEPATH (1)
#endif
#endif

namespace proc {
	int parse_exepath(const std::string& exe, std::string& to, std::string& errmsg);
	int cache_exefile(const std::string& exe); 
}
