#pragma once
#include <map>
#include <mutex>
#include <vector>
#include "libcute.h"
#include "route_t.h"
#define VIRTUAL_MACHNE_SIZE (64)

namespace sys {
	struct cluster_event;

	class consistent_ring {
	public:
		using hash_type = size_t;
		DEFAULT_MOVE_COPY(consistent_ring)
		explicit consistent_ring(uint32_t vs = VIRTUAL_MACHNE_SIZE);
		int find(hash_type, route_t&);

		template <typename TValue, std::enable_if_t<
			          std::is_integral_v<TValue> ||
			          std::is_enum_v<TValue>> * = nullptr>
		int find(const TValue h, route_t& out) {
			return find(static_cast<hash_type>(h), out);
		}

		template <typename TValue, typename Hty = std::hash<TValue>,
		          std::enable_if_t<
			          !std::is_integral_v<TValue> &&
			          !std::is_enum_v<TValue>> * = nullptr>
		int find(const TValue& h, route_t& out) {
			return find(Hty().operator()(h), out);
		}

		int find(const char* h, route_t& out) {
			return find(std::string(h), out);
		}

		int s_type{};
		void recv(const std::vector<cluster_event>&);
		void emplace(const route_t&);
		void erase(const route_t&);
		void registed_route(std::vector<route_t> &);
		void clear();
		~consistent_ring();
	private:
		void internal_emplace(const route_t&);
		void internal_erase(const route_t&);
		std::map<uint64_t, route_t> _map{};
		uint32_t _vsize;
		std::mutex _mutex{};
	};
}
