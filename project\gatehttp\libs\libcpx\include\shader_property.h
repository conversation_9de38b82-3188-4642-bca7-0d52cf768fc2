#pragma once
#ifndef SHADER_PROPERTY_H
#define SHADER_PROPERTY_H
#include <string>
#define SHADER_BUILTIN_VALUE(XX)						\
XX(tint_color			, "_TintColor"				)	\
XX(color				, "_Color"					)	\
XX(buff_color			, "_BuffColor"				)	\
XX(emission				, "_Emission"				)	\
XX(hit_ceoff			, "_hitCeoff"				)	\
XX(dissolve_threshold	, "_DissolveThreshold"		)	\
/*---------------------------------------------------*/ \
XX(skin_layer			, "_SKIN"					)	\
XX(main_tex				, "_MainTex"				)	\
XX(bump_tex				, "_BumpTex"				)	\
XX(noise_tex			, "_NoiseTex"				)	\
XX(control_tex			, "_ControlTex"				)	\
XX(illumin_tex			, "_IlluminTex"				)	\
XX(alpha_tex			, "_AlphaTex"				)	\
XX(mask_tex				, "_MaskTex"				)	\
XX(offset				, "_Offset"					)	\
XX(raw_background_grab	, "_RawBackgroundGrab"		)	\
XX(cut_threshold		, "_cutThreshhold"			)	\
/*---------------------------------------------------*/ \
XX(zoffset				, "_ZOffset"				)	\
XX(source				, "_Source"					)	\
XX(dest					, "_Dest"					)	\
XX(ztest				, "_ZTest"					)	\
XX(zwrite				, "_ZWrite"					)	\
/*---------------------------------------------------*/ \
XX(disper_mask			, "_DisperMask"				)	\
XX(disper_gradient		, "_DisperGradient"			)	\
XX(disper_distance		, "_DisperDistance"			)	\
XX(disper_tile			, "_DisperTile"				)	\
/*---------------------------------------------------*/ \
XX(src_color			, "_SrcColor"				)	\
XX(dst_color			, "_DstColor"				)	\
XX(ratio				, "_Ratio"					)
/*shader property keys---------------------------*/
#define SHADER_BUILTIN_KEY(XX) \
XX(k_sample_screen		, "SAMPLE_SCREEN"			)	\
XX(k_cutoff				, "CUT_OFF"					)	\
XX(k_transparent		, "TRANSPARENT"				)	\
XX(k_occlusion			, "OCCLUSION_ON"			)	\
XX(k_light_always		, "Always"					)	\
XX(k_dissolve_on		, "DISSOLVE_ON"				)	\
XX(k_border_off			, "BORDER_OFF"				)	\
XX(k_layer_disper		, "LAYER_DISPER"			)	\
XX(k_layer_color		, "LAYER_LIGHTCOLOR"		)	\
XX(k_shadowcast			, "SHADOW_CASTER"			)	\
XX(k_visible			, "VISIBLE"					)	

namespace cpx {
	class shader_property {
	public:
#define XX(name, string) static const int name;
		SHADER_BUILTIN_VALUE(XX)
		SHADER_BUILTIN_KEY(XX)
#undef XX
		static int property_id(const std::string&);
	};
}
#endif
