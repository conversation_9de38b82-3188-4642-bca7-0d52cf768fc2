#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "property_group.h"
#include "thing_group.h"
#include <nav_struct.h>
#include <range.h>

namespace tvo {
	class type_hero_level;

	struct level_queue_t {
		DELETE_MOVE_COPY(level_queue_t)
		level_queue_t();
		~level_queue_t();
		const type_hero_level* find(int level) const;
		const type_hero_level* find_exp(int64_t) const;
		int method{};
		int career{};
		int mode{};
		bool linear{};
		std::vector<type_hero_level*> list{};
		type_hero_level* head{};
		type_hero_level* tail{}; 
	};

	struct level_group_t {
		int method{};
		/*type_currency.id the level use*/
		int cost{};
		int mode{};
		std::string name{};
		DELETE_MOVE_COPY(level_group_t)
		sys::hashmapx<int, level_queue_t*> careers;
		level_group_t();
		~level_group_t();
	};

	struct level_pair {
		int method{};
		std::string name;
	};

	class type_hero_level final : public type_data {
	public:
		DELETE_MOVE_COPY(type_hero_level)
		type_hero_level* next{};
		type_hero_level* prev{};
		type_hero_level* tail{};
		type_hero_level* head{};

		//@see TYPE_HERO_LEVEL_WRAPPER() in typeclass_define.h
		enum method_t {
			kLevel,
		};

		//@see TYPE_HERO_LEVEL_WRAPPER() in typeclass_define.h
		enum mode_t {
			kAuto,
			kStrange,
		};

		//@see TYPE_HERO_LEVEL_WRAPPER() in typeclass_define.h
		enum property_mode_t {
			kPropertyReplace,
			kPropertyAdd,
		};

		type_hero_level();
		static void awake_all(const awake_context_t&);
		~type_hero_level() override;
		static const sys::hashmapx<int, level_group_t*>& all_levels();
		static void set_methods(const std::vector<level_pair>& methods);
		static std::vector<level_pair> get_methods();
		static int get_method(const std::string& k);
		static std::string get_method_name(int method);
		static level_queue_t* get_level_queue(int method, int career);
		TYPE_HERO_LEVEL_WRAPPER()
	};
}
