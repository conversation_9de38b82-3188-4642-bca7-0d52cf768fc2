#pragma once
#define SKILL_CHIP_OVERRIDE_FIELD(XX) \
XX(0, fxSourceId		, fxsource_id		, ds::int32_t				)	\
XX(1, cd				, cd				, ds::float_t				)	\
XX(2, overlap			, overlap			, ds::int32_t				)	\
XX(3, tag  				, tag				, ds::int32_t				)	\
XX(4, breakTags			, break_tags		, std::vector<ds::int32_t>	)	\
XX(5, prerequestTime	, prerequest_time	, ds::range					)
#define SKILL_CHIP_OVERRIDE_MAX (6)
/*-----------------------------getter func define-----------------------------*/
#define SKILL_CHIP_OVERRIDE_DEFI(num, name, getter, f_type )\
f_type get_##getter(weapon_mode_t m)const;
/*-----------------------------getter func declare-----------------------------*/
#define SKILL_CHIP_OVERRIDE_DECL(num, name, getter, f_type)		\
f_type type_skill::get_##getter(const weapon_mode_t m) const{	\
	auto* c = find_chip(m);										\
	if(!c ||!c->override_fields[num] ){ return name; }			\
	return c->name;												\
}
/*-----------------skill chip config checker----------------------------*/
#define SKILL_CHIP_CONFIG_CHECK(num, name,getter, f_type)			\
override_fields[num] =jo.contains(#name) ? 1: 0	;
#define SKILL_FXSOURCE_FIELD(XX) \
XX(0 , element		, element			, ds::int32_t					)\
XX(1 , rangeMode	, range_mode		, ds::int32_t					)\
XX(2 , ceoff 		, ceoff				, ds::float_t					)\
XX(3 , cost			, cost				, ds::property_group*			)\
XX(4 , fx			, fx				, std::string					)\
XX(5 , fxch			, fxch				, std::string					)\
XX(6 , fxhh			, fxhh				, std::string					)\
XX(7 , fxbh			, fxbh				, std::string					)\
XX(8 , fxsh			, fxsh				, std::string					)\
XX(9 , fxdh			, fxdh				, std::string					)
#define SKILL_GET_FXSOURCE_FIELD_DEFI(num, name, getter, ftype) \
ftype get_##getter(weapon_mode_t w)const;
#define SKILL_GET_FXSOURCE_FIELD_DECL(num, name, getter, ftype)	\
ftype type_skill::get_##getter(const weapon_mode_t m) const{	\
	auto * f = get_fxsource(m);									\
	return f->name;												\
};
/*---------------------------------weapon mode enum---------------------------------*/
 