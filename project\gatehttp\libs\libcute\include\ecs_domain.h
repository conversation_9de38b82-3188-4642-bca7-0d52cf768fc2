#pragma once
#include "hashmapx.h"
#include "pb.h"

namespace ecs {
	class classinfo_t;

	class domain_t {
	public:
		DELETE_MOVE_COPY(domain_t)
		domain_t(pb_state*, uint32_t v);
		pb_state* state() const;
		uint32_t version() const;
		classinfo_t* find(int class_id) const;
		classinfo_t* find(const std::string&) const;
		using const_iterator = sys::hashmapx<int, classinfo_t*>::const_iterator;
		const_iterator begin() const;
		const_iterator end() const;
		void free_mem() const;
		~domain_t();
	private:
		pb_state* _pb;
		uint32_t _ver;
		sys::hashmapx<int, classinfo_t*> _classes{};
	};
}
