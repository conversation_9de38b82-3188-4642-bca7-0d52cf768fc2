#pragma once
#include "libcute.h"
#include "usr_worker.h"

namespace tvo {
	struct extra_server;
}

namespace dvo {
	class worker_extra final : public usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_extra)
		void start() override;
		void stop() override;
		void update(const update_context*) override;
		void on_notifiction(const worker_notification*) override;

	private:
		void on_load();
		void check_version();
		void send_notification() const;
		void send_client(const tvo::extra_server&) const;
		time_t _time{};
		uint32_t _version{};
	};
}
