#pragma once
namespace cpx {
	template <size_t BLockSize, size_t Length>
	struct stack_mem_array {
		static constexpr size_t entry_size = Length;
		DELETE_MOVE_COPY(stack_mem_array)

		stack_mem_array(): _entries{} {
		}

		~stack_mem_array() = default;

		template <typename T, typename ... Args>
		T* obtain(Args&&... args) {
			static_assert(sizeof(T) <= BLockSize);
			for (size_t i = 0; i < entry_size; ++i) {
				entry_t* e = &_entries[i];
				if (e->token) {
					continue;
				}
				e->token = true;
				T* v = new(e->mem)T(std::forward<Args>(args) ...);
				return v;
			}
			throw_e("no available entry found");
			return nullptr;
		}

		template <typename T>
		void release(T* v) {
			if (!v)return;
			void* ptr = v;
			if (ptr < &_entries || ptr >= &_entries + sizeof(_entries)) {
				throw_e("is not pooling object");
			}
			v->~T();
			auto* e = static_cast<entry_t*>(ptr);
			e->token = false;
		}

	private:
		struct entry_t {
			char mem[BLockSize]{0};
			bool token{};
		};

		entry_t _entries[Length];
	};
}
