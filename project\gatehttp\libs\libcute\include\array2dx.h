#pragma once
#include <iterator>
#include "sys_exception.h"

namespace sys {
	struct rect_window {
		int x{};
		int y{};
		int w{};
		int h{};
	};

	template <typename T>
	class array2dx {
	public:
		array2dx() = default;

		array2dx(T* list, const int row, const int col): _array(list),
		                                                 _row(row),
		                                                 _col(col),
		                                                 _size(row * col) {
		}

		array2dx(const int row, const int col): _row(row), _col(col), _size(row * col) {
			if (_size > 0) {
				_array = new T[_size]();
			} else {
				_array = nullptr;
			}
		}

		array2dx(const array2dx& rhs) noexcept {
			if (&this != rhs) {
				copy(rhs);
			}
		}

		array2dx(array2dx&& rhs) noexcept {
			if (&rhs != this) {
				swap(std::forward<array2dx>(rhs));
			}
		}

		array2dx& operator =(const array2dx& rhs) {
			if (&rhs != this) {
				copy(rhs);
			}
			return *this;
		}

		array2dx& operator =(array2dx&& rhs) noexcept {
			if (this != &rhs) {
				swap(std::forward<array2dx>(rhs));
			}
			return *this;
		}

		void swap(array2dx&& rhs) noexcept {
			std::swap(rhs._row, _row);
			std::swap(rhs._col, _col);
			std::swap(rhs._size, _size);
			std::swap(rhs._array, _array);
		}

		void copy(const array2dx& rhs) noexcept {
			_row = rhs._row;
			_col = rhs._col;
			_size = rhs._size;
			if (_array) {
				delete[] _array;
				_array = nullptr;
			}
			if (_size <= 0) {
				return;
			}
			_array = new T[_size]();
			if (rhs._array) {
				for (int i = 0; i < _size; ++i) {
					_array[i] = rhs._array[i];
				}
			}
		}

		~array2dx() {
			if (_array) {
				delete [] _array;
				_array = nullptr;
			}
			_size = 0;
			_row = 0;
			_col = 0;
		}

		int size() const { return _size; }

		void clear() {
			if (!_array) {
				return;
			}
			for (int i = 0; i < _size; ++i) {
				_array[i] = {};
			}
		}

		void resize(const int r, const int c) {
			if (r < 0 || c < 0) {
				throw_e("bad arguments");
			}
			if (r == _row && c == _col) {
				return;
			}
			_row = r;
			_col = c;
			const auto s = r * c;
			if (_size == s) {
				return;
			}
			_size = s;
			delete _array;
			_array = _size > 0 ? new T[_size] (): nullptr;
		}

		void resize(const int s) {
			const int r = s / _col;
			resize(r, _col);
		}

		int col() const { return _col; }
		int row() const { return _row; }

		void limit(int& r, int& c) const {
			if (r >= _row) r = _row - 1;
			if (r < 0)r = 0;
			if (c >= _col) c = _col - 1;
			if (c < 0)c = 0;
		}

		T at(const int row, const int col) const {
			const int offset = row * _col + col;
			if (offset < 0 || offset >= _size) {
				return T();
			}
			return _array[offset];
		}

		T& at(const int row, const int col) {
			const int offset = row * _col + col;
			if (offset < 0 || offset >= _size) {
				static T basic{};
				return basic;
			}
			return _array[offset];
		}

		T* ptr(const int r, const int c) {
			if (!constrain(r, c)) {
				return nullptr;
			}
			const int offset = r * _col + c;
			if (offset < 0 || offset >= _size) {
				return nullptr;
			}
			return &_array[offset];
		}

		const T* ptr(const int r, const int c) const {
			if (!constrain(r, c)) {
				return nullptr;
			}
			const int offset = r * _col + c;
			if (offset < 0 || offset >= _size) {
				return nullptr;
			}
			return &_array[offset];
		}

		bool tryget(const int row, const int col, T& to) const {
			if (!constrain(row, col)) {
				return false;
			}
			const int offset = row * _col + col;
			if (offset < 0 || offset >= _size) {
				return false;
			}
			to = _array[offset];
			return true;
		}

		T operator [](const int index) const {
			if (index < 0 || index >= _size) {
				return T{};
			}
			return _array[index];
		}

		T& operator [](const int index) {
			if (index < 0 || index >= _size) {
				static T basic{};
				return basic;
			}
			return _array[index];
		}

		bool constrain(const int r, const int c) const {
			return r >= 0 && r < _row && c >= 0 && c < _col;
		}

		bool constrain(const rect_window r) const {
			if (r.x < 0)return false;
			if (r.y < 0)return false;
			if (r.x + r.w > _col)return false;
			if (r.y + r.h > _row)return false;
			return true;
		}

	private:
		T* _array{};
		int _row{};
		int _col{};
		int _size{};
	};
}
