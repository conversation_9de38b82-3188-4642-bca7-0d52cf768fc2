#pragma once
#include <libcute.h>
#include <type_cast.h>
#include <ref_ptr.h>

namespace cpx {
	struct time_vo;
}

namespace dvo {
	class player_vo;
	abstract uni_task {
	public:
		TYPE_CAST_BASE_DEFINE(uni_task)

	public:
		CUTE_REF_PTR_CLASS_DECL(uni_task)

	public:
		DELETE_MOVE_COPY(uni_task)

		enum status_t {
			kIdle,
			kRunning,
			kDone,
			kAbort
		};

		virtual void abort() = 0;
		virtual int id() const = 0;
		virtual int error() const = 0;
		virtual const std::string& errmsg() const;
		virtual status_t update(const cpx::time_vo&) =0;
		virtual status_t status() const = 0;

	protected:
		uni_task();
	};

	template <typename T>
	using user_task_impl = sys::class_type_impl<T, uni_task>;
}
