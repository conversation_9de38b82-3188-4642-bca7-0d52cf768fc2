#pragma once
#include "cute_manager.h"
#include "thread_locker.h"

namespace sys {
	template <typename TDerive>
	class asyncx_base {
	public:
		DELETE_MOVE_COPY(asyncx_base)
		asyncx_base() = default;
		virtual ~asyncx_base() = default;

		int start(uv_loop_t* l) {
			if (uv_is_active(reinterpret_cast<uv_handle_t*>(&_async))) {
				return 0;
			}
			_async.data = this;
			return uv_async_init(l, &_async, async_cb);
		}

		int start() {
			return start(cute_mainloop());
		}

		void stop() {
			auto* h = reinterpret_cast<uv_handle_t*>(&_async);
			if (!uv_is_active(h)) {
				return;
			}
			uv_close(h, nullptr);
		}

		void trigger() {
			if (!uv_is_active(reinterpret_cast<uv_handle_t*>(&_async))) {
				log_error("async is not active");
				return;
			}
			uv_async_send(&_async);
		}

		void* data{};

	private:
		void invoke() const {
			const auto* self = static_cast<const TDerive*>(this);
			self->on_invoke();
		}

		static void async_cb(uv_async_t* t) {
			const auto* x = static_cast<asyncx_base*>(t->data);
			x->invoke();
		}

		uv_async_t _async{};
	};

	class asyncx final : public asyncx_base<asyncx> {
	public:
		DELETE_MOVE_COPY(asyncx)
		using delegate_call = void(*)(const asyncx*);
		friend class asyncx_base<asyncx>;

		explicit asyncx(const delegate_call fn) : _dcall(fn) {
		}

		~asyncx() override = default;

	private:
		void on_invoke() const {
			if (_dcall) {
				_dcall(this);
			}
		}

		delegate_call _dcall;
	};

	template <typename C>
	class asyncxm final : public asyncx_base<asyncxm<C>> {
	public:
		DELETE_MOVE_COPY(asyncxm)
		using member_call = void(C::*)(const asyncxm*);
		friend class asyncx_base<asyncxm<C>>;

		explicit asyncxm(C* obj, const member_call fn) : _inst(obj), _call(fn) {
		}

		~asyncxm() override = default;

	private:
		void on_invoke() const {
			if (_inst && _call) {
				(_inst->*_call)(this);
			}
		}

		C* _inst;
		member_call _call;
	};
}
