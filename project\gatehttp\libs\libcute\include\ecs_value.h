#pragma once
namespace ecs {
	struct value_t;
}

#include <cstdint>
#include <string>
#include <vector>
#include "ecs_array.h"
#include "libcute.h"
#include "libcutex.h"
#include "pb.h"
#include "raw_string.h"
#include <ecs_traits.h>

namespace ecs {
	template <typename T>
	inline constexpr bool is_floating_point_v = is_floating_point<T>::value;
	class object_t;
	class array_t;
	class iparser_t;

	struct value_t {
		friend classinfo_t;
		friend array_t;
		friend object_t;
		DELETE_MOVE_COPY(value_t)
		int dirty{};
		iparser_t* parser{};

		union {
			double real;
			int64_t i64;
		};

		template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
		operator T() const {
			return static_cast<T>(i64);
		}

		template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
		value_t& operator =(const T v) {
			i64 = static_cast<int64_t>(v);
			return *this;
		}

		template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
		void get_to(T& v) const {
			v = static_cast<T>(i64);
		}

		template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
		void set_to(T v) {
			i64 = static_cast<int64_t>(v);
		}

		template <typename T, std::enable_if_t<is_floating_point_v<T>>* = nullptr>
		operator T() const {
			return static_cast<T>(real);
		}

		template <typename T, std::enable_if_t<is_floating_point_v<T>>* = nullptr>
		value_t& operator =(const T v) {
			real = static_cast<double>(v);
			return *this;
		}

		template <typename T, std::enable_if_t<is_floating_point_v<T>>* = nullptr>
		void get_to(T& v) const {
			v = static_cast<T>(real);
		}

		template <typename T, std::enable_if_t<is_floating_point_v<T>>* = nullptr>
		void set_to(T v) {
			real = static_cast<double>(v);
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<sys::raw_string, T>>* = nullptr>
		void get_to(T& v) const {
			v = *this;
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<T, sys::raw_string>>* = nullptr>
		void set_to(T v) {
			*this = v;
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<object_t, T>>* = nullptr>
		void get_to(T& v) {
			object_t o = *this;
			v = o;
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<T, object_t>>* = nullptr>
		void set_to(T v) {
			const object_t o = v;
			*this = o;
		}

		template <typename T, std::enable_if_t<
			          std::is_integral_v<T> ||
			          std::is_floating_point_v<T> ||
			          std::is_convertible_v<sys::raw_string, T> ||
			          std::is_convertible_v<object_t, T>
		          >* = nullptr>
		void get_to(std::vector<T>& to) const {
			const auto* self = list();
			to.clear();
			to.reserve(self->size());
			for (size_t i = 0; i < self->size(); ++i) {
				to.emplace_back(self->at<T>(i));
			}
		}

		template <typename T, std::enable_if_t<
			          std::is_integral_v<T> ||
			          std::is_floating_point_v<T> ||
			          std::is_convertible_v<T, sys::raw_string> ||
			          std::is_convertible_v<T, object_t>
		          >* = nullptr>
		void set_to(const std::vector<T>& src) {
			auto* self = list();
			self->clear();
			self->reserve(src.size());
			for (const auto& v : src) {
				self->emplace(v);
			}
		}

		value_t& copy_from(const value_t&, const fieldinfo_t*);
		//value_t& move_from(value_t&&, const fieldinfo_t*);
		operator std::string() const;
		value_t& operator =(const std::string&);
		operator sys::raw_string() const;
		value_t& operator =(const sys::raw_string&);
		operator ecs::object_t() const;
		operator pb_slice() const;
		value_t& operator =(const pb_slice&);
		value_t& operator =(const object_t&);
		array_t* list() const;
		void set_dirty(int k, bool v);
		bool is_dirty(int) const;
	private:
		value_t();
		~value_t();
	};
}

namespace ecs {
	interface iparser_t {
	public:
		DELETE_MOVE_COPY(iparser_t)
		virtual int to_ecs(const object_t&) const = 0;
		virtual int from_ecs(const object_t&) = 0;
		virtual const char* ecs_name() const noexcept =0;
		iparser_t();
		virtual ~iparser_t();
	};
}
