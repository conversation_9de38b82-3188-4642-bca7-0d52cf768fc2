//gen by cpp_autogen automatically, don't modify it!
#pragma once
#ifndef CPX_ACTION_BUILTIN_TYPES_H
#define CPX_ACTION_BUILTIN_TYPES_H

#define CPX_ACTION_BUILTIN_TYPES(XX)\
XX(1419543335,cpx_act_block_on_parameter,"ai:blockByParamSet")\
XX(1558363373,cpx_act_delay,"actionDelay")\
XX(199312851,cpx_act_sub_control,"actionSubControl") \
XX(-777406281,cpx_act_nothing,"actionNothing")\
XX(-472136580,cpx_act_rewind_parameter,"actionRewindParam")\
XX(248364165,cpx_act_sequence,"action:sequence")\
XX(70218443,cpx_act_parallel,"action:parallel")\
XX(90388643,cpx_act_selector,"action:selector")\
XX(-1144926262,cpx_act_parallel_selector,"action:parallelSelector")\
XX(-1341717733,cpx_act_set_inited,"actionSetInited")\
XX(807188332,cpx_act_set_parameter,"paramSetter")\
XX(-1344376568,cpx_act_repeat,"deco:repeat")\
XX(5648498,cpx_act_until_success,"deco:untilSuccess")\
XX(786244089,cpx_act_until_failure,"deco:untilFailure")\

#include "cpx_act_block_on_parameter.h"
#include "cpx_act_delay.h"
#include "cpx_act_sub_control.h"
#include "cpx_act_nothing.h"
#include "cpx_act_rewind_parameter.h"
#include "cpx_act_sequence.h"
#include "cpx_act_parallel.h"
#include "cpx_act_selector.h"
#include "cpx_act_parallel_selector.h"
#include "cpx_act_set_inited.h"
#include "cpx_act_set_parameter.h"
#include "cpx_act_repeat.h"
#include "cpx_act_until_success.h"
#include "cpx_act_until_failure.h"


#endif //CPX_ACTION_BUILTIN_TYPES_H
