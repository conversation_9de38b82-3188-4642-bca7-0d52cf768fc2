#pragma once
#include <type_data.h>
#include "btree_list.h"
#include "typedata_wrapper_libgame.h"
#include "thing_group.h"
#include "activity_tree.h"

namespace tvo {
	class type_activity final : public type_data {
	public:
		enum type_t {
			kMain,
			kSub
		};

		static void awake_all(const awake_context_t&);
		void awake(const awake_context_t&) override;
		cpx::btree_list<activity::activity_tree> actions{};
		sys::hashmapx<activity::node_key, int> node_keys{};
		TYPE_ACTIVITY_WRAPPER()
	};
}
