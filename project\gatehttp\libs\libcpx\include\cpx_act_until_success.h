#pragma once
#include <cpx_action.h>
#include "cpx_action_builtin_wrapper.h"
#include <sys_exception.h>
#include "cpx_act_until.h"

namespace cpx {
	class cpx_act_until_success final : public cpx_act_until {
	public:
		DELETE_MOVE_COPY(cpx_act_until_success)
		CPXA_CPX_ACT_UNTIL_SUCCESS_WRAPPER()
		cpx_act_until_success();
		~cpx_act_until_success() override;
	protected:
		return_check_t check(bt::btree_status) const override;
	}; // endof class
} // endof namespace
