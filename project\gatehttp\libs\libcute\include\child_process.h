#pragma once
#include <uv.h>
#include "libcute.h"
#include "proc_signal.h"
#include <ref_ptr.h>

namespace proc {
	class child_process;
	typedef std::function<void ()> child_stop_cb;

	enum child_status {
		CHILD_IDLE,
		CHILD_RUN,
		CHILD_STOP
	};

	enum child_event_type {
		EVT_CHILD_DUMP,
		EVT_CHILD_CLOSE,
	};

	struct child_exit {
		child_process* process{};
		child_event_type type{};
		int64_t code{};
		int32_t sig{};
	};
}

namespace proc {
	class service_proc_list;

	class child_process {
	public:
		DELETE_MOVE_COPY(child_process)
		typedef void (*delay_cb)(child_process*, time_t);
		explicit child_process(uv_loop_t*);
		~child_process();
		id_64 id() const;
		int type() const;
		const std::string& name() const;
		const std::string& exename() const;
		time_t exemtime() const; 
		int spawn(child_config&&);
		int send(const std::string&);
		int send(signal_code);
		int send(uv_tcp_t*);
		void stop(const child_stop_cb&);
		void attach(service_proc_list*);
		void detach();
		child_config config{};
		child_health health{};
		child_status status{};
		int64_t exitcode{};
		int exitsig{};
	private:
		friend class service_proc_list;
		void finalize();
		void dispose() const;
		void close_pipe();
		static void on_process_exit(uv_process_t*, int64_t code, int signal);
		static void on_pipe_close(uv_handle_t*);
		uv_loop_t* _loop{};
		uv_process_t _process{};
		uv_process_options_t _options{};
		uv_pipe_t _pipe{};
		child_stop_cb _closecb{};
		service_proc_list* _parent{};
		std::string _exepath{};
		time_t _exemtime{};
		void invoke(child_event_type, int64_t = 0, int = 0);
	};
}
