#pragma once
#include "service_common_code_wrapper.h"
#include "libcute.h"
#include "message_data.h"
#include "ref_ptr.h"

namespace net {
	class net_peer;
}

using MessageData = message_data;

class message_collection final : public pb_base {
public:
	int send(const pb_base*, const ref_ptr<net::net_peer>& to);
	int send(const ref_ptr<net::net_peer>& to);
	int encode(const pb_base*, pb_stream&);

	template <typename T, typename Callback,
	          std::enable_if_t<std::is_invocable_v<Callback, T*, message_data*, pb_slice>>* = nullptr>
	void read(const pb_slice slice, Callback cb, T* ctx) {
		pbd_slice s{slice, slice.ptr};
		uint32_t tag;
		while (pb_readvarint32(&s.base, &tag)) {
			const int fnumber = pb_gettag(tag);
			switch (fnumber) {
				case kSceneId: {
					pbd_out o;
					if (pb_readvarint64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					sceneId = o.u64;
					break;
				}
				case kRequestKey: {
					pbd_out o{};
					if (pb_readvarint64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					requestKey = o.u32;
					break;
				}
				case kUsrId: {
					pbd_out o;
					if (pb_readvarint64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					usrId = o.u64;
					break;
				}
				case kTime: {
					pbd_out o;
					if (pb_readfixed64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					*static_cast<uint64_t*>(static_cast<void*>(&time)) = o.u64;
					break;
				}
				case kDatas: {
					message_data md{};
					pbd_slice sv{};
					pbd_readbytes(&s, &sv);
					pb_slice str{};
					md.read_slice(&sv, &str);
					cb(ctx, &md, str);
					break;
				}
				default:
					pb_skipvalue(&s.base, tag);
					break;
			}
		}
	}

	template <typename T, typename Callback,
	          std::enable_if_t<std::is_invocable_v<int, Callback, T*, message_data*, pb_slice>>* = nullptr>
	int read(const pb_slice slice, Callback cb, T* ctx) {
		pbd_slice s{slice, slice.ptr};
		uint32_t tag;
		while (pb_readvarint32(&s.base, &tag)) {
			const int fnumber = pb_gettag(tag);
			switch (fnumber) {
				case kSceneId: {
					pbd_out o;
					if (pb_readvarint64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					sceneId = o.u64;
					break;
				}
				case kRequestKey: {
					pbd_out o{};
					if (pb_readvarint64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					requestKey = o.u32;
					break;
				}
				case kUsrId: {
					pbd_out o;
					if (pb_readvarint64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					usrId = o.u64;
					break;
				}
				case kTime: {
					pbd_out o;
					if (pb_readfixed64(&s.base, &o.u64) == 0) {
						log_error("invalid varint value at offset %d", pbd_offset(&s));
					}
					*static_cast<uint64_t*>(static_cast<void*>(&time)) = o.u64;
					break;
				}
				case kDatas: {
					message_data md{};
					pbd_slice sv{};
					pbd_readbytes(&s, &sv);
					pb_slice str{};
					md.read_slice(&sv, &str);
					int err;
					if ((err = cb(ctx, &md, str))) {
						return err;
					}
					break;
				}
				default:
					pb_skipvalue(&s.base, tag);
					break;
			}
		}
		return 0;
	}

	PB_WRAPPER_MessageCollection()
};
