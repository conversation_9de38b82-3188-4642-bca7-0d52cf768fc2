#ifndef WEB_H
#define WEB_H
#define DEFAULT_BACKLOG 128
#define  HTTP_SUCCESS  200
#define  HTTP_DONE_WITHOUT_RETURN  204
#define  HTTP_DONE_PARTIAL  206
#define  HTTP_ERROR_GRAMMER  400
#define  HTTP_ERROR_METHOD_FORBIDDEN  405
//------------------------buffer setting-------------------
#define  READ_BUFFER (512)
#define  WRITE_BUFFER (512)
#define  PATH_SIZE (256)
//------------------------http setting-------------------
#ifndef HTTP_TIMEOUT
#define HTTP_TIMEOUT (3000)
#endif
#ifndef TCP_TIME_OUT
#define TCP_TIME_OUT (30000)
#endif
#include "libcute.h"
#include <uv.h>
#include "pb.h"
#include "logger.h"
#include "http_parser.h"
#include "http_message.h"
//api
LP_NS_BEGIN
LP_NS_END
#endif
