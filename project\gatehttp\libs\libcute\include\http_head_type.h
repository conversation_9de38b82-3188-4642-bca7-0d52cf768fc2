#ifndef HTTP_HEAD_TYPE_H
#define HTTP_HEAD_TYPE_H
#include <libcute.h>
LP_NS_BEGIN
#define HTTP_REQUEST_HEAD(XX)\
XX(None					,   None				)\
XX(CacheControl			,	Cache-Control		)\
XX(Connection 			,	Connection			)\
XX(Date 				,	Date				)\
XX(KeepAlive 			,	Keep-Alive			)\
XX(Pragma 				,	Pragma				)\
XX(Trailer				,	Trailer				)\
XX(TransferEncoding 	,	Transfer-Encoding	)\
XX(Upgrade 				,	Upgrade				)\
XX(Via 					,	Via					)\
XX(Warning 				,	Warning				)\
XX(Allow 				,	Allow				)\
XX(ContentLength 		,	Content-Length		)\
XX(ContentType 			,	Content-Type		)\
XX(ContentEncoding 		,	Content-Encoding	)\
XX(ContentLanguage 		,	Content-Language	)\
XX(ContentLocation 		,	Content-Location	)\
XX(ContentMd5 			,	Content-MD5			)\
XX(ContentRange 		,	Content-Range		)\
XX(Expires 				,	Expires				)\
XX(LastModified 		,	Last-Modified		)\
XX(Accept 				,	Accept				)\
XX(AcceptCharset 		,	Accept-Charset		)\
XX(AcceptEncoding 		,	Accept-Encoding		)\
XX(AcceptLanguage		,	Accept-Language		)\
XX(Authorization 		,	Authorization		)\
XX(Cookie 				,	Cookie				)\
XX(Expect 				,	Expect				)\
XX(From 				,	From				)\
XX(Host 				,	Host				)\
XX(IfMatch 				,	If-Match			)\
XX(IfModifiedSince 		,	If-Modified-Since	)\
XX(IfNoneMatch 			,	If-None-Match		)\
XX(IfRange 				,	If-Range			)\
XX(IfUnmodifiedSince	,	If-Unmodified-Since	)\
XX(MaxForwards 			,	Max-Forwards		)\
XX(ProxyAuthorization 	,	Proxy-Authorization	)\
XX(Referer 				,	Referer				)\
XX(Range 				,	Range				)\
XX(Te 					,	Te					)\
XX(Translate 			,	Translate			)\
XX(UserAgent 			,	User-Agent			)

typedef enum httphead_request_t {
#define XX(head, string)HREQ_##head,
	HTTP_REQUEST_HEAD(XX)
#undef XX
} httphead_request;

LP_API
httphead_request httphead_request_enum(const char*);
LP_API
const char* httphead_request_str(httphead_request);
/*


*/
#define HTTP_RESPONSE_HEAD(XX)\
XX(None					   , None			   )\
XX(CacheControl            , Cache-Control     )\
XX(Connection              , Connection        )\
XX(Date                    , Date              )\
XX(KeepAlive               , Keep-Alive        )\
XX(Pragma                  , Pragma            )\
XX(Trailer                 , Trailer           )\
XX(TransferEncoding        , Transfer-Encoding )\
XX(Upgrade                 , Upgrade           )\
XX(Via                     , Via               )\
XX(Warning                 , Warning           )\
XX(Allow                   , Allow             )\
XX(ContentLength           , Content-Length    )\
XX(ContentType             , Content-Type      )\
XX(ContentEncoding         , Content-Encoding  )\
XX(ContentLanguage         , Content-Language  )\
XX(ContentLocation         , Content-Location  )\
XX(ContentMd5              , Content-MD5       )\
XX(ContentRange            , Content-Range     )\
XX(Expires                 , Expires           )\
XX(LastModified            , Last-Modified     )\
XX(AcceptRanges            , Accept-Ranges     )\
XX(Age                     , Age               )\
XX(ETag                    , ETag              )\
XX(Location                , Location          )\
XX(ProxyAuthenticate       , Proxy-Authenticate)\
XX(RetryAfter              , Retry-After       )\
XX(Server                  , Server            )\
XX(SetCookie               , Set-Cookie        )\
XX(Vary                    , Vary              )\
XX(WwwAuthenticate         , WWW-Authenticate  )
/*


*/
typedef enum httphead_response_t {
#define XX(head, string)HRESP_##head,
	HTTP_RESPONSE_HEAD(XX)
#undef XX
} httphead_response;

LP_API
httphead_response httphead_response_enum(const char*);
LP_API
const char* httphead_response_str(httphead_response);
/*

*/
LP_NS_END
#endif
