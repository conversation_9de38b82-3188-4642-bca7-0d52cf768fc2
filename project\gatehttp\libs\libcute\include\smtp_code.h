#ifndef  SMTP_ERRORC_H
#define  SMTP_ERRORC_H 
LP_NS_BEGIN
#define SMTP_CODE(XX)												\
XX(220	, READY 				, "smtp system ready"				)	\
XX(221	, CL<PERSON><PERSON>					, "stmp close"						)	\
XX(235	, AUTH_OK				, "auth ok"							)	\
XX(250	, TAKE_DONE				, "take cmd and done"				)	\
XX(334	, WAIT_VALIAD			, "wait input authorize"			)	\
XX(354	, BEGIN_INPUT			, "beg input and end with ..."		)	\
XX(421	, UNAVAILABLE			, "not available"					)	\
XX(450	, REQUEST_FAIL			, "request fail"					)	\
XX(451	, ABORT_ERROR			, "command abort"					)	\
XX(452	, OUT_OF_MEM			, "command abort out of memory"		)	\
XX(455	, OUT_OF_SERVICE		, "out of service"					)	\
XX(500	, GRAMMER_WRONG			, "grammer wrong"					)	\
XX(501	, GRAMMER_ERROR			, "grammer error"					)	\
XX(502	, <PERSON>IMPLEMENT			, "unimplement method"				)	\
XX(530	, START_TLS				, "must start tls"					)	\
XX(554	, SEND_ERROR			, "wrong mail address"				)

typedef enum smtp_code_t {
#define  XX(num, name , string)SMTP_##name=(num),
	SMTP_CODE(XX)
#undef XX
} smtp_code;

typedef struct stmp_message_t {
	int code;
	const char* msg;
} stmp_message;

LP_NS_END
#endif
