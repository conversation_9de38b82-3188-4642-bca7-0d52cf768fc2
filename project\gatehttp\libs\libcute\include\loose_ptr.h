#ifndef CUTE_LOOSE_PTR_H
#define CUTE_LOOSE_PTR_H
#pragma once
#include <cassert>
#include <cstddef>
#include <memory>
#include <atomic>

template <typename T>
class loose_ptr {
public:
	using self_type = loose_ptr<T>;
	using element_type = T;
	using atomic_t = std::atomic<size_t>;

	constexpr loose_ptr() noexcept : px(nullptr) {
	}

	loose_ptr(T* p) : px(p) {
	}

	template <typename U>
	loose_ptr(loose_ptr<U> const& rhs, std::enable_if_t<std::is_convertible_v<U*, T*>>* = nullptr) : px(rhs.get()) {
	}

	loose_ptr(self_type const& rhs) : px(rhs.px) {
	}

	~loose_ptr() {
		px = nullptr;
	}

	template <typename U>
	friend class loose_ptr;

	template <typename U>
	loose_ptr& operator=(loose_ptr<U> const& rhs) {
		self_type(rhs).swap(*this);
		return *this;
	}

	// Move support
	loose_ptr(self_type&& rhs) noexcept : px(rhs.px) { rhs.px = nullptr; }

	loose_ptr& operator=(self_type&& rhs) noexcept {
		self_type(static_cast<self_type&&>(rhs)).swap(*this);
		return *this;
	}

	template <typename U>
	loose_ptr(loose_ptr<U>&& rhs, std::enable_if_t<std::is_convertible_v<U*, T*>>* = nullptr) noexcept
		: px(rhs.px) {
		rhs.px = nullptr;
	}

	template <typename U, typename Deleter>
	loose_ptr& operator=(std::unique_ptr<U, Deleter>&& rhs) {
		loose_ptr(rhs.release()).swap(*this);
		return *this;
	}

	loose_ptr& operator=(loose_ptr const& rhs) {
		if (this != &rhs) {
			self_type(rhs).swap(*this);
		}
		return *this;
	}

	void reset() noexcept { self_type().swap(*this); }
	void reset(element_type* rhs) { self_type(rhs).swap(*this); }
	element_type* get() const noexcept { return px; }

	template <typename U>
	U* get() const noexcept {
		return px ? dynamic_cast<U*>(px) : nullptr;
	}

	element_type* detach() noexcept {
		element_type* ret = px;
		px = nullptr;
		return ret;
	}

	element_type& operator*() const {
		if (!px) {
			throw_e("using null_ptr");
		}
		return *px;
	}

	element_type* operator->() const {
		if (!px) {
			throw_e("using null_ptr");
		}
		return px;
	}

	// implicit conversion to "bool"
	operator bool() const noexcept { return px != nullptr; }
	// operator! is redundant, but some compilers need it
	bool operator!() const noexcept { return px == nullptr; }

	void swap(loose_ptr& rhs) noexcept {
		element_type* tmp = px;
		px = rhs.px;
		rhs.px = tmp;
	}

private:
	element_type* px;
};

template <typename T, typename U>
bool operator==(loose_ptr<T> const& a, loose_ptr<U> const& b) noexcept {
	return a.get() == b.get();
}

template <typename T, typename U>
bool operator==(loose_ptr<T> const& a, U* b) noexcept {
	return a.get() == b;
}

template <typename T, typename U>
bool operator==(T* a, loose_ptr<U> const& b) noexcept {
	return a == b.get();
}
#ifdef __cpp_impl_three_way_comparison
template <typename T, typename U>
inline std::strong_ordering operator<=>(loose_ptr<T> const &a, loose_ptr<U> const &b) noexcept {
  return a.get() <=> b.get();
}

template <typename T, typename U>
inline std::strong_ordering operator<=>(loose_ptr<T> const &a, U *b) noexcept {
  return a.get() <=> b;
}

template <typename T, typename U>
inline std::strong_ordering operator<=>(T *a, loose_ptr<U> const &b) noexcept {
  return a <=> b.get();
}
#else
template <typename T, typename U>
bool operator!=(loose_ptr<T> const& a, loose_ptr<U> const& b) noexcept {
	return a.get() != b.get();
}

template <typename T, typename U>
bool operator!=(loose_ptr<T> const& a, U* b) noexcept {
	return a.get() != b;
}

template <typename T, typename U>
bool operator!=(T* a, loose_ptr<U> const& b) noexcept {
	return a != b.get();
}

template <typename T, typename U>
bool operator<(loose_ptr<T> const& a, loose_ptr<U> const& b) noexcept {
	return a.get() < b.get();
}

template <typename T, typename U>
bool operator<(loose_ptr<T> const& a, U* b) noexcept {
	return a.get() < b;
}

template <typename T, typename U>
bool operator<(T* a, loose_ptr<U> const& b) noexcept {
	return a < b.get();
}

template <typename T, typename U>
bool operator<=(loose_ptr<T> const& a, loose_ptr<U> const& b) noexcept {
	return a.get() <= b.get();
}

template <typename T, typename U>
bool operator<=(loose_ptr<T> const& a, U* b) noexcept {
	return a.get() <= b;
}

template <typename T, typename U>
bool operator<=(T* a, loose_ptr<U> const& b) noexcept {
	return a <= b.get();
}

template <typename T, typename U>
bool operator>(loose_ptr<T> const& a, loose_ptr<U> const& b) noexcept {
	return a.get() > b.get();
}

template <typename T, typename U>
bool operator>(loose_ptr<T> const& a, U* b) noexcept {
	return a.get() > b;
}

template <typename T, typename U>
bool operator>(T* a, loose_ptr<U> const& b) noexcept {
	return a > b.get();
}

template <typename T, typename U>
bool operator>=(loose_ptr<T> const& a, loose_ptr<U> const& b) noexcept {
	return a.get() >= b.get();
}

template <typename T, typename U>
bool operator>=(loose_ptr<T> const& a, U* b) noexcept {
	return a.get() >= b;
}

template <typename T, typename U>
bool operator>=(T* a, loose_ptr<U> const& b) noexcept {
	return a >= b.get();
}
#endif
template <typename T>
bool operator==(loose_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() == nullptr;
}

template <typename T>
bool operator==(std::nullptr_t, loose_ptr<T> const& p) noexcept {
	return p.get() == nullptr;
}
#ifdef __cpp_impl_three_way_comparison
template <typename T>
inline std::strong_ordering operator<=>(loose_ptr<T> const &p, std::nullptr_t) noexcept {
  return p.get() <=> nullptr;
}

template <typename T>
inline std::strong_ordering operator<=>(std::nullptr_t, loose_ptr<T> const &p) noexcept {
  return p.get() <=> nullptr;
}
#else
template <typename T>
bool operator!=(loose_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() != nullptr;
}

template <typename T>
bool operator!=(std::nullptr_t, loose_ptr<T> const& p) noexcept {
	return p.get() != nullptr;
}

template <typename T>
bool operator<(loose_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() < nullptr;
}

template <typename T>
bool operator<(std::nullptr_t, loose_ptr<T> const& p) noexcept {
	return p.get() < nullptr;
}

template <typename T>
bool operator<=(loose_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() <= nullptr;
}

template <typename T>
bool operator<=(std::nullptr_t, loose_ptr<T> const& p) noexcept {
	return p.get() <= nullptr;
}

template <typename T>
bool operator>(loose_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() > nullptr;
}

template <typename T>
bool operator>(std::nullptr_t, loose_ptr<T> const& p) noexcept {
	return p.get() > nullptr;
}

template <typename T>
bool operator>=(loose_ptr<T> const& p, std::nullptr_t) noexcept {
	return p.get() >= nullptr;
}

template <typename T>
bool operator>=(std::nullptr_t, loose_ptr<T> const& p) noexcept {
	return p.get() >= nullptr;
}
#endif
template <typename T>
void swap(loose_ptr<T>& lhs, loose_ptr<T>& rhs) noexcept {
	lhs.swap(rhs);
}

// mem_fn support
template <typename T>
T* get_pointer(loose_ptr<T> const& p) {
	return p.get();
}

template <typename T, typename U>
loose_ptr<T> static_pointer_cast(loose_ptr<U> const& p) {
	return static_cast<T*>(p.get());
}

template <typename T, typename U>
loose_ptr<T> const_pointer_cast(loose_ptr<U> const& p) {
	return const_cast<T*>(p.get());
}

template <typename T, typename U>
loose_ptr<T> dynamic_pointer_cast(loose_ptr<U> const& p) {
	return dynamic_cast<T*>(p.get());
}

// operator<<
template <typename E, typename T, typename Y>
std::basic_ostream<E, T>& operator<<(std::basic_ostream<E, T>& os, loose_ptr<Y> const& p) {
	os << p.get();
	return os;
}

#include <functional> // hash 

namespace std {
	template <typename T>
	struct hash<loose_ptr<T>> {
		size_t operator()(const loose_ptr<T>& v) const noexcept {
			return static_cast<size_t>(v.get());
		}
	};
}
#endif
