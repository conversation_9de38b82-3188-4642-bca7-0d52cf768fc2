#pragma once
#include "hashs.h"
#include "sys_exception.h"

namespace sys {
	template <typename K<PERSON>, typename Vty, typename Hty = std::hash<Kty>>
	class hashmapx {
		using pair_t = std::pair<Kty, Vty>;
		struct entry_t;
	public:
		template <typename MapType, typename PairType>
		struct iterator_type {
			using pair_t = PairType;
			using iterator_category = std::forward_iterator_tag;
			using value_type = pair_t;
			using difference_type = ptrdiff_t;
			using pointer = pair_t*;
			using reference = pair_t&;
			using map_type = MapType;
			friend hashmapx;
			iterator_type() = default;
			~iterator_type() = default;

			iterator_type(map_type* e, const int p): _map(e), _pos(p) {
			}

			iterator_type(const iterator_type& rhs) {
				copy(rhs);
			}

			iterator_type(iterator_type&& rhs) noexcept {
				if (&rhs != this) {
					swap(std::forward<iterator_type>(rhs));
				}
			}

			iterator_type& operator =(const iterator_type& rhs) {
				if (&rhs != this) {
					copy(rhs);
				}
				return *this;
			}

			iterator_type& operator =(iterator_type&& rhs) noexcept {
				if (&rhs != this) {
					swap(std::forward<iterator_type>(rhs));
				}
				return *this;
			}

			bool operator ==(const iterator_type& rhs) const {
				return rhs._map == _map &&
					rhs._pos == _pos;
			}

			bool operator !=(const iterator_type& rhs) const {
				return !(*this == rhs);
			}

			iterator_type& operator ++() {
				_pos ++;
				if (_map && _map->_size <= _pos) {
					_map = nullptr;
					_pos = 0;
				}
				return *this;
			}

			iterator_type& operator ++(int) {
				auto temp = *this;
				++(*this);
				return temp;
			}

			void swap(iterator_type&& rhs) noexcept {
				std::swap(_map, rhs._map);
				std::swap(_pos, rhs._pos);
			}

			void copy(const iterator_type& rhs) {
				_map = rhs._map;
				_pos = rhs._pos;
			}

			operator bool() const {
				return _map;
			}

			pair_t& operator *() const {
				if (!_map) {
					throw_e("out of range");
				}
				int at = _map->_indexer[_pos];
				return _map->_entries[at].slot_;
			}

			pair_t* operator ->() const {
				if (_map) {
					int at = _map->_indexer[_pos];
					return &_map->_entries[at].slot_;
				}
				throw_e("out of range");
				return nullptr;
			}

		private:
			map_type* _map{};
			int _pos{};
		};

		using iterator = iterator_type<hashmapx, pair_t>;
		using const_iterator = iterator_type<const hashmapx, const pair_t>;
		typedef Vty value_type;
		typedef Kty key_type;
		typedef Hty hasher_t;
		typedef size_t size_type;
		typedef typename iterator::iterator_category iterator_category;
		typedef typename iterator::difference_type difference_type;
		typedef typename iterator::pointer pointer;
		typedef typename iterator::reference reference;

		explicit hashmapx(const size_t size) {
			reserve_(size);
		}

		hashmapx() = default;
		hashmapx(const hashmapx&);
		hashmapx(hashmapx&&) noexcept;
		hashmapx& operator =(const hashmapx& rhs);
		hashmapx& operator =(hashmapx&& rhs) noexcept;
		hashmapx& swap(hashmapx&& rhs) noexcept;
		~hashmapx();

		void reserve(const size_t val) {
			reserve_(val);
		}

		iterator emplace(const key_type& k, const value_type& v) {
			reserve_(_size + 1);
			int pos = add(k, v, _entries, _indexer, _capacity, _size);
			return pos == -1
				       ? iterator()
				       : iterator(this, pos);
		}

		iterator emplace(const key_type& k) {
			reserve_(_size + 1);
			const value_type v{};
			int pos = add(k, v, _entries, _indexer, _capacity, _size);
			return pos == -1
				       ? iterator()
				       : iterator(this, pos);
		}

		bool contains(const key_type& k) {
			return find_location(k, _capacity, _entries) != -1;
		}

		iterator begin() noexcept {
			if (_size > 0) {
				return iterator(this, 0);
			}
			return iterator();
		}

		iterator end() noexcept {
			return iterator();
		}

		const_iterator begin() const noexcept {
			if (_size > 0) {
				return const_iterator(this, 0);
			}
			return const_iterator();
		}

		const_iterator end() const noexcept {
			return const_iterator();
		}

		const_iterator cbegin() const noexcept {
			if (_size > 0) {
				return const_iterator(this, 0);
			}
			return const_iterator();
		}

		const_iterator cend() const noexcept {
			return const_iterator();
		}

		iterator find(const key_type& k) noexcept {
			const int at = find_location(k, _capacity, _entries);
			return at == -1
				       ? iterator()
				       : iterator(this, at);
		}

		const_iterator find(const key_type& k) const noexcept {
			const int at = find_location(k, _capacity, _entries);
			return at == -1
				       ? const_iterator()
				       : const_iterator(this, at);
		}

		iterator erase(const key_type& k) noexcept;
		iterator erase(const iterator& it) noexcept;

		const value_type& operator[](const key_type& k) const {
			return at(k);
		}

		const value_type& operator[](const key_type&& k) const {
			return at(std::move(k));
		}

		value_type& operator[](const key_type& k) {
			return at(k);
		}

		value_type& operator[](const key_type&& k) {
			return at(std::move(k));
		}

		const value_type& at(const key_type& k) const noexcept {
			return tryget(k);
		}

		value_type& at(const key_type& k) {
			int pos = find_location(k, _capacity, _entries);
			if (pos == -1) {
				reserve_(_size + 1);
				Vty v{};
				pos = add(k, v, _entries, _indexer, _capacity, _size);
			}
			int at = _indexer[pos];
			return _entries[at].slot_.second;
		}

		const value_type& tryget(const key_type& k) const noexcept {
			const int pos = find_location(k, _capacity, _entries);
			if (pos == -1) {
				return null_v;
			}
			int at = _indexer[pos];
			return _entries[at].slot_.second;
		}

		bool tryget(const key_type& k, value_type& v) const noexcept {
			const int pos = find_location(k, _capacity, _entries);
			if (pos == -1)return false;
			int at = _indexer[pos];
			v = _entries[at].slot_.second;
			return true;
		}

		bool empty() const noexcept {
			return _size == 0;
		}

		size_t size() const noexcept {
			return static_cast<size_t>(_size);
		}

		size_t capacity() const noexcept {
			return static_cast<size_t>(_capacity);
		}

		void clear() noexcept {
			if (_indexer) {
				for (int i = 0; i < _size; ++i) {
					auto at = _indexer[i];
					_entries[at] = entry_t{};
				}
			}
			_size = 0;
		}

	private :
		enum status_t:char {
			Zero = 0,
			kData = 1 << 0,
			kRemoved = 1 << 1
		};

		struct entry_t {
			std::pair<Kty, Vty> slot_{};
			status_t status_{Zero};
			int ptr_{0};
		};

		entry_t* _entries{nullptr};
		int* _indexer{};
		int _size{};
		int _capacity{};
		void copy(const hashmapx&);

		static int add(const key_type& key,
		               const value_type& value,
		               entry_t* entries,
		               int* indexer,
		               const int prime,
		               int& idx) {
			const size_t code = hasher_t()(key);
			const size_t h0 = code % prime;
			const size_t h1 = 1 + code % (prime - 1);
			size_t at = h0;
			for (int i = 1; i < prime; ++i) {
				entry_t* e = &entries[at];
				if (e->slot_.first == key) {
					if ((e->status_ & kData) == 0) {
						*e = entry_t{std::pair(key, value), kData, idx};
						indexer[idx] = static_cast<int>(at);
						++idx;
					} else {
						e->slot_.second = value;
					}
					return e->ptr_;
				}
				if ((e->status_ & kData) == 0) {
					*e = entry_t{std::pair(key, value), kData, idx};
					indexer[idx] = static_cast<int>(at);
					++idx;
					return e->ptr_;
				}
				at = (h0 + i * h1) % prime;
			}
			return -1;
		}

		static int find_location(const key_type& key, const int prime, const entry_t* entries) {
			if (!entries) {
				return - 1;
			}
			const size_t code = hasher_t()(key);
			const size_t h0 = code % prime;
			const size_t h1 = 1 + code % (prime - 1);
			size_t at = h0;
			for (int i = 1; i < prime; ++i) {
				const entry_t& e = entries[at];
				if ((e.status_ & kRemoved) != 0) {
					goto next;
				}
				if ((e.status_ & kData) == 0) {
					return -1;
				}
				if (e.slot_.first == key) {
					return static_cast<int>(e.ptr_);
				}
			next :
				at = (h0 + i * h1) % prime;
			}
			return -1;
		}

		void reserve_(size_t val) {
			/*make sure hit ratio less than 1/3*/
			val += val >> 1;
			int cap = static_cast<int>(val);
			if (_capacity >= cap) {
				return;
			}
			if (cap < 10) {
				cap = 10;
			}
			cap = next_prime(cap);
			auto* n_entries = new entry_t[cap];
			int* n_indexer = new int[cap];
			int size = 0;
			if (_entries) {
				for (int i = 0; i < _size; ++i) {
					auto at = _indexer[i];
					auto* s = &_entries[at].slot_;
					add(s->first,
					    s->second,
					    n_entries,
					    n_indexer,
					    cap,
					    size);
				}
				delete[] _entries;
				delete[] _indexer;
			}
			_indexer = n_indexer;
			_entries = n_entries;
			_capacity = cap;
			_size = size;
		}

		iterator erase_at(int pos) {
			if (pos != -1) {
				int at = _indexer[pos];
				int ptr = _entries[at].ptr_;
				_entries[at] = entry_t{};
				_entries[at].status_ = kRemoved;
				--_size;
				if (ptr != _size) {
					int tail = _indexer[_size];
					_entries[tail].ptr_ = ptr;
					_indexer[ptr] = tail;
				} else {
					pos = -1;
				}
				_indexer[_size] = -1;
			}
			return pos == -1
				       ? iterator()
				       : iterator(this, pos);
		}

		static value_type null_v;
	};

	template <typename Kty, typename Vty, typename Hty>
	Vty hashmapx<Kty, Vty, Hty>::hashmapx::null_v{};

	template <typename Kty, typename Vty, typename Hty>
	hashmapx<Kty, Vty, Hty>::hashmapx(const hashmapx& rhs) {
		if (&rhs != this) {
			copy(rhs);
		}
	}

	template <typename Kty, typename Vty, typename Hty>
	hashmapx<Kty, Vty, Hty>::hashmapx(hashmapx&& rhs) noexcept {
		if (&rhs != this) {
			swap(std::forward<hashmapx>(rhs));
		}
	}

	template <typename Kty, typename Vty, typename Hty>
	hashmapx<Kty, Vty, Hty>& hashmapx<Kty, Vty, Hty>::operator=(const hashmapx& rhs) {
		if (&rhs != this) {
			copy(rhs);
		}
		return *this;
	}

	template <typename Kty, typename Vty, typename Hty>
	hashmapx<Kty, Vty, Hty>& hashmapx<Kty, Vty, Hty>::operator=(hashmapx&& rhs) noexcept {
		if (&rhs != this) {
			swap(std::forward<hashmapx>(rhs));
		}
		return *this;
	}

	template <typename Kty, typename Vty, typename Hty>
	hashmapx<Kty, Vty, Hty>::~hashmapx() {
		delete [] _entries;
		delete[] _indexer;
		_size = 0;
		_capacity = 0;
		_entries = nullptr;
		_indexer = nullptr;
	}

	template <typename Kty, typename Vty, typename Hty>
	typename hashmapx<Kty, Vty, Hty>::iterator hashmapx<Kty, Vty, Hty>::erase(const Kty& k) noexcept {
		const int pos = find_location(k, _capacity, _entries);
		return erase_at(pos);
	}

	template <typename Kty, typename Vty, typename Hty>
	typename hashmapx<Kty, Vty, Hty>::iterator hashmapx<Kty, Vty, Hty>::erase(const iterator& it) noexcept {
		if (!it._map || it._map != this) {
			return iterator();
		}
		return erase_at(it._pos);
	}

	template <typename Kty, typename Vty, typename Hty>
	void hashmapx<Kty, Vty, Hty>::copy(const hashmapx& rhs) {
		if (_capacity < rhs._capacity) {
			delete []_indexer;
			delete[] _entries;
			const int cap = rhs._capacity;
			_indexer = new int[cap];
			_entries = new entry_t[cap];
		}
		ct_memcpy(static_cast<void*>(_indexer),
		          static_cast<void*>(rhs._indexer),
		          rhs._capacity * sizeof(int));
		for (int i = 0; i < rhs._size; ++i) {
			auto at = _indexer[i];
			auto* re = &rhs._entries[at];
			entry_t* e = &_entries[at];
			*e = *re;
		}
		_size = rhs._size;
		_capacity = rhs._capacity;
	}

	template <typename Kty, typename Vty, typename Hty>
	hashmapx<Kty, Vty, Hty>& hashmapx<Kty, Vty, Hty>::swap(hashmapx&& rhs) noexcept {
		if (&rhs != this) {
			std::swap(_entries, rhs._entries);
			std::swap(_indexer, rhs._indexer);
			std::swap(_capacity, rhs._capacity);
			std::swap(_size, rhs._size);
		}
		return *this;
	}
}

template <typename Kty, typename Vty, typename Hty>
void swap(sys::hashmapx<Kty, Vty, Hty>& lhs, sys::hashmapx<Kty, Vty, Hty>& rhs) noexcept {
	lhs.swap(rhs);
}
