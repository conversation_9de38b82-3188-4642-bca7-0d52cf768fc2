#pragma once
#include <btree_list.h>
#include <libcute.h>
#include <type_data.h>
#include "dailyopt_action.h"
#include "dailyopt_cost.h"
#include "typedata_wrapper_libgame.h"

namespace tvo {
	class type_dailyopt final : public type_data {
	public:
		static void awake_all(const awake_context_t&);
		void awake(const awake_context_t&) override;
		const dailyopt::dailyopt_cost* cost_at(int) const;
		cpx::btree_list<dailyopt::dailyopt_cost> cost_list;
		cpx::btree_list<dailyopt::dailyopt_action> actions;
		TYPE_DAILYOPT_WRAPPER()
	};
}
