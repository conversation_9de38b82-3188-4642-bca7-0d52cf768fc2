#pragma once
#include <unordered_set>
#include "markcode.h"
#include "usr_worker.h"

namespace sys {
	class worker_markcode final : public dvo::usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_markcode)
		void start() override;
		void stop() override;
		void update(const dvo::update_context*) override;
		void cache(markcode_key);
		void send(markcode_log&&);
		void on_notifiction(const dvo::worker_notification*) override;
	private:
		void insert(markcode_log&&);
		void flush();
		time_t _flushtime{};
		std::unordered_set<markcode_key> _codes{};
		std::vector<markcode_log> _logs{};
	};
}
