#pragma once
namespace cpx {
	template <typename T>
	class cpx_state_pool {
	public:
		DELETE_MOVE_COPY(cpx_state_pool)

		explicit cpx_state_pool() {
		}

		~cpx_state_pool() {
			while (!_pool.empty()) {
				auto* v = _pool.top();
				_pool.pop();
				delete v;
			}
		}

		rpx_state* obtain() {
			T* a = nullptr;
			if (_pool.empty()) {
				a = new T();
			} else {
				auto* v = _pool.top();
				_pool.pop();
				a = new(v)T();
			}
			return a;
		}

		void release(rpx_state* a) {
			if (!a)return;
			if (!_pool.empty() && _pool.top() == a) {
				log_error("Internal error. Trying to destroy object that is already released to pool.");
				return;
			}
			auto* b = static_cast<T*>(a);
			b->~T();
			_pool.emplace(b);
		}

	private:
		std::stack<T*> _pool;
	};
}
