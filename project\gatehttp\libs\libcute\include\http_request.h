#pragma once
#include  "libcute.h"
#include "http_client.h"
#include <string>

namespace net {
	class http_request {
	public:
		DELETE_MOVE_COPY(http_request)
		explicit http_request(const char*);
		explicit http_request(http_client::ptr_t);
		void read(pb_buffer*);
		void write(const char* buf, size_t size);
		void write(const std::string&);
		http_phase phase() const;
		size_t content_length();
		bool done();
		int error() const;
		head_request_t* request_head();
		explicit operator bool() const;
		~http_request();
	private:
		http_client::ptr_t _pstream;
		http_client* _server;
		head_request_t _head;
		void write_head(size_t len);
		void output(const char* buf, size_t len, int fin) const;
		void flush() const;
		void ensure_out();
		void await_phase(http_phase) const;
	};
}
