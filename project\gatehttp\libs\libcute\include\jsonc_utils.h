#ifndef JSONC_UTILS_H
#define JSONC_UTILS_H
#ifdef __cplusplus
extern "C"
{
#endif
#include "jsonc.h"
/* Implement RFC6901 (https://tools.ietf.org/html/rfc6901) JSON Pointer spec. */
CJSON_PUBLIC(jsonc *) jsonc_utils_get_pointer(jsonc* object, const char* pointer);
CJSON_PUBLIC(jsonc *) jsonc_utils_get_pointer_casesensitive(jsonc* object, const char* pointer);
/* Implement RFC6902 (https://tools.ietf.org/html/rfc6902) JSON Patch spec. */
/* NOTE: This modifies objects in 'from' and 'to' by sorting the elements by their key */
CJSON_PUBLIC(jsonc *) jsonc_utils_generate_patches(jsonc* from, jsonc* to);
CJSON_PUBLIC(jsonc *) jsonc_utils_generate_patches_casesensitive(jsonc* from, jsonc* to);
/* Utility for generating patch array entries. */
CJSON_PUBLIC(void) jsonc_array_add_patch(jsonc* array, const char* operation, const char* path, const jsonc* value);
/* Returns 0 for success. */
CJSON_PUBLIC(int) jsonc_object_apply_patches(jsonc* object, const jsonc* patches);
CJSON_PUBLIC(int) jsonc_object_apply_patches_casesensitive(jsonc* object, const jsonc* patches);
CJSON_PUBLIC(jsonc *) jsonc_utils_merge_patch(jsonc* target, const jsonc* patch);
CJSON_PUBLIC(jsonc *) jsonc_utils_merge_patch_casesensitive(jsonc* target, const jsonc* patch);
/* generates a patch to move from -> to */
/* NOTE: This modifies objects in 'from' and 'to' by sorting the elements by their key */
CJSON_PUBLIC(jsonc *) jsonc_utils_gen_merge_patch(jsonc* from, jsonc* to);
CJSON_PUBLIC(jsonc *) jsonc_utils_gen_merge_patch_casesensitive(jsonc* from, jsonc* to);
/* Given a root object and a target object, construct a pointer from one to the other. */
CJSON_PUBLIC(char *) jsonc_utils_findptr_from_object_to(const jsonc* object, const jsonc* target);
/* Sorts the members of the object into alphabetical order. */
CJSON_PUBLIC(void) jsonc_sort(jsonc* object);
CJSON_PUBLIC(void) jsonc_sort_casesensitive(jsonc* object);
#ifdef __cplusplus
}
#endif
#endif
