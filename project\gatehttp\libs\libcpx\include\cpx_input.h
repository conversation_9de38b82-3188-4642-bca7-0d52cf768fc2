#pragma once
#include <libcute.h>
#include <sys_exception.h>
#include "context_cluster.h"
#include "cpx_cast.h"
#include "time_vo.h"

namespace bt {
	class bt_workdata;
}

namespace sys {
	class blackboard;
}

namespace cpx {
	class cpx_parameter;
	class cpx_controller;
	class rpx_parameter;
	class cpx_entity;
	class rpx_controller;
	class rpx_machine;
	class rpx_state;

	class cpx_input {
	public:
		friend class rpx_state_action;
		friend class cpx_act_sub_control;
		DELETE_MOVE_COPY(cpx_input)
		time_vo now;
		bt::context_cluster cluster;
		rpx_controller& ctrl() const;
		sys::blackboard& blackboard() const;
		rpx_parameter& rpx_param() const;
		const cpx_controller& ctrl_source() const;
		const cpx_parameter* find_param(int64_t pid) const;
		cpx_input();
		~cpx_input();

		template <typename T,
		          std::enable_if_t<std::is_base_of_v<cpx_entity, T>> * = nullptr>
		T& host() const {
			if (!entity) {
				throw_e("entity is nil");
			}
			return *assert_entity<T>(entity);
		}

	private:
		rpx_controller* controller;
		cpx_entity* entity;
		rpx_machine* machine;
		rpx_state* state;
	};

	class cpx_output {
	public:
		DELETE_MOVE_COPY(cpx_output)
		cpx_output();
		~cpx_output();
		int priority;
	};
}
