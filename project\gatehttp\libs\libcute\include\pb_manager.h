#pragma once
#include <string>

namespace ecs {
	class domain_t;
}

class pb_base;
class pb_manager;
#include "pb.h"
#include "pb_context.h"

class pb_manager {
public:
	static uint32_t version();
	static int file_version();
	static void add_base_path(const std::string& file);
	static int load_default();
	static void load_version(const std::string& root, bool compressed = false);
	static void reload();
	static pb_state* current();
	static ecs::domain_t* current_domain();
	static ecs::domain_t* domain_at(int ver);
	static int get_name(int type, std::string& name);
	static const char* get_name(int type);
	static void free_mem();
};

pb_Type* pb_classid_to_type(int class_id);
void pbe_addlength(pb_buffer* b, size_t len);
int pbd_mismatch(const pb_Field* f, const pbd_slice* s, uint32_t tag);
void pbd_readbytes(pbd_slice* in, pbd_slice* out);
int pbd_offset(const pbd_slice* s);
/*both a, b should be pb_FieldType*/
int pbd_compatible_type_id(uint32_t a, uint32_t b);
