#pragma once
#include "cpx_action.h"

namespace cpx {
	class cpx_act_until : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_act_until)
		~cpx_act_until() override;

	protected:
		cpx_act_until();

		enum return_check_t {
			kReset,
			kRuning,
			kDone,
		};

		void on_start(cpx_input*, cpx_output*) const final;
		bt::btree_status on_update(cpx_input*, cpx_output*) const final;
		void on_stop(cpx_input*, cpx_output*) const final;
		virtual return_check_t check(bt::btree_status) const = 0;
	};
}
