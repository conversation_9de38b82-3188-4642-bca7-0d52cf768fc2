#pragma once
#include "libcute.h"
#include <libcutex.h>
#include <type_cast.h>

namespace tvo {
	class tvo_scope;
	struct awake_context_t;

	class type_data {
	public :
		friend class typesheet;
		DELETE_MOVE_COPY(type_data)
		int id{};
		virtual int class_id() const = 0;
		virtual const char* class_n() const = 0;
		virtual void parse(dynamic_vo&&) =0;
		virtual void pre_parse(const dynamic_vo&);
		const tvo_scope* scope() const;

	protected:
		type_data();
		friend class typesheet;
		virtual void awake(const awake_context_t&);
		virtual ~type_data();
		const std::string& get_lang(const char*, int langk, const char*) const;
		tvo_scope* _scope{};
	};
}
