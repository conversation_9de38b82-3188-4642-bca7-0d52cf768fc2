#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "nav_struct.h"

namespace tvo {
	class type_property_relation;
	class type_buff;

	class type_career final : public type_data {
	public:
		DELETE_MOVE_COPY(type_career)
		type_career();
		~type_career() override;
		static void awake_all(const awake_context_t&);
		type_buff* buff() const;
		std::vector<type_property_relation*> relation{};
		TYPE_CAREER_WRAPPER()

	private:
		type_buff* _buff{};
	};
}
