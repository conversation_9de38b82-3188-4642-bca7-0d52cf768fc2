#pragma once
#include <fstream>
#include <libcute.h>
#include <hashmapx.h>
#include <system_error.h>

namespace bt {
	template <typename T>
	class btree_factory {
		DELETE_MOVE_COPY(btree_factory)
		btree_factory() = default;
		~btree_factory() = default;

	public:
		using tree_type = T;
		using tree_creator = tree_type* (*)();
		using factory_map_t = sys::hashmapx<std::string, tree_creator>;
		using instance_map_t = sys::hashmapx<std::string, tree_type*>;

		template <typename TRoot, std::enable_if<std::is_base_of_v<T, TRoot>>* = nullptr>
		static int read_manifest(const char* file) {
			std::ifstream in(file, std::ios::in | std::ios::binary);
			if (in.fail()) {
				log_error("config file %s read fail", file);
				return ERR_FILE_R_FAIL;
			}
			dynamic_vo json;
			in >> json;
			in.close();
			auto list = json.at("files");
			using root_type = TRoot;
			for (const auto& jo : list) {
				auto* root = new root_type();
				root->root = root;
				root->src_type(root_type::kFile);
				root->parse(jo);
				const std::string n = root->name();
				m_instance.emplace(n, root);
			}
			return 0;
		}

		template <typename TRoot, std::enable_if<std::is_base_of_v<T, TRoot>>* = nullptr>
		static TRoot* find(const std::string& name) {
			auto it = m_instance.find(name);
			if (it == m_instance.end()) {
				return nullptr;
			}
			return static_cast<TRoot*>(it->second);
		}

		static tree_type* create(const std::string& cmd) {
			auto it = m_factory.find(cmd);
			if (it == m_factory.end()) {
				return nullptr;
			}
			return it->second();
		}

		static void reg_creator(const std::string& cmd, const tree_creator c) {
			m_factory.emplace(cmd, c);
		}

	private:
		static factory_map_t m_factory;
		static instance_map_t m_instance;
	};

	template <typename T>
	typename btree_factory<T>::factory_map_t btree_factory<T>::m_factory;
	template <typename T>
	typename btree_factory<T>::instance_map_t btree_factory<T>::m_instance;
}
