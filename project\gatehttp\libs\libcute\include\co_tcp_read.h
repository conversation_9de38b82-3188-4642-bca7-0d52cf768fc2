#pragma once
#include "co_job.h"
#include "ref_ptr.h"
#include "service_form.h"
#include "stop_watcher.h"
#include "tcp_peer.h"

namespace co {
	class tcp_read final : co_job {
	public:
		DELETE_MOVE_COPY(tcp_read)
		tcp_read(ref_ptr<net::tcp_peer>, int form_id);
		~tcp_read() override;
		ref_ptr<service_form> result() const;
	private:
		void on_read(const net::net_base::event_t*);
		ref_ptr<net::tcp_peer> _p;
		int _form_id;
		bool _done;
		stop_watcher _timeout;
		ref_ptr<service_form> _back;
	};
}
