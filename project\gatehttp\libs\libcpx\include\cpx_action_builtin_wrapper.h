//gen by cpp_autogen automatically, don't modify it!
#pragma once
#ifndef CPX_ACTION_BUILTIN_WRAPPER_H
#define CPX_ACTION_BUILTIN_WRAPPER_H

#define CPXA_CPX_ACT_BLOCK_ON_PARAMETER_WRAPPER()\
int64_t paramId{};\
nav::float_t targetValue{};\
bt::value_compare_type compare{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="paramId"){v.get_to(paramId);}\
else if(k=="targetValue"){v.get_to(targetValue);}\
else if(k=="compare"){v.get_to(compare);}\
}\
}\
int class_id() const noexcept override {\
return 1419543335;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_block_on_parameter";\
}\

#define CPXA_CPX_ACT_DELAY_WRAPPER()\
nav::float_t delay{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="delay"){v.get_to(delay);}\
}\
}\
int class_id() const noexcept override {\
return 1558363373;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_delay";\
}\

#define CPXA_CPX_ACT_NOTHING_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_id() const noexcept override {\
return -777406281;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_nothing";\
}\

#define CPXA_CPX_ACT_REWIND_PARAMETER_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_id() const noexcept override {\
return -472136580;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_rewind_parameter";\
}\

#define CPXA_CPX_ACT_SEQUENCE_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="maxLoops"){v.get_to(maxLoops);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_id() const noexcept override {\
return 248364165;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_sequence";\
}\

#define CPXA_CPX_ACT_PARALLEL_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="maxLoops"){v.get_to(maxLoops);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_id() const noexcept override {\
return 70218443;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_parallel";\
}\

#define CPXA_CPX_ACT_SELECTOR_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="maxLoops"){v.get_to(maxLoops);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_id() const noexcept override {\
return 90388643;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_selector";\
}\

#define CPXA_CPX_ACT_PARALLEL_SELECTOR_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="maxLoops"){v.get_to(maxLoops);}\
else if(k=="returnMode"){v.get_to(returnMode);}\
}\
}\
int class_id() const noexcept override {\
return -1144926262;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_parallel_selector";\
}\

#define CPXA_CPX_ACT_SET_INITED_WRAPPER()\
bool value{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="value"){v.get_to(value);}\
}\
}\
int class_id() const noexcept override {\
return -1341717733;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_set_inited";\
}\

#define CPXA_CPX_ACT_SET_PARAMETER_WRAPPER()\
int64_t paramId{};\
nav::float_t targetValue{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="paramId"){v.get_to(paramId);}\
else if(k=="targetValue"){v.get_to(targetValue);}\
}\
}\
int class_id() const noexcept override {\
return 807188332;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_set_parameter";\
}\

#define CPXA_CPX_ACT_REPEAT_WRAPPER()\
int maxLoops{};\
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="maxLoops"){v.get_to(maxLoops);}\
}\
}\
int class_id() const noexcept override {\
return -1344376568;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_repeat";\
}\

#define CPXA_CPX_ACT_UNTIL_SUCCESS_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_id() const noexcept override {\
return 5648498;\
}\
const char* class_n() const  noexcept override {\
return "cpx_act_until_success";\
}\

#define CPXA_CPX_ACT_UNTIL_FAILURE_WRAPPER()\
void parse(const dynamic_vo& jo) override {\
}\
int class_id() const noexcept override {\
return 786244089;\
}\
const char* class_n() const noexcept override {\
return "cpx_act_until_failure";\
}\



#define CPXA_CPX_ACT_SUB_CONTROL_WRAPPER()\
std::string file{}; \
void parse(const dynamic_vo& jo) override {\
for(auto & [k,v] :jo.items()){\
if(k=="file"){v.get_to(file);}\
}\
}\
int class_id() const noexcept override {return 199312851;}\
const char* class_n() const noexcept override {return "cpx_act_sub_control";}\




#endif //CPX_ACTION_BUILTIN_WRAPPER_H
