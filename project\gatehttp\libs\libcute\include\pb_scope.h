#pragma once
#include <libcute.h>
#include "pb.h"
#include "pb_manager.h"
#include "logger.h"
class message_collection;
class pb_base;

namespace mem {
	/*
		a head scope prepare for write bytes next 
		it will take a sizeof(int) at head of stream 
		once all done, the deconstruct will write the true 
		length into head 
	
	*/
	struct pb_size_scope {
		DELETE_MOVE_COPY(pb_size_scope)
		explicit pb_size_scope(pb_buffer*);
		void ignore();
		void discard();
		int size() const;
		bool empty() const;
		size_t beg() const;
		~pb_size_scope();

	private:
		pb_buffer* _buf;
		size_t _head;
		size_t _beg;
	};
}

namespace mem {
	using pb_field_index = int;
}

namespace mem {
	/*a head scope prepare for write bytes to field_index */
	struct pb_bytes_scope {
		DELETE_MOVE_COPY(pb_bytes_scope)
		explicit pb_bytes_scope(pb_buffer*, pb_field_index);
		void ignore();
		void discard();
		int size() const;
		bool empty() const;
		~pb_bytes_scope();

	private:
		pb_buffer* _buf;
		size_t _head;
		size_t _beg;
	};

	/*a head scope prepare for write a complex pb_vo to field_index */
	struct pb_message_scope {
		DELETE_MOVE_COPY(pb_message_scope)
		explicit pb_message_scope(pb_buffer*, pb_field_index);
		void ignore();
		void discard();
		int size() const;
		bool empty() const;
		~pb_message_scope();

	private:
		pb_buffer* _buf;
		size_t _head;
		size_t _beg;
	};
}

namespace mem {
	/*
		a head scope prepare for write a message_collection
	*/
	struct message_collection_head {
		DELETE_MOVE_COPY(message_collection_head)
		message_collection_head(pb_buffer*, const message_collection*);
		~message_collection_head();
		void ignore();
		void discard();
		int size() const;
		bool empty() const;

	private:
		pb_buffer* _buf;
		size_t _head;
		size_t _beg;
	};
};

namespace mem {
	template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
	int pb_readint(const pb_slice slice, const pb_field_index field, T& out) {
		pbd_slice s{};
		s.base = slice;
		s.head = slice.ptr;
		uint32_t tag;
		while (pb_readvarint32(&s.base, &tag)) {
			const int fnumber = pb_gettag(tag);
			if (fnumber == field) {
				pbd_out o;
				if (pb_readvarint64(&s.base, &o.u64) == 0) {
					log_error("invalid varint value at offset %d", pbd_offset(&s));
					return -1;
				}
				out = static_cast<T>(o.u64);
				return 0;
			}
			pb_skipvalue(&s.base, tag);
		}
		return -1;
	}
}
