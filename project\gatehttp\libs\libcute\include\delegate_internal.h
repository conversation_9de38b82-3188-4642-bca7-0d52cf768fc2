#pragma once
#include <functional>
#include <utility>
#define CUTE_DELEGATE_FUNC_DECL(T , MEMBER, ADD, REMOVE)				\
public:																	\
/*---add----	*/														\
void ADD(T::delegate_call fn){(MEMBER).add(fn);}						\
template<typename C>													\
void ADD(C* obj, T::delegate_mcall<C> fn){(MEMBER).add(obj,fn);}		\
/*---remove----	*/														\
void REMOVE(T::delegate_call fn){(MEMBER).remove(fn);}					\
template<typename C>													\
void REMOVE(C* obj, T::delegate_mcall<C> fn){(MEMBER).remove(obj,fn);}	\
private:
/*



*/
#define CUTE_DELEGATE_TREE_FUNC_DECL(T , K, MEMBER, ADD, REMOVE)				\
public:																			\
/*---add----	*/																\
void ADD(K k, T::delegate_call fn){(MEMBER).add(k,fn);}							\
template<typename C>															\
void ADD(K k, C* obj, T::delegate_mcall<C> fn){(MEMBER).add(k,obj,fn);}			\
/*---remove----	*/																\
void REMOVE(K k,T::delegate_call fn){(MEMBER).remove(k, fn);}					\
template<typename C>															\
void REMOVE(K k,C* obj, T::delegate_mcall<C> fn){(MEMBER).remove(k,obj,fn);}	\
void REMOVE(K k){(MEMBER).remove(k);}											\
private:
/*



*/
namespace delegate_internal {
	enum class call_type {
		MEMBER_CALL,
		FUNC_PTR
	};

	template <typename ...T>
	struct delegate_entry {
	};

	template <typename R, typename ...Args>
	struct delegate_entry<R(Args ...)> {
		using delegate_call = R(*)(Args ...);
		template <typename C>
		using delegate_mcall = R(C::*)(Args ...);
		using delegate_func = std::function<R(Args ...)>;
		bool cancel_{};
		call_type type_{};
		delegate_call dcall_{}; // direct call
		delegate_func mcall_{}; // member call 
		void* instance_{};

		explicit delegate_entry(const delegate_call d): type_(call_type::FUNC_PTR),
		                                                dcall_(d) {
		}

		explicit delegate_entry(delegate_func m, void* inst): dcall_(),
		                                                      mcall_(std::move(m)),
		                                                      instance_(inst) {
		}

		delegate_entry() {
		}
	};

	template <class C, typename R, typename ...Args>
	class class_member_method final {
	public:
		using member_call = R(C::*)(Args ...);

		class_member_method(const member_call& fun, C* ptr): _func(fun), _ptr(ptr) {
		}

		R operator()(Args&&... args) {
			return (_ptr->*_func)(std::forward<Args>(args)...);
		}

		bool operator ==(const class_member_method<C, R, Args...>& other) const {
			return _ptr == other._ptr && _func == other._func;
		}

		template <typename T>
		bool operator ==(const T&&) const {
			return false;
		}

		C* ptr() const { return _ptr; }
	private:
		member_call _func;
		C* _ptr;
	};

	template <typename C, typename R, typename ...Args>
	auto make_class_member(R (C::*class_func_type)(Args ...), C* class_ptr) {
		return class_member_method<C, R, Args...>(class_func_type, class_ptr);
	};
}
