#pragma once
#include "net_stream_writer.h"
#include "recycle_stream.h"

namespace net {
	struct tcp_stream_writer_fast final : net_stream_writer {
		int write(const char*, size_t) override;
		int write(const recycle_stream&) override;
		int flush() override;
		void get_uvbuf(uv_buf_t*) override;
		size_t size() override;
		void on_write_done() override;
		void stat(stat_writer&, time_t) override;
		int ping(time_t)   override;
		int pong(time_t)   override;

	private:
		recycle_stream _writing{};
		recycle_stream _sending{};
		time_t _stat_time{};
		int64_t _queue{};

		struct {
			size_t length{};
			size_t count{};
		} _netstat;
	};
}
