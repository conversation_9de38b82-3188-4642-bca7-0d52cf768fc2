#pragma once
#include <type_traits>

namespace ecs {
	template <typename>
	struct _isnumber32_helper : std::false_type {
	};

	template <>
	struct _isnumber32_helper<uint32_t> : std::true_type {
	};

	template <>
	struct _isnumber32_helper<int> : std::true_type {
	};

	template <typename Tp>
	struct is_number32 : _isnumber32_helper<std::remove_cv_t<Tp>>::type {
	};

	template <typename Tp>
	inline constexpr bool is_number32_v = is_number32<Tp>::value;

	template <typename>
	struct _isnumber64_helper : std::false_type {
	};

	template <>
	struct _isnumber64_helper<uint64_t> : std::true_type {
	};

	template <>
	struct _isnumber64_helper<int64_t> : std::true_type {
	};

	//template <>
	//struct _isnumber64_helper<size_t> : std::true_type {
	//};

	template <typename Tp>
	struct is_number64 : _isnumber64_helper<std::remove_cv_t<Tp>>::type {
	};

	template <typename Tp>
	inline constexpr bool is_number64_v = is_number64<Tp>::value;

	template <typename>
	struct _isnumber8_helper : std::false_type {
	};

	template <>
	struct _isnumber8_helper<bool> : std::true_type {
	};

	template <>
	struct _isnumber8_helper<char> : std::true_type {
	};

	template <typename Tp>
	struct is_number8 : _isnumber8_helper<std::remove_cv_t<Tp>>::type {
	};

	template <typename Tp>
	inline constexpr bool is_number8_v = is_number8<Tp>::value;



	template <typename>
	struct is_floating_point
		: std::false_type {
	};

	template <>
	struct is_floating_point<float>
		: std::true_type {
	};

	template <>
	struct is_floating_point<double>
		: std::true_type {
	};

	template <>
	struct is_floating_point<long double>
		: std::true_type {
	};

}
