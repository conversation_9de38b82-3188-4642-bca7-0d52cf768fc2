#pragma once
#include <libcute.h>
#include <randseed.h>

#include "context_cluster.h"
#include "process_mode.h"

namespace cpx {
	class cpx_entity;
}

namespace bt { 
	class bt_workdata;
	class bt_context;

	class input_t {
		DEFAULT_MOVE_COPY(input_t)
	public:  
		context_cluster cluster;
		process_mode mode;
		virtual ~input_t();
		virtual nav::float_t nextd() const = 0;
	protected:
		input_t(); 
	};

	class output_t {
		DEFAULT_MOVE_COPY(output_t)
	protected:
		output_t();
		virtual ~output_t();
	};
}
