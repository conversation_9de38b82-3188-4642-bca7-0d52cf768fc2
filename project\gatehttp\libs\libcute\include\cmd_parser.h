#pragma once
#include "libcute.h"
#include <string>
#include <vector>

namespace sys {
	struct cmd_arg {
		enum op_type_t {
			kMust,
			kOptional,
		};

		std::string name{};
		std::string description{};
		op_type_t op_type{};
		std::string default_value{};
		std::vector<std::string> values{};
		std::string value() const;
	};

	class cmd_parser {
	public:
		DEFAULT_MOVE_COPY(cmd_parser)
		cmd_parser();
		virtual ~cmd_parser() = default;
		void parse(int argc, const char* const args[]);
		cmd_parser& add(const cmd_arg&);
		bool success() const noexcept;
		const cmd_arg& arg_at(const std::string&)const ;
		const char* program_name() const;
		void parse_error(const char*);
		void print_args() const;
		using const_iterator = std::vector<cmd_arg>::const_iterator;
		const_iterator begin() const;
		const_iterator end() const;
	protected:
		virtual void print_usage() const = 0;
	private:
		const char* _program{};
		bool _suc{};
		std::vector<cmd_arg> _args{};
	};
}
