/*auto gen, don't modify it*/
#ifndef SERVICE_COMMON_TYPE_H 
#define SERVICE_COMMON_TYPE_H 
#define SERVICE_COMMON_TYPE(XX)\
XX(359655438, service_form, "service_form")\
XX(794275889, MessageData, "MessageData")\
XX(1530375653, Message<PERSON>ollection, "MessageCollection")\
XX(2024311543, SocketForm, "SocketForm")\
XX(1379892991, room_id, "room_id")\
XX(697481833, hash128, "hash128")\

#endif  // endof SERVICE_COMMON_TYPE_H