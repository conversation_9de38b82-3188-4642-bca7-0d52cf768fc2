#pragma once
#include <fp_math.hpp>
#include <hashmapx.h>
#include <libcute.h>

namespace cpx {
	class anim_collection {
	public:
		DELETE_MOVE_COPY(anim_collection)

		enum update_mode {
			kNormal,
			kFor<PERSON>,
		};

		struct slot_t {
			int type{};
			nav::float_t value{0};
			int64_t frame{0};
			update_mode mode{};
			bool operator ==(const slot_t&) const;
			bool operator !=(const slot_t&) const;
		};

		struct container_t {
			DELETE_MOVE_COPY(container_t)
			container_t();
			~container_t();
			int bone;
			sys::hashmapx<int, slot_t> slots{};
			int64_t frame;
		};

		container_t& main();
		container_t& make(int bone);
		anim_collection();
		~anim_collection();

	private:
		sys::hashmapx<int, container_t*> _bones{};
		container_t _main;
	};
}
