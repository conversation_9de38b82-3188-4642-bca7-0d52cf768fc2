#ifndef THREAD_POOL_H
#define THREAD_POOL_H 
#pragma once
#include <vector>
#include <queue>
#include <memory>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <future>
#include <functional>
#include <stdexcept>
#include "libcute.h"
#include <type_traits> 
#include "libcutex.h"
#include "logger.h"

class thread_pool {
public:
	typedef std::function<void()> thread_job;
	explicit thread_pool(size_t);
	DELETE_MOVE_COPY(thread_pool)

	template <typename Fn, typename ... Args,
	          std::enable_if_t<std::is_invocable_v<Fn, Args ...>>* = nullptr>
	void enqueue(Fn&& fn, Args&& ... args) {
		{
			std::unique_lock lock(_mutex);
			if (_stop) {
				log_error("thread pool stopped");
			} else {
				_tasks.emplace([=]() { try_call(fn((args)...)); });
			}
		}
		_condition.notify_one();
	}

	void enqueue(thread_id, const thread_job&);
	int enqueue_at(size_t at, const thread_job&);
	thread_id get_subid(size_t at) const;
	size_t size() const;
	size_t count_task() const;
	void upsize_to(size_t size);

	void notify_all() {
		_condition.notify_all();
	}

	~thread_pool();
private:
	class entry {
	public:
		explicit entry(thread_pool*, size_t idx);
		DELETE_MOVE_COPY(entry)
		void work();
		void start();
		thread_id id_;
		thread_pool* pool_;
		std::thread thread_;
		std::queue<thread_job> jobs_;
		size_t index_;
		~entry();
	};

	std::vector<entry*> _threads;
	std::queue<thread_job> _tasks;
	std::mutex _mutex;
	std::condition_variable _condition;
	std::atomic<bool> _stop;
};
#endif
/*template <class F, class... Args>
	auto enqueue(F&& f, Args&&... args)
	-> std::future<std::invoke_result_t<decltype(f), Args ...>> {
		using R = std::invoke_result_t<decltype(f), Args ...>;
		auto task = std::make_shared<std::packaged_task<R()>>(
			std::bind(std::forward<F>(f), std::forward<Args>(args)...)
		);
		std::future<R> res = task->get_future();
		{
			std::unique_lock lock(_mutex);
			if (_stop) {
				log_error("thread pool stopped");
			} else {
				_tasks.emplace([task]() { (*task)(); });
			}
		}
		_condition.notify_one();
		return res;
	}*/
