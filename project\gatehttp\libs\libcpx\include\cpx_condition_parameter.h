#pragma once
#include <cpx_condition.h>
#include "cpx_condition_builtin_wrapper.h"
#include "value_compare_type.h"
#include <cpx_struct.h>
namespace cpx {
	class cpx_condition_parameter final : public cpx_condition {
	public:
		DELETE_MOVE_COPY(cpx_condition_parameter)
		CPXC_CPX_CONDITION_PARAMETER_WRAPPER()
		cpx_condition_parameter();
		~cpx_condition_parameter() override;
	protected:
		bool check_validate(const cpx_transition_context* ctx) const override;
		void on_read() override;
	};
}
