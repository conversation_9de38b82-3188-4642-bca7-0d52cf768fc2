#pragma once
#include <libcute.h>
#include <hashmapx.h>
#include <consistent_ring.h>
#include <consistent_key.h>

namespace sys {
	class graph_consistent {
	public:
		DELETE_MOVE_COPY(graph_consistent)
		graph_consistent();
		~graph_consistent();
		graph_consistent& add(service_type);
		void recv(const std::vector<cluster_event>&);
		int find(consistent_key, route_t&);

		template <typename TService, typename TValue, std::enable_if_t<std::is_integral_v<TValue>> * = nullptr>
		int find(const TService st, const TValue h, route_t& out) {
			return find(consistent_key(st, h), out);
		}

		template <typename TService, typename TValue, std::enable_if_t<!std::is_integral_v<TValue>> * = nullptr>
		int find(const TService st, const TValue& h, route_t& out) {
			return find(consistent_key(st, h), out);
		}

		template <typename TService>
		int find(const TService st, const char* h, route_t& out) {
			return find(consistent_key(st, h), out);
		}

		std::vector<route_t> registed_routes() const;
		std::vector<route_t> registed_routes(service_type) const;
	private:
		hashmapx<service_type, consistent_ring*> _crings{};
	};
}
