#pragma once
#include <string>
#include "http_parser.h"
#include  "libcute.h"

namespace net {
	struct ip_addr {
		DEFAULT_MOVE_COPY(ip_addr)
		ip_addr();
		ip_addr(const char* ip, int port);
		explicit ip_addr(const char* url);
		void smtp(const char*, int port);
		int error() const;
		explicit operator bool() const;
		const std::string& ip() const;
		int port() const;
		const std::string& url() const;
		sockaddr* sock();
		int read_url(int beg, int end, char* to) const;
		int sa_family() const;
		int ssl() const;
		int ws() const;
		void ssl(int);
		bool operator ==(const ip_addr& rhs) const;
		~ip_addr();

	private:
		std::string _url{};
		std::string _ip{};
		int _port{};
		int _err{};
		int _ssl{};
		int _ws{};
		http_parser_url _http{};

		union {
			sockaddr addr;
			sockaddr_in sock4;
			sockaddr_in6 sock6;
		} _sock;
	};
}
