#pragma once
#include <libcutex.h>
#include <vector>
#include "cpx_core.h"

namespace cpx {
	class cpx_state_machine;
	class cpx_state_exit;
	class cpx_state_any;
	class cpx_state;

	class cpx_machine final : public cpx_core {
	public:
		DELETE_MOVE_COPY(cpx_machine)
		cpx_machine();
		explicit cpx_machine(const cpx_state_machine*);
		~cpx_machine() override;
		std::vector<const cpx_state*> states;
		const cpx_state_machine* parent{};
		const cpx_state* default_state{};
		const cpx_state* enter_state{};
		const cpx_state_any* any{};
		const cpx_state_exit* exit{};
		uint32_t ignore_action{};
		uint32_t ignore_condition{};
		std::string name;
		void read(const dynamic_vo& vo, const std::string& file);
	};
}
