#pragma once
#include "libcute.h"
#include "usr_worker.h"

namespace tvo {
	class type_currency;
}

namespace dvo {
	class data_currency;
}

namespace dvo {
	class worker_currency final : public usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_currency)
		ref_ptr<data_currency> get_data(int id) const;
		ref_ptr<data_currency> get_data(const std::string& k) const;
		ref_ptr<data_currency> try_get(int id) const;

		template <typename T, std::enable_if_t<std::is_enum_v<T>> * = nullptr>
		ref_ptr<data_currency> get_data(const T id) {
			return get_data(static_cast<int>(id));
		}

		void start() override;
		void stop() override;
	protected:
		void on_notifiction(const worker_notification*) override;
	private:
		void on_load() const;
		ref_ptr<data_currency> get_data(const tvo::type_currency* t) const;
	};
}
