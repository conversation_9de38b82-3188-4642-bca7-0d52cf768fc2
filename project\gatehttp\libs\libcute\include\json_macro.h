#ifndef JSON_MACRO_H
#define JSON_MACRO_H 
#include "macro_tool.h"
#include "json.hpp"
typedef nlohmann::json json_t;
/*


*/
#define cute_json_to(n)ja[#n] =jb.n;
#define cute_json_from(n)if(k==#n)v.get_to(jb.n);
#define JSON_GETTER(...)	\
for(auto&[k,v]:ja.items()){		\
	if(v.is_null())continue;	\
	CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_json_from, __VA_ARGS__)) \
}
/*


*/
#define JSON_TYPE_INCRUSIVE(T, ...)\
friend void to_json(json_t& ja, const T& jb) { \
CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_json_to, __VA_ARGS__)) } \
friend void from_json(const json_t& ja, T& jb) {\
	JSON_GETTER(__VA_ARGS__)\
}
/*


*/
#define JSON_TYPE_TO(T ,...)\
friend void to_json(json_t& ja, const T& jb) { \
CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_json_to, __VA_ARGS__)) }
/*


*/
#define JSON_TYPE_INLINE(T, ...)\
inline void to_json(json_t& ja, const T& jb) { \
CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_json_to, __VA_ARGS__)) } \
inline void from_json(const json_t& ja, T& jb) {\
	JSON_GETTER(__VA_ARGS__)\
}
/*


*/
#define cute_json_self_parse(n)if(k==#n)v.get_to(n);
#define JSON_CALL_PARSE(...)				\
for(auto&[k,v]:ja.items()){					\
	if(v.is_null())continue;				\
	CUTE_MACRO_EXPAND(CUTE_MACRO_PASTE(cute_json_self_parse, __VA_ARGS__)) \
}
/*


*/
#define JSON_TYPE_PARSE(method ,...)void method(const json_t& ja){JSON_CALL_PARSE(__VA_ARGS__)} 

#endif
