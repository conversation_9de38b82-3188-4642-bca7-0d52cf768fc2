#pragma once
#include <vector>
#include "logger.h"
#include "sys_exception.h"

namespace sys {
	template <typename T>
	class object_pool {
	public:
		using value_type = T;
		~object_pool() = default;
		object_pool() = default;

		object_pool(const object_pool& rhs) {
			_list = rhs._list;
			_freelist = rhs._freelist;
			_size = rhs._size;
		}

		object_pool& operator =(const object_pool& rhs) {
			if (&rhs != this) {
				_list = rhs._list;
				_freelist = rhs._freelist;
				_size = rhs._size;
			}
			return *this;
		}

		object_pool(object_pool&& rhs) noexcept {
			std::swap(rhs._list, _list);
			std::swap(rhs._freelist, _freelist);
			std::swap(rhs._size, _size);
		}

		object_pool& operator =(object_pool&& rhs) noexcept {
			if (&rhs != this) {
				std::swap(rhs._list, _list);
				std::swap(rhs._freelist, _freelist);
				std::swap(rhs._size, _size);
			}
			return *this;
		}

		T& at(const size_t idx) {
			if (idx >= _list.size() || _list[idx].next != ALLOCATE) {
				throw_e("out of range");
			}
			return _list[idx].value;
		}

		bool is_valid(const size_t idx) {
			if (idx >= _list.size()) {
				return false;
			}
			return _list[idx].next != ALLOCATE;
		}

		size_t occupy() {
			int64_t index{};
			if (_freelist != LIST_END) {
				index = _freelist;
				_freelist = _list[index].next;
				_list[index].next = ALLOCATE;
				++ _size;
				return index;
			}
			{
				index = _list.size();
				_list.emplace_back(entry_t{value_type{}, ALLOCATE});
				++ _size;
			}
			return index;
		}

		value_type& next() {
			size_t idx = occupy();
			return _list[idx].value;
		}

		void release(size_t idx) {
			if (idx >= _list.size()) {
				throw_e("out of range");
			}
			if (_list[idx].next != ALLOCATE) {
				log_error("release invalid index");
				return;
			}
			_list[idx].next = _freelist;
			_freelist = idx;
			auto* t = &_list[idx].value;
			t->~T();
			new(t)T();
			--_size;
		}

		void clear() {
			_freelist = LIST_END;
			_list.clear();
			_size = 0;
		}

		size_t size() const {
			return _size;
		}

		bool empty() const {
			return 0 == _size;
		}

		size_t pool_size() const {
			return _list.size();
		}

	private:
		static constexpr int64_t LIST_END = -1;
		static constexpr int64_t ALLOCATE = -2;

		struct entry_t {
			value_type value{};
			int64_t next{};
		};

		bool reach_next(size_t& pos) const {
			const size_t list_size = _list.size();
			while (++pos < list_size) {
				if (_list.at(pos).next == ALLOCATE) {
					return true;
				}
			}
			pos = 0;
			return false;
		}

		bool reach_first(size_t& pos) const {
			const size_t list_size = _list.size();
			for (;;) {
				if (pos >= list_size) {
					break;
				}
				if (_list.at(pos).next == ALLOCATE) {
					return true;
				}
				++pos;
			}
			pos = 0;
			return false;
		}

		entry_t* assert_position(size_t pos) {
			if (pos >= _list.size()) {
				throw_e("out of range");
			}
			entry_t* e = &_list.at(pos);
			if (e->next != ALLOCATE) {
				throw_e("out of range");
			}
			return e;
		}

		std::vector<entry_t> _list;
		int64_t _freelist{LIST_END};
		size_t _size{};
	public:
		struct iterator {
			using iterator_category = std::forward_iterator_tag;
			using value_type = T;
			using difference_type = ptrdiff_t;
			using pointer = T*;
			using reference = T&;
			friend class object_pool;

			iterator(object_pool* b, const size_t pos): _pool(b), _pos(pos) {
			}

			iterator(object_pool* b): _pool(b), _pos(0) {
				if (!b->reach_first(_pos)) {
					_pool = nullptr;
				}
			}

			iterator(): _pool(nullptr), _pos(0) {
			}

			iterator(const iterator&) = default;
			iterator& operator =(const iterator&) = default;

			iterator(iterator&& rhs) noexcept {
				swap(rhs);
			}

			iterator& operator =(iterator&& rhs) noexcept {
				if (this != &rhs) {
					swap(std::forward<iterator>(rhs));
				}
				return *this;
			}

			void swap(iterator&& rhs) noexcept {
				std::swap(rhs._pool, _pool);
				std::swap(rhs._pos, _pos);
			}

			~iterator() = default;

			bool operator ==(const iterator& rhs) {
				return rhs._pool == _pool && rhs._pos == _pos;
			}

			bool operator !=(const iterator& rhs) {
				return !(*this == rhs);
			}

			iterator& operator ++() {
				if (!_pool) {
					_pos = 0;
					return *this;
				}
				if (!_pool->reach_next(_pos)) {
					_pool = nullptr;
				}
				return *this;
			}

			iterator& operator ++(int) {
				auto temp = *this;
				++(*this);
				return temp;
			}

			T& operator *() const {
				if (!_pool) {
					throw_e("out of range");
				}
				return _pool->assert_position(_pos)->value;
			}

			T* operator->() const {
				if (!_pool) {
					throw_e("out of range");
				}
				return &_pool->assert_position(_pos)->value;
			}

			operator bool() const {
				return _pool && _pool->is_valid(_pos);
			}

		private:
			object_pool* _pool;
			size_t _pos;
		};

		struct const_iterator {
			using iterator_category = std::forward_iterator_tag;
			using value_type = T;
			using difference_type = ptrdiff_t;
			using pointer = T*;
			using reference = T&;

			const_iterator(const object_pool* b, const size_t pos): _pool(b), _pos(pos) {
			}

			const_iterator(const object_pool* b): _pool(b), _pos(0) {
				if (!b->reach_first(_pos)) {
					_pool = nullptr;
				}
			}

			const_iterator(): _pool(nullptr), _pos(0) {
			}

			const_iterator(const const_iterator&) = default;
			const_iterator& operator =(const const_iterator&) = default;

			const_iterator(const_iterator&& rhs) noexcept {
				swap(rhs);
			}

			const_iterator& operator =(const_iterator&& rhs) noexcept {
				if (this != &rhs) {
					swap(std::forward<const_iterator>(rhs));
				}
				return *this;
			}

			void swap(const_iterator&& rhs) noexcept {
				std::swap(rhs._pool, _pool);
				std::swap(rhs._pos, _pos);
			}

			~const_iterator() = default;

			bool operator ==(const const_iterator& rhs) {
				return rhs._pool == _pool && rhs._pos == _pos;
			}

			bool operator !=(const const_iterator& rhs) {
				return !(*this == rhs);
			}

			const_iterator& operator ++() {
				if (!_pool) {
					_pos = 0;
					return *this;
				}
				if (!reach_next(_pos)) {
					_pool = nullptr;
				}
				return *this;
			}

			const_iterator& operator ++(int) {
				auto temp = *this;
				++(*this);
				return temp;
			}

			const T& operator *() const {
				if (!_pool) {
					throw_e("out of range");
				}
				return _pool->assert_position(_pos)->value;
			}

			const T* operator->() const {
				if (!_pool) {
					throw_e("out of range");
				}
				return &_pool->assert_position(_pos)->value;
			}

			operator bool() const {
				return _pool && _pool->is_valid(_pos);
			}

		private:
			const object_pool* _pool;
			size_t _pos;
		};

		iterator begin() {
			if (0 == _size) {
				return iterator();
			}
			return iterator(this);
		}

		iterator end() {
			return iterator();
		}

		const_iterator begin() const {
			if (0 == _size) {
				return const_iterator();
			}
			return const_iterator(this);
		}

		const_iterator end() const {
			return const_iterator();
		}

		const_iterator cbegin() const {
			if (0 == _size) {
				return const_iterator();
			}
			return const_iterator(this);
		}

		const_iterator cend() const {
			return const_iterator();
		}

		iterator release(iterator it) {
			if (!it._pool) {
				return iterator();
			}
			release(it._pos);
			return ++ it;
		}
	};
}
