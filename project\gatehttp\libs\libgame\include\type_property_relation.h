#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "property_group.h"

namespace tvo {
	class type_property_relation final : public type_data {
	public:
		DELETE_MOVE_COPY(type_property_relation)
		type_property_relation();
		~type_property_relation() override;
		static void awake_all(const awake_context_t&);
		void awake(const awake_context_t&) override;
		const type_property* main() const;

	private:
		const type_property* _main{};

	public:
		TYPE_PROPERTY_RELATION_WRAPPER()
	};
}
