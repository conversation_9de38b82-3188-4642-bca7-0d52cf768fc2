#pragma once
#include "cpx_state.h"
#include <json_macro.h>

namespace cpx {
	class cpx_state_exit final : public cpx_state {
	public:
		DELETE_MOVE_COPY(cpx_state_exit)
		JSON_TYPE_INLINE(cpx_state_exit,
		                 stateName,
		                 priority,
		                 isDefault,
		                 isEnter)
		cpx_state_exit();
		~cpx_state_exit() override;
		state_type_t state_type() const override;
		rpx_state* obtain()const  override;
		void release(rpx_state*)const override;
	protected:
		void parse(const dynamic_vo&) override;
	};
}
