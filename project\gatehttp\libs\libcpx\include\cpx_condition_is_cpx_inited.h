#pragma once
#include <cpx_condition.h>
#include "cpx_condition_builtin_wrapper.h"

namespace cpx {
	class cpx_condition_is_cpx_inited final : public cpx_condition {
	public:
		DELETE_MOVE_COPY(cpx_condition_is_cpx_inited)
		CPXC_CPX_CONDITION_IS_CPX_INITED_WRAPPER()
		cpx_condition_is_cpx_inited();
		~cpx_condition_is_cpx_inited() override;
	protected:
		bool check_validate(const cpx_transition_context* ctx) const override;
	};
}
