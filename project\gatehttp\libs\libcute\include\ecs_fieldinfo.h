#pragma once
#include "libcute.h"
#include <pb.h>

namespace ecs {
	struct fieldinfo_t {
		DELETE_MOVE_COPY(fieldinfo_t)
		unsigned type_id; /* PB_T* enum */
		int message;
		pb_Field* pb;
		size_t width;
		int32_t number;
		bool required;
		bool delete_flag;
		//const char* name() const;
		fieldinfo_t();
		bool repeated() const;
		bool is_name(const char* str) const;
		operator bool() const;
		~fieldinfo_t();
	};
}
