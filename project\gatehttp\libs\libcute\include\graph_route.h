#pragma once
#include <shared_mutex>
#include "consistent_ring.h"
#include "libcute.h"
#include "route_stat.h"
#include "service_form.h"
#include "service_t.h"
#include "tcp_peer.h"

namespace sys {
	struct service_d;
	class graph_central;
	class cluster_manager;

	class graph_route {
	public:
		friend class cluster_manager;
		typedef void (*message_callback)(const ref_ptr<service_form>&);
		typedef void (*extra_connection_stat)(std::vector<route_stat>&);
		DELETE_MOVE_COPY(graph_route)
		explicit graph_route(cluster_manager*);
		~graph_route();
		void enter();
		void update(time_t);
		void exit();
		void stat(std::vector<route_stat>&) const;
		extra_connection_stat extra_stat{};

	private:
		bool done{};
		hashmapx<route_t, ref_ptr<net::tcp_peer>> connections{};

	public:
		ref_ptr<net::tcp_peer> any();
		ref_ptr<net::tcp_peer> get(const service_t&);
		ref_ptr<net::tcp_peer> get(const route_t&);

	private:
		ref_ptr<net::tcp_peer> find_single(const route_t&);
		ref_ptr<net::tcp_peer> find_assign(const service_t&);
		ref_ptr<net::tcp_peer> find_any();

	private:
		using lock_read = std::shared_lock<std::shared_mutex>;
		using lock_write = std::lock_guard<std::shared_mutex>;
		void on_conn_event(const net::net_event*);
		void recv(net::net_base*, const ref_ptr<service_form>&);
		void recv_shakehand(const ref_ptr<service_form>&);
		void on_conn_lost(net::net_base*);
		bool all_connection_ok() const;
		void retry_connect(time_t);
		void send_tick(time_t);
		std::vector<ref_ptr<net::tcp_peer>> alives() const;
		cluster_manager* _manager{};
		time_t _tick_time{};
		time_t _bad_time{};
		std::atomic<bool> _running{};
		int _maxretry{};
		size_t _index{};
		std::shared_mutex _mutex;

		struct {
			consistent_ring map{};
			bool dirty{};
		} rings;
	};
}
