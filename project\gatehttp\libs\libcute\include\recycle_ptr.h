#pragma once
#include <cstddef>
#include <memory>
#include "recycle_pool.h"
#include "sys_exception.h"

namespace mem {
	template <typename T, typename Locker, size_t BlockSize>
	class recycle_ptr {
	public:
		using self_type = recycle_ptr<T, Locker, BlockSize>;
		using element_type = T;

		constexpr recycle_ptr() noexcept: px(nullptr) {
		}

		recycle_ptr(T* p) : px(p) {
			if (px != nullptr) {
				add_cnt_ref(px);
			}
		}

		recycle_ptr(self_type const& rhs) : px(rhs.px) {
			if (px) add_cnt_ref(px);
		}

		~recycle_ptr() {
			if (px) release_cnt_ref(px);
			px = nullptr;
		}

		// Move support
		recycle_ptr(self_type&& rhs) noexcept : px(rhs.px) { rhs.px = nullptr; }

		recycle_ptr& operator=(self_type&& rhs) noexcept {
			self_type(static_cast<self_type&&>(rhs)).swap(*this);
			return *this;
		}

		recycle_ptr& operator=(recycle_ptr const& rhs) {
			if (this != &rhs) {
				self_type(rhs).swap(*this);
			}
			return *this;
		}

		element_type* get() const noexcept { return px; }

		element_type& operator*() const {
			if (!px) {
				throw_e("using null_ptr");
			}
			return *px;
		}

		element_type* operator->() const {
			if (!px) {
				throw_e("using null_ptr");
			}
			return px;
		}

		operator bool() const noexcept { return px != nullptr; }
		// operator! is redundant, but some compilers need it
		bool operator!() const noexcept { return px == nullptr; }

		bool operator ==(const recycle_ptr& rhs) const noexcept {
			return rhs.px == px;
		}

		bool operator !=(const recycle_ptr& rhs) const noexcept {
			return !(*this == rhs);
		}

		void swap(recycle_ptr& rhs) noexcept {
			element_type* tmp = px;
			px = rhs.px;
			rhs.px = tmp;
		}

		static recycle_ptr make_ptr() {
			return recycle_ptr(s_pool.obtain());
		}

	private:
		using pool_type = recycle_pool<T, Locker, BlockSize>;
		static inline pool_type s_pool{};

		static void add_cnt_ref(element_type* p) {
			s_pool.add_ref(p);
		}

		static void release_cnt_ref(element_type* p) {
			s_pool.release_ref(p);
		}

		element_type* px{};
	};

	template <typename T, typename L, size_t B>
	bool operator!=(recycle_ptr<T, L, B> const& p, std::nullptr_t) noexcept {
		return p.get() != nullptr;
	}

	template <typename T, typename L, size_t B>
	bool operator!=(std::nullptr_t, recycle_ptr<T, L, B> const& p) noexcept {
		return p.get() != nullptr;
	}

	template <typename T, typename L, size_t B>
	void swap(recycle_ptr<T, L, B>& lhs, recycle_ptr<T, L, B>& rhs) noexcept {
		lhs.swap(rhs);
	}
}

#include <functional> // hash

namespace std {
	template <typename T, typename L, size_t B>
	struct hash<mem::recycle_ptr<T, L, B>> {
		size_t operator()(const mem::recycle_ptr<T, L, B>& v) const noexcept {
			return static_cast<size_t>(v.get());
		}
	};
}
