#pragma once
#include <libcutex.h>
#include "cpx_core.h"

namespace cpx {
	class cpx_machine;
	class cpx_transition;
	struct cpx_transition_context;

	class cpx_condition : public cpx_core {
	public:
		struct read_context {
			cpx_machine* machine{};
			std::string file{};
		};

		DELETE_MOVE_COPY(cpx_condition)
		bool check(const cpx_transition_context*) const;
		void read(const dynamic_vo&, const read_context&);
		size_t numchildren() const;
		cpx_transition* transition{};
		~cpx_condition() override;
		virtual void on_read();
		virtual int class_id() const noexcept = 0;
		virtual const char* class_n() const noexcept = 0;
		size_t size() const;
		const cpx_condition* child_at(size_t at) const;

	protected:
		virtual bool check_validate(const cpx_transition_context* ctx) const = 0;
		virtual void parse(const dynamic_vo&) =0;
		cpx_condition();

	private:
		std::vector<const cpx_condition*> _children;
	};
}
