#pragma once
#include <vector>
#include <libcute.h>
#include "usr_worker.h"

namespace skill {
	class idvo_skill;
}

namespace buff {
	class worker_buff;
	class buff_runner;
	class input_t;

	class ibuff_retriever {
	public:
		DELETE_MOVE_COPY(ibuff_retriever)
		virtual ~ibuff_retriever();
		/*get the runner to update*/
		virtual void fetch(const input_t*, std::vector<buff_runner*>&) = 0;
		/*get the runner to clear*/
		virtual void clear(std::vector<buff_runner*>&) = 0;
		virtual void start() = 0;
		virtual void stop() = 0;

	protected:
		explicit ibuff_retriever();
	};

	class worker_buff final : public dvo::usr_worker {
	public:
		DELETE_MOVE_COPY(worker_buff)
		TYPE_CAST_IMPLEMENT(worker_buff)
		worker_buff();
		~worker_buff() override;
		void start() override;
		void stop() override;
		void fetch(const input_t*, std::vector<buff_runner*>&) const;
		void clear() const;

		template <typename T, typename... Args,
		          std::enable_if_t<std::is_base_of_v<ibuff_retriever, T>>* = nullptr>
		void add_fetch(Args... args) {
#if DEBUG
			for (auto* v : _retrievers) {
				const T* r = dynamic_cast<T*>(v);
				if (r) {
					auto err = sys::str_format("%s already register", typeid(T).name());
					throw_e(err.c_str());
				}
			}
#endif
			T* n = new T((args)...);
			if (_started) {
				n->start();
			}
			_retrievers.emplace_back(n);
		}

		void add_fetch(ibuff_retriever*);
		void delete_fetch(ibuff_retriever*);
		void remove_fetch(ibuff_retriever*);

	private:
		void on_notifiction(const dvo::worker_notification*) override;
		void update(const dvo::update_context*) override;
		void save() const;
		void load();
		std::vector<ibuff_retriever*> _retrievers{};
		bool _started{};
		bool _loaded{};
	};
}
