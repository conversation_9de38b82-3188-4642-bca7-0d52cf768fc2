#pragma once
#include <string>
#include "pb.h"
#include "pb_context.h"

class pb_stream {
public:
	pb_stream();
	explicit pb_stream(const std::string&);
	pb_stream(const char* buf, size_t size);
	pb_stream(const pb_stream&);
	pb_stream(pb_stream&&) noexcept;
	~pb_stream();
	pb_stream& operator =(const pb_stream&);
	pb_stream& operator =(pb_stream&&) noexcept;
	pb_stream& copy(const pb_stream&);
	pb_stream& swap(pb_stream&&) noexcept;
	pb_stream& operator +=(const pb_stream&);
	pb_slice slice() const;
	pbd_slice sliced() const;
	/*compress src into self*/
	int compress(pb_slice src, int level = -1, size_t* outsize = nullptr);
	/*uncompress src into self*/
	int uncompress(pb_slice src, size_t* outsize = nullptr);
	int read_file(const char* file);
	int write_file(const char* file) const;
	bool operator ==(const pb_stream&) const;
	bool operator !=(const pb_stream&) const;
	pb_stream& append(pb_slice);
	pb_stream& addchar(char);
	/*add \0 to end of stream*/
	pb_stream& stringfy();
	pb_buffer* buf();
	char* base() const;
	pb_stream& reserve(size_t);
	size_t size() const;
	size_t capacity() const;
	bool empty() const;
	void clear();

private:
	pb_buffer _buf;
};
