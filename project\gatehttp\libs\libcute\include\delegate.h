#pragma once
#include "delegate_internal.h"
#include "logger.h"
#include "thread_locker.h"

template <typename ...T>
class delegate_vo {
};

template <typename Tlocker, typename R, typename ...Args>
class delegate_vo<Tlocker, R(Args ...)> {
public:
	using this_type = delegate_vo<Tlocker, R(Args ...)>;
	using delegate_call = R(*)(Args ...);
	template <typename C>
	using delegate_mcall = R(C::*)(Args ...);
	using entry_t = delegate_internal::delegate_entry<R(Args ...)>;
	using call_t = delegate_internal::call_type;
	using locker_type = Tlocker;
	using thread_guard_t = sys::thread_guard<locker_type>;
	delegate_vo() = default;

	delegate_vo(const delegate_vo& rhs) {
		_entries = rhs._entries;
	}

	delegate_vo(delegate_vo&& rhs) noexcept {
		_entries = std::move(rhs._entries);
	}

	delegate_vo& operator =(const delegate_vo& rhs) {
		_entries = rhs._entries;
		return *this;
	}

	delegate_vo& operator =(delegate_vo&& rhs) noexcept {
		if (&rhs != this) {
			_entries = std::move(rhs._entries);
		}
		return *this;
	}

	~delegate_vo() = default;

	this_type& add(delegate_call fn) {
		thread_guard_t g(_mutex);
		for (auto& e : _entries) {
			if (e.type_ == call_t::FUNC_PTR &&
				e.dcall_ == fn) {
				e.cancel_ = false;
				return *this;
			}
		}
		_entries.emplace_back(entry_t(fn));
		return *this;
	}

	this_type& remove(delegate_call fn) {
		thread_guard_t g(_mutex);
		for (auto& e : _entries) {
			if (e.type_ == call_t::FUNC_PTR && fn == e.dcall_) {
				e.cancel_ = true;
				_purge = true;
			}
		}
		if (!_invoking) purge();
		return *this;
	}

	template <class C>
	this_type& add(C* obj, delegate_mcall<C> fn) {
		thread_guard_t g(_mutex);
		using member_call_t = delegate_internal::class_member_method<C, R, Args...>;
		auto func = delegate_internal::make_class_member(fn, obj);
		for (auto& e : _entries) {
			if (e.type_ != call_t::MEMBER_CALL)continue;
			auto* f = e.mcall_.template target<member_call_t>();
			if (f && *f == func) {
				e.cancel_ = false;
				return *this;
			}
		}
		_entries.emplace_back(entry_t(func, obj));
		return *this;
	}

	template <class C>
	this_type& remove(C* obj, delegate_mcall<C> fn) {
		thread_guard_t g(_mutex);
		using member_call_t = delegate_internal::class_member_method<C, R, Args...>;
		auto func = delegate_internal::make_class_member(fn, obj);
		for (auto& e : _entries) {
			if (e.type_ != call_t::MEMBER_CALL)continue;
			auto* f = e.mcall_.template target<member_call_t>();
			if (f && *f == func) {
				e.cancel_ = true;
				_purge = true;
				break;
			}
		}
		if (!_invoking)purge();
		return *this;
	}

	template <class C>
	this_type& remove(C* obj) {
		thread_guard_t g(_mutex);
		using member_call_t = delegate_internal::class_member_method<C, R, Args...>;
		//auto func = delegate_internal::make_class_member(fn, obj);
		for (auto& e : _entries) {
			if (e.type_ != call_t::MEMBER_CALL)continue;
			auto* f = e.mcall_.template target<member_call_t>();
			if (f && f->ptr() == obj) {
				e.cancel_ = true;
				_purge = true;
				break;
			}
		}
		if (!_invoking)purge();
		return *this;
	}

	this_type& remove(void* body) {
		thread_guard_t g(_mutex);
		for (auto& e : _entries) {
			if (e.type_ != call_t::MEMBER_CALL) {
				continue;
			}
			if (e.instance_ == body) {
				e.cancel_ = true;
				_purge = true;
			}
		}
		if (!_invoking) {
			purge();
		}
		return *this;
	}

	this_type& clear() {
		thread_guard_t g(_mutex);
		_entries.clear();
		return *this;
	}

	void operator()(Args ...args) {
		invoke(std::forward<Args>(args)...);
	}

	R request(Args ... args) {
		return p_request(std::forward<Args>(args)...);
	}

	template <class C, typename Fn, typename ...FArgs,
	          std::enable_if_t<std::is_invocable_v<Fn, C*, FArgs...>> * = nullptr>
	void foreach(Fn fn, FArgs ...args) {
		thread_guard_t g(_mutex);
		using member_call_t = delegate_internal::class_member_method<C, R, Args...>;
		_invoking = true;
		for (entry_t& e : _entries) {
			if (e.type_ != call_t::MEMBER_CALL)continue;
			member_call_t* f = e.mcall_.template target<member_call_t>();
			if (!f)continue;
			fn(f->ptr(), (args)...);
		}
		_invoking = false;
		purge();
	}

	size_t size() {
		return _entries.size();
	}

	bool empty() {
		return _entries.empty();
	}

private:
	using delegate_func = std::function<R(Args ...)>;
	std::vector<entry_t> _entries{};
	bool _purge{};
	bool _invoking{};
	locker_type _mutex;

	R p_request(Args&&... args) {
		if (_entries.empty()) {
			return R();
		}
		entry_t& e = _entries[0];
		if (e.type_ == call_t::FUNC_PTR) {
			return e.dcall_(std::forward<Args>(args)...);
		}
		return e.mcall_(std::forward<Args>(args)...);
	}

	void invoke(Args&& ...args) {
		thread_guard_t g(_mutex);
		_invoking = true;
		std::size_t idx = 0;
		while (true) {
			if (idx >= _entries.size())break;
			entry_t& e = _entries[idx];
			if (!e.cancel_) {
				try {
					if (e.type_ == call_t::FUNC_PTR) {
						e.dcall_(std::forward<Args>(args)...);
					} else {
						e.mcall_(std::forward<Args>(args)...);
					}
				} catch (std::exception& ex) {
					log_error("caught %s", ex.what());
					e.cancel_ = true;
				}
				catch (...) {
					log_error("caught <unknow> error");
					e.cancel_ = true;
				}
			}
			idx ++;
			if (e.cancel_) {
				_purge = true;
			}
		}
		_invoking = false;
		if (_purge)purge();
	}

	void purge() {
		const auto it = std::remove_if(_entries.begin(),
		                               _entries.end(),
		                               [](const entry_t& e) {
			                               return e.cancel_;
		                               });
		_entries.erase(it, _entries.end());
		_purge = false;
	}
};

template <typename R, typename ...Args>
using delegate = delegate_vo<sys::thread_lock, R, Args...>;
template <typename R, typename ...Args>
using unsafe_delegete = delegate_vo<sys::thead_no_lock, R, Args...>;
