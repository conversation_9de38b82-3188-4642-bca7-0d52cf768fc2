#pragma once
#include <mutex>
#include "net_stream_writer.h"
#include <queue>

namespace net {
	struct tcp_stream_writer_safe final : net_stream_writer {
		DELETE_MOVE_COPY(tcp_stream_writer_safe)
		tcp_stream_writer_safe();
		~tcp_stream_writer_safe() override;
		int write(const char*, size_t) override;
		int write(const recycle_stream&) override;
		int flush() override;
		void get_uvbuf(uv_buf_t*) override;
		size_t size() override;
		void on_write_done() override;
		int ping(time_t) override;
		int pong(time_t) override;

		void stat(stat_writer&, time_t) override;

	private:
		int private_flush();

	public:
		

	private:
		recycle_stream _writing{};
		recycle_stream _sending{};

		struct {
			size_t length{};
			size_t count{};
			size_t wait{};
		} _status;

		time_t _stat_time{};
		std::recursive_mutex _tslock;
		std::queue<recycle_stream> _wait{};
	};
}
