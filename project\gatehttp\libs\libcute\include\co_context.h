#pragma once
#include <libcopp/coroutine/coroutine_context.h>
#include <libcopp/stack/stack_allocator.h>

#include "co_stack_allocate_pool.h"
#include  "libcute.h"

namespace co {
	class co_context : public copp::coroutine_context {
	public:
		using coroutine_context_type = coroutine_context;
		using base_type = coroutine_context;
		using allocator_type = stack_allocate_pool; 
		//using allocator_type = copp::allocator::default_statck_allocator;
		//using this_type = co_context;
		using ptr_t = libcopp::util::intrusive_ptr<co_context>;
		using callback_t = callback_t;
		COROUTINE_CONTEXT_BASE_USING_BASE(base_type)
	public:
		size_t use_count() const noexcept;
		int stack_in();
		int stack_out();
		int stack_deep() const;
		~co_context();
	private:
		DELETE_MOVE_COPY(co_context)
		explicit co_context(allocator_type alloc) noexcept;
		explicit co_context(allocator_type&& alloc) noexcept;
		allocator_type _alloc; /** stack allocator **/
		std::atomic<size_t> _refcnt;
		std::atomic<int> _deep;
		friend void intrusive_ptr_add_ref(co_context* p);
		friend void intrusive_ptr_release(co_context* p);
	public:
		static ptr_t create(callback_t&& runner, allocator_type& alloc,
		                    size_t stack_sz = 0,
		                    size_t private_buffer_size = 0,
		                    size_t coroutine_size = 0);
		static ptr_t create(callback_t&& runner,
		                    size_t stack_size = 0,
		                    size_t private_buffer_size = 0,
		                    size_t coroutine_size = 0) noexcept;
		static ptr_t create(int (*fn)(void*),
		                    allocator_type& alloc,
		                    size_t stack_size = 0,
		                    size_t private_buffer_size = 0,
		                    size_t coroutine_size = 0) noexcept;
		static ptr_t create(int (*fn)(void*),
		                    size_t stack_size = 0,
		                    size_t private_buffer_size = 0,
		                    size_t coroutine_size = 0) noexcept;

		template <class TRunner>
		static ptr_t create(TRunner* runner, allocator_type& alloc,
		                    size_t stack_size = 0,
		                    size_t private_buffer_size = 0,
		                    size_t coroutine_size = 0) noexcept {
			if (nullptr == runner) {
				return create(callback_t(), alloc, stack_size, private_buffer_size, coroutine_size);
			}
			return create([runner](void* private_data) { return (*runner)(private_data); }, alloc, stack_size,
			              private_buffer_size, coroutine_size);
		}

		template <class TRunner>
		static ptr_t create(TRunner* runner,
		                    size_t stack_size = 0,
		                    size_t private_buffer_size = 0,
		                    size_t coroutine_size = 0) noexcept {
			return create([runner](void* private_data) {
				              return (*runner)(private_data);
			              },
			              stack_size,
			              private_buffer_size,
			              coroutine_size);
		}

		static void set_minimun_stack_size(size_t);
	};
}
