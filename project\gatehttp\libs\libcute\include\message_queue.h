#pragma once
#include <libcute.h>
#include <recycle_stream.h>
#include <hashmapx.h>
#include "pb_base.h"
#include "route_t.h"
#include "service_form.h"
#define MAX_MESSAGE_BLOCK_SIZE (1024* 1024 * 2) // 2M

namespace sys {
	struct consistent_key;
}

namespace mem {
	class message_queue {
	public:
		DELETE_MOVE_COPY(message_queue)
		~message_queue();
		message_queue();
		recycle_stream stream(const route_t&);
		void flush();
		int send(const pb_base*, const route_t&, int64_t chash = 0);
		int send(const pb_base*, const sys::consistent_key&); 
	private:
		std::mutex _mqlock{};
		sys::hashmapx<route_t, recycle_stream> _outputs{};
	};
}
