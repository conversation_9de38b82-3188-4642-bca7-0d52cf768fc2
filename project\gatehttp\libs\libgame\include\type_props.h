#pragma once
#include <btree_list.h>
#include <string>
#include <type_data.h>
#include "prop_extra_vo.h"
#include "prop_propertybase.h"
#include "prop_use_tree.h"
#include "thing_group.h"
#include <vector2_int.h>
#include "typedata_wrapper_libgame.h"
#include "props_use_limit.h"

namespace dvo {
	class usr_info;
}

namespace ds {
	class bag_container;
}

namespace tvo {
	class type_hero;
	class type_props;
	class type_props_group;
	class type_body_position;

	class type_props final : public type_data {
	public:
		static void awake_all(const awake_context_t & );
		bool equipable() const;
		const type_props_group* group() const;
		const std::string& groupkey() const;
		void get_active_bag(const dvo::usr_info* usr,
		                    ds::bag_container** bag,
		                    ds::bag_container** body) const;
		bool support_body(const ds::bag_container* body) const;
		int get_place_option(type_body_position** main, type_body_position** aux) const;
		bool match_career(const dvo::usr_info* usr) const;
		int size_mode() const;
		bool get_avatar(std::string& to, const type_hero* hero, const type_body_position* bp) const;
		cpx::btree_list<props::prop_extra_vo> extra_tree;
		cpx::btree_list<props::prop_propertybase> wash_tree;
		cpx::btree_list<props::prop_propertybase> property_tree;
		cpx::btree_list<props::prop_use_tree> usage_tree;
		std::vector<type_body_position*> place_list;
		std::vector<type_body_position*> occupied_list;
		std::vector<type_body_position*> extra_render_list;
		bool use_need_buff{};
		std::vector<const props::props_use_limit*> limits;

	private: 
		const type_props_group* _group{};
	public :
		TYPE_PROPS_WRAPPER()
	};
}
