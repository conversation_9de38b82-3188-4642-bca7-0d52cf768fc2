#pragma once
#include <cstddef>
#include <cassert>
#include <mutex>
#include "thread_locker.h"
#include <logger.h>

namespace mem {
	/*
		different from memory_pool,  
		instead of ~T(),
		void (T*)::clear() must be implement and will be call as backing pool, 
	*/
	template <typename T, typename Locker, size_t BlockSize = 4096>
	class recycle_pool {
	public :
		typedef T value_type;
		using locker_type = Locker;
		using thread_guard = sys::thread_guard<locker_type>;
	private:
		struct mem_slot {
			union {
				mem_slot* next;
				size_t cnt_ref{};
			};

			value_type element{};
		};

		static_assert(BlockSize >= 4 * sizeof(mem_slot), "BlockSize too small.");
		static_assert(sizeof(size_t) == sizeof(void*), "differ size of union.");
		static constexpr size_t element_offset = offsetof(mem_slot, element);
		DELETE_MOVE_COPY(recycle_pool)
	public:
		recycle_pool() noexcept = default;

		~recycle_pool() noexcept {
			{
				auto* m = _free_slot;
				_free_slot = nullptr;
				while (m) {
					m->element.~value_type();
					m = m->next;
				}
			}
			{
				block_slot* b = _current_block;
				_current_block = nullptr;
				while (b) {
					block_slot* prev = b->next;
					operator delete(reinterpret_cast<void*>(b));
					b = prev;
				}
			}
		}

		value_type* obtain() {
			thread_guard lock(_mutex);
			return private_obtain();
		}

	private:
		value_type* private_obtain() {
			if (_free_slot != nullptr) {
				mem_slot* m = _free_slot;
				_free_slot = m->next;
				m->next = nullptr;
				m->cnt_ref = 0;
				return &m->element;
			}
			if (_slot_bottom >= _slot_top) {
				allocate_block();
			}
			{
				mem_slot* m = _slot_bottom++;
				new(m)mem_slot();
				return &m->element;
			}
		}

		void private_release(mem_slot* m) {
			if (!m)return;
			m->element.clear();
			m->next = _free_slot;
			_free_slot = m;
		}

		void swap(recycle_pool&& rhs) noexcept {
			thread_guard a(_mutex);
			thread_guard b(rhs._mutex);
			std::swap(_current_block, rhs._current_block);
			std::swap(_slot_bottom, rhs._slot_bottom);
			std::swap(_slot_top, rhs._slot_top);
			std::swap(_free_slot, rhs._free_slot);
		}

		struct block_slot {
			block_slot* next;
		};

		locker_type _mutex;
		block_slot* _current_block{};
		mem_slot* _slot_bottom{};
		mem_slot* _slot_top{};
		mem_slot* _free_slot{};

		static size_t pad_pointer(char* p, const size_t align) noexcept {
			const auto result = reinterpret_cast<uintptr_t>(p);
			return (align - result) % align;
		}

		void allocate_block() {
			auto* new_block = static_cast<char*>(operator new(BlockSize));
			auto* slot = reinterpret_cast<block_slot*>(new_block);
			slot->next = _current_block;
			_current_block = slot;
			// Pad block body to satify the alignment requirements for elements
			char* body = new_block + sizeof(block_slot);
			const size_t padding = pad_pointer(body, alignof(mem_slot));
			_slot_bottom = reinterpret_cast<mem_slot*>(body + padding);
			_slot_top = reinterpret_cast<mem_slot*>(new_block + BlockSize - sizeof(mem_slot));
		}

	public:
#define value_to_member(x)((mem_slot*)((char*)(x) - element_offset))

		void add_ref(value_type* p) {
			if (!p) {
				return;
			}
			thread_guard lock(_mutex);
			++ value_to_member(p)->cnt_ref;
		}

		void release_ref(value_type* p) {
			if (!p) {
				return;
			}
			thread_guard lock(_mutex);
			auto* m = value_to_member(p);
#if DEBUG
			assert(m->cnt_ref > 0 && "mem->cnt_ref == 0 ");
#else
			if(m->cnt_ref == 0 ){
				log_error_stack("mem->cnt_ref == 0 "); 
				return;
			}
#endif
			-- m->cnt_ref;
			if (0 == m->cnt_ref) {
				private_release(m);
			}
		}
#undef value_to_member
	};
}
