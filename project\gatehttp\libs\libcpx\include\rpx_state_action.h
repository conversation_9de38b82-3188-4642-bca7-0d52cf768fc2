#pragma once
#include "btree_status.h"
#include "cpx_input.h"
#include "rpx_state.h"

namespace cpx {
	class cpx_state_action;

	class rpx_state_action final : public rpx_state {
	public:
		DELETE_MOVE_COPY(rpx_state_action)
		explicit rpx_state_action();
		~rpx_state_action() override;
		void enter() override;
		void update(const tick_context*) override;
		void exit() override;
		int priority() const override;
		runtime_type rtype() const override;
	private:
		void release();
		void update_action(const time_vo& tick, bt::btree_status& st);
		void reset_children();
		bt::btree_status update_tree();
		cpx_input _input{};
		cpx_output _output{};
		size_t _index{};
		int _loop{};
		int _maxloops{};
		bt::btree_status _status{};
		const cpx_state_action* _cpx{};
	};
}
