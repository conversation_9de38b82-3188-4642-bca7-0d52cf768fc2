#pragma once
namespace sys {
	enum cluster_op_code {
		CLUSTER_OP_PREPARE,
		CLUSTER_OP_TICK,
		CLUSTER_OP_JOIN,
	};

	inline const char* cluster_op_code_str(const cluster_op_code c) {
		switch (c) {
			case CLUSTER_OP_PREPARE: return "Prepare";
			case CLUSTER_OP_TICK: return "Tick";
			case CLUSTER_OP_JOIN: return "Join";
			default: return "return <unknown>";
		}
	}

	enum cluster_service_status {
		CLUSTER_ST_IDLE,
		CLUSTER_ST_PREPARE,
		CLUSTER_ST_JOIN,
		CLUSTER_ST_KILL
	};

	inline const char* cluster_service_status_str(const cluster_service_status c) {
		switch (c) {
			case CLUSTER_ST_IDLE: return "Idle";
			case CLUSTER_ST_PREPARE: return "Prepare";
			case CLUSTER_ST_JOIN: return "Join";
			case CLUSTER_ST_KILL: return "Kill";
			default: return "return <unknown>";
		}
	}
}
