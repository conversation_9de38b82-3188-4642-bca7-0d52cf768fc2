#pragma once
#include "net_stream_reader.h"
#include "net_stream_writer.h"
#include "web_message.h"

namespace net {
	struct web_parser;
	class web_peer;

	struct web_stream_reader final : net_stream_reader {
		DELETE_MOVE_COPY(web_stream_reader)
		web_stream_reader();
		~web_stream_reader() override;
		int read(ssize_t, const uv_buf_t*) override;
		void stat(stat_reader&, time_t) override;
		web_parser* parser{};
	};

	class web_stream_write final : public net_stream_writer {
	public:
		int write(const char*, size_t) override;
		int flush() override;
		void get_uvbuf(uv_buf_t*) override;
		size_t size() override;
		size_t capacity() const;
		size_t space() const;
		void on_write_done() override;
		int write(const recycle_stream&) override;
		int ping(time_t) override;
		int pong(time_t) override;

	private:
		pb_stream _stream{};
	};
}
