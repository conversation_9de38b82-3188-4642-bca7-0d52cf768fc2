#pragma once
#ifndef CO_THREAD_POOL_H
#define CO_THREAD_POOL_H 
#pragma once
#include <vector>
#include <queue>
#include <memory>
#include <future>
#include <functional>
#include "libcute.h"
#include <type_traits>
#include <uv.h>
#include "co_thread.h"
#include "logger.h"

class co_thread_pool {
public:
	typedef std::function<void()> thread_job;
	explicit co_thread_pool(size_t);
	DELETE_MOVE_COPY(co_thread_pool)
	template <class F, class... Args>
	auto enqueue(F&& f, Args&&... args)
	-> std::future<std::invoke_result_t<decltype(f), Args ...>>;
	size_t size() const;
	size_t count_task() const;
	~co_thread_pool();
private:
	class entry {
	public:
		explicit entry(co_thread_pool*, size_t idx);
		DELETE_MOVE_COPY(entry)
		void work();
		void start();
		void add(thread_job);
		thread_id id_;
		co_thread_pool* pool_;
		std::queue<thread_job> jobs_;
		size_t index_;
		~entry();
		void stop();
	private:
		co_thread::ptr_t _ctx;
		int _yielding;
	};

	size_t _cnt;
	std::vector<entry*> _threads;
	std::atomic<bool> _stop;
};

template <class F, class ... Args>
auto co_thread_pool::enqueue(F&& f, Args&&... args) -> std::future<std::invoke_result_t<decltype(f), Args ...>> {
	using R = std::invoke_result_t<decltype(f), Args ...>;
	auto task = std::make_shared<std::packaged_task<R()>>(
		std::bind(std::forward<F>(f), std::forward<Args>(args)...)
	);
	std::future<R> res = task->get_future();
	{
		if (_stop) {
			log_error("thread pool stopped");
		}
		else {
			const size_t i = (_cnt ++) % _threads.size();
			_threads[i]->add([task]() { (*task)(); });
		}
	}
	return res;
}
#endif
