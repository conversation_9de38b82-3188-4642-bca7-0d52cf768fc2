#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include <thing_group.h>

namespace tvo {
	class type_prop_formular final : public type_data {
	public:
		DELETE_MOVE_COPY(type_prop_formular)
		type_prop_formular();
		~type_prop_formular() override;
		static void awake_all(const awake_context_t&);
	public:
		//enums decl! @see TYPE_PROP_FORMULAR_WRAPPER() in typeclass_define.h
		enum type_t {
			kCombine,
			kResolve,
			kForge
		};

		TYPE_PROP_FORMULAR_WRAPPER()
	};
}
