#pragma once
#include <libcute.h>
#include <ref_ptr.h>
#include <system_error.h>
#include "service_form.h"
#include "service_proto_core.h"
#include "tcp_conn.h"
#include "time_tools.h"

namespace net {
	class tcp_awaiter_base {
		CUTE_REF_PTR_CLASS_DECL(tcp_awaiter_base)
		DELETE_MOVE_COPY(tcp_awaiter_base)

	public:
		virtual ~tcp_awaiter_base();
		virtual bool recv(const ref_ptr<service_form>&, time_t) = 0;
		virtual bool overdue(time_t) = 0;

	protected:
		tcp_awaiter_base();
	};

	template <typename TRequest, typename TResponse, typename TArgs>
	class tcp_awaiter final : public tcp_awaiter_base {
	public:
		typedef void (*callback)(tcp_awaiter*, const TArgs&);
		DELETE_MOVE_COPY(tcp_awaiter)
		static_assert(std::is_base_of_v<pb_base, TRequest>);
		static_assert(std::is_base_of_v<pb_base, TResponse>);

		tcp_awaiter(const callback cb, TArgs&& args): _args(std::forward<TArgs>(args)), _cb(cb) {
		}

		tcp_awaiter(const callback cb, const TArgs& args): _args(args), _cb(cb) {
		}

		~tcp_awaiter() override = default;

		bool overdue(const time_t now) override {
			if (now > _due) {
				_cb(this, _args);
				return true;
			}
			return false;
		}

		int request(const TRequest& req, const route_t& to) {
			tcp_conn tcp(to);
			const int err = tcp.send(&req, service_form::PB_FUNC_REQUEST);
			if (err == ERR_OK) {
				_form_id = tcp.form_id();
				_due = cute_timestamp() + 30;
			}
			return err;
		}

		TResponse response() {
			if (_back) {
				return _back->obj<TResponse>();
			}
			return TResponse{};
		}

		int error() const {
			return _error;
		}

		int form_id() const {
			return _form_id;
		}

		bool recv(const ref_ptr<service_form>& form, const time_t now) override {
			if (now >= _due) {
				_error = ERR_TCP_REQUEST_FAIL;
				_cb(this, _args);
				return false;
			}
			if (form->form_id != _form_id) {
				return false;
			}
			if (form->class_id == PBT_service_fail) {
				_error = PBT_service_fail;
				_cb(this, _args);
			} else if (form->class_id == TResponse::assign_type()) {
				_back = form;
				_cb(this, _args);
			} else {
				_error = PBT_service_fail;
				_cb(this, _args);
			}
			return true;
		}

		time_t due() const {
			return _due;
		}

	private:
		TArgs _args{};
		time_t _due{};
		int _form_id{};
		int _error{};
		callback _cb{};
		ref_ptr<service_form> _back{};
	};
}
