#pragma once
#include <libcute.h>
#include "co_job.h"
#include "pb_base.h"
#include "ref_ptr.h"
#include "route_t.h"
#include "stop_watcher.h"
#include "system_error.h"
#include "tcp_peer.h"

namespace sys {
	struct cluster_event;
}

namespace web {
	class tcp_request final : public co_job {
	public:
		DELETE_MOVE_COPY(tcp_request)
		explicit tcp_request();
		~tcp_request() override;

		template <typename T>
		T request(const route_t& to, pb_base* msg) {
			if (ERR_OK != send(to, msg)) {
				return T{};
			}
			if (!_response) {
				return T{};
			}
			return _response->obj<T>();
		}

		uint32_t timeout_ms() const;
		tcp_request& timeout_ms(uint32_t);

	private:
		int send(const route_t&, const pb_base*);
		void on_route_call(const sys::cluster_event*);
		void kick(const sys::cluster_event*);
		uint32_t _timeout_ms;
		stop_watcher _timewatch;
		ref_ptr<service_form> _response;
		service_form _request{};
	};
}
