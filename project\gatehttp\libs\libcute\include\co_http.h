#pragma once
#include <string>
#include "http_message.h"
#include "libcute.h"
#include "libcutex.h"
#include "pb.h"

namespace net {
	class co_http final {
	public:
		DELETE_MOVE_COPY(co_http)
		explicit co_http(std::string url, int retry = 3);
		~co_http();
		void load(const dynamic_vo& msg, pb_buffer*);
		void load(const char*, size_t, pb_buffer*);
		void visit(pb_buffer*);
		dynamic_vo load(const dynamic_vo& msg);
		int send(const dynamic_vo& msg);
		http_content_type content_type{};
	private:
		void call(const char*, size_t, pb_buffer*);
		std::string _url;
		int _error;
		int _retry;
	};
}
