#pragma once
#include <libcute.h>
#include <type_data.h>
#include "dlink_make.h"
#include "typedata_wrapper_libgame.h"
#include "thing_group.h"
#include "property_group.h"
#include "render_interface.h"

namespace tvo {
	class type_body_position;
	class type_buff;

	class type_sep_body final : public type_data,
	                            public ds::render_body {
	public:
		DELETE_MOVE_COPY(type_sep_body)
		type_sep_body();
		static void awake_all(const awake_context_t&);
		~type_sep_body() override;
		type_buff* buff() const;
		UPLEVEL_LINK_FIELDS(type_sep_body)
		bool single() const override;
		bool ignore(const type_body_position*) const override;
		std::string art_key() const override;
		int default_set() const override;
		int class_id() const override;
		const char* class_n() const override;
		void parse(dynamic_vo&&) override;
		ds::render_display get_display() const override;
		//TYPE_SEP_BODY_WRAPPER()
	};
}
