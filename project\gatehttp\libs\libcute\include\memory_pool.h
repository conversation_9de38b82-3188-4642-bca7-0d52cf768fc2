#ifndef SYS_MEMORY_POOL_H
#define SYS_MEMORY_POOL_H 
#include <cstddef>
#include <mutex>
#include "thread_locker.h"

namespace mem {
	template <typename T, typename Locker = sys::thread_lock, size_t BlockSize = 4096>
	class memory_pool {
	public :
		typedef T value_type;
		typedef T* pointer;
		typedef T& reference;
		typedef const T* const_pointer;
		typedef const T& const_reference;
		typedef void (*mem_cb)(pointer);
		using locker_type = Locker;
		using thread_guard = sys::thread_guard<locker_type>;
		memory_pool() noexcept = default;

		memory_pool(const mem_cb c, const mem_cb d) noexcept: _ctor_cb(c), _dtor_cb(d) {
		}

		memory_pool(const memory_pool&) = delete;
		memory_pool& operator=(const memory_pool&) = delete;

		memory_pool(memory_pool&& rhs) noexcept {
			swap(std::forward<memory_pool>(rhs));
		}

		memory_pool& operator=(memory_pool&& rhs) noexcept {
			if (this != &rhs) {
				swap(std::forward<memory_pool>(rhs));
			}
			return *this;
		}

		~memory_pool() noexcept {
			mem_slot* curr = _current_block;
			while (curr != nullptr) {
				mem_slot* prev = curr->next;
				operator delete(reinterpret_cast<void*>(curr));
				curr = prev;
			}
		}

		static pointer address(reference x) noexcept { return &x; }

		static const_pointer address(const_reference x) noexcept {
			return &x;
		}

		static size_t max_size() noexcept {
			const size_t max = -1 / BlockSize;
			return (BlockSize - sizeof(char*)) / sizeof(mem_slot) * max;
		}

		pointer allocate() {
			thread_guard lock(_mutex);
			return private_allocate();
		}

		void deallocate(pointer p) {
			if (!p)return;
			thread_guard lock(_mutex);
			private_deallocate(p);
		}

		template <class... Args>
		pointer obtain(Args&&... args) {
			thread_guard lock(_mutex);
			pointer p = private_allocate();
			new(p)T(std::forward<Args>(args)...);
			return p;
		}

		void release(pointer p) {
			if (!p)return;
			thread_guard lock(_mutex);
			p->~value_type();
			private_deallocate(p);
		}

	private:
		void swap(memory_pool&& rhs) noexcept {
			thread_guard a(_mutex);
			thread_guard b(rhs._mutex);
			std::swap(_current_block, rhs._current_block);
			std::swap(_current_slot, rhs._current_slot);
			std::swap(_last_slot, rhs._last_slot);
			std::swap(_free_slot, rhs._free_slot);
			std::swap(_ctor_cb, rhs._ctor_cb);
			std::swap(_dtor_cb, rhs._dtor_cb);
		}

		pointer private_allocate() {
			if (_free_slot != nullptr) {
				auto p = reinterpret_cast<pointer>(_free_slot);
				_free_slot = _free_slot->next;
				if (_ctor_cb)_ctor_cb(p);
				return p;
			}
			if (_current_slot >= _last_slot) {
				allocate_block();
			}
			const auto p = reinterpret_cast<pointer>(_current_slot++);
			if (_ctor_cb)_ctor_cb(p);
			return p;
		}

		void private_deallocate(pointer p) {
			if (_dtor_cb) {
				_dtor_cb(p);
			}
			auto* a = reinterpret_cast<mem_slot*>(p);
			a->next = _free_slot;
			_free_slot = a;
		}

		union mem_slot {
			value_type element;
			mem_slot* next;
		};

		mem_slot* _current_block{};
		mem_slot* _current_slot{};
		mem_slot* _last_slot{};
		mem_slot* _free_slot{};
		mem_cb _ctor_cb{};
		mem_cb _dtor_cb{};
		locker_type _mutex;

		static size_t pad_pointer(char* p, const size_t align) noexcept {
			const auto result = reinterpret_cast<uintptr_t>(p);
			return (align - result) % align;
		}

		void allocate_block() {
			auto new_block = static_cast<char*>(operator new(BlockSize));
			reinterpret_cast<mem_slot*>(new_block)->next = _current_block;
			_current_block = reinterpret_cast<mem_slot*>(new_block);
			// Pad block body to satify the alignment requirements for elements
			char* body = new_block + sizeof(void*);
			const size_t padding = pad_pointer(body, alignof(mem_slot));
			_current_slot = reinterpret_cast<mem_slot*>(body + padding);
			_last_slot = reinterpret_cast<mem_slot*>(new_block + BlockSize - sizeof(mem_slot) + 1);
		}

		static_assert(BlockSize >= 4 * sizeof(mem_slot), "BlockSize too small.");
	};
}
#endif
