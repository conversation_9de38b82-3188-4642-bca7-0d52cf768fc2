#pragma once
#include <string>
#include "http_message.h"
#include "http_peer.h"

namespace net {
	class http_server;

	class http_session final : public http_peer {
	public:
		DELETE_MOVE_COPY(http_session)
		friend class http_worker;
		http_session(http_server*, uv_loop_t*, size_t id);
		std::string url() const;
		~http_session() override;
	protected:
		void on_parse_(http_phase) override;
	private:
		int _token;
		int _version;
		http_server* _server;
	};
}
