#pragma once
#include <http_parser.h>
#include "http_cookie.h"
#include "http_message.h"
#include "pb_stream.h"
#include "str_tools.h"

namespace net {
	using http_headers = std::map<std::string, std::string, sys::string_case_less>;
	using http_body = std::string;

	struct http_messagex {
		void add_cookie(const http_cookie&);
		using cookie_name = std::string;
		const http_cookie& get_cookie(const cookie_name&) const;
		int head(const std::string&, std::string&);
		int type{HTTP_BOTH};
		uint16_t http_major{1};
		uint32_t http_minor{1};
		std::vector<http_cookie> cookies{};
		http_headers headers{};
		http_body body{};
		/*struct content*/
		pb_stream content{};
		size_t content_length{};
		http_content_type content_type{};
	};

	struct http_repsonsex : http_messagex {
		http_status status_code{};
		std::string dump();
	};
}
