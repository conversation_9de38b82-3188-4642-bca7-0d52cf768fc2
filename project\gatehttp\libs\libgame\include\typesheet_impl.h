#pragma once
#include <typesheet.h>

namespace tvo {
	template <typename T>
	struct typesheet_impl {
		static_assert(std::is_base_of_v<type_data, T> && !std::is_abstract_v<T> && "is not top class of data");

		struct iterator_cast {
			using iterator = typesheet::list_iter;
			using pair_t = std::pair<id_64, T*>;

			explicit iterator_cast(const iterator& t): _it(t) {
			}

			iterator_cast(const iterator_cast& o): _it(o._it) {
			}

			iterator_cast(iterator_cast&& o) noexcept: _it(std::move(o._it)) {
			}

			~iterator_cast() = default;

			iterator_cast& operator ++() {
				++_it;
				return *this;
			}

			iterator_cast& operator ++(int) {
				auto temp = *this;
				++(*this);
				return temp;
			}

			bool operator ==(const iterator_cast& o) {
				return _it == o._it;
			}

			bool operator !=(const iterator_cast& o) {
				return _it != o._it;
			}

			iterator_cast& operator =(const iterator_cast& o) noexcept {
				if (this != &o) {
					_it = o._it;
				}
				return *this;
			}

			iterator_cast& operator =(iterator_cast&& o) noexcept {
				if (this != &o) {
					std::swap(_it, o._it);
				}
				return *this;
			}

			operator bool() const {
				return *_it;
			}

			T* operator *() const {
				if (!*_it) {
					throw_e("out of range");
				}
				auto* p = (*_it);
				auto* m = sys::type_cast<T>(p);
				return m;
			}

		private:
			iterator _it;
		};

		DEFAULT_MOVE_COPY(typesheet_impl)
		template <typename P, typename... Args>
		using is_predicator = typesheet::is_predicator<T, P, Args...>;

		explicit typesheet_impl(const typesheet* x): _sheet(x) {
		}

		~typesheet_impl() = default;

		iterator_cast begin() const {
			if (!_sheet) { throw_e("sheet is nil"); }
			return iterator_cast(_sheet->begin());
		}

		iterator_cast end() const {
			if (!_sheet) { throw_e("sheet is nil"); }
			return iterator_cast(_sheet->end());
		}

		T* select_one(const int id) const {
			if (!_sheet) { throw_e("sheet is nil"); }
			return _sheet->select_one<T>(id);
		}

		template <typename Predicator, typename... Args,
		          is_predicator<Predicator, Args...>* = nullptr>
		T* select_one(Predicator&& fn, Args&&... args) const {
			if (!_sheet) { throw_e("sheet is nil"); }
			return _sheet->select_one<T, Predicator, Args...>(std::forward<Predicator>(fn),
			                                                  std::forward<Args>(args)...);
		}

		template <typename Predicator, typename... Args,
		          is_predicator<Predicator, Args...>* = nullptr>
		void select_list(std::vector<T*>& to, Predicator&& fn, Args&&... args) const {
			if (!_sheet) { throw_e("sheet is nil"); }
			_sheet->select_list<T, Predicator, Args...>(to, std::forward<Predicator>(fn),
			                                            std::forward<Args>(args)...);
		}

		operator bool() const {
			return _sheet;
		}

	private:
		const typesheet* _sheet;
	};
}
