#pragma once
#include <fp_math.hpp>

namespace bt {
	enum class value_compare_type {
		Less,
		<PERSON><PERSON><PERSON><PERSON>,
		Equal,
		<PERSON><PERSON><PERSON><PERSON>,
		<PERSON>,
		NotEqual
	};

	template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
	bool value_compare(const T a, const T b, const value_compare_type t) {
		switch (t) {
			case value_compare_type::Less: return a < b;
			case value_compare_type::LessEqual: return a <= b;
			case value_compare_type::Equal: return a == b;
			case value_compare_type::GreatEqual: return a >= b;
			case value_compare_type::Great: return a > b;
			case value_compare_type::NotEqual: return a != b;
		}
		return false;
	}

	bool value_compare(float a, float b, value_compare_type);
	bool value_compare(double a, double b, value_compare_type);
#if FP_FIX_MATH_FLOAT
	bool value_compare(nav::float_t a, nav::float_t b, value_compare_type);
#endif
}
