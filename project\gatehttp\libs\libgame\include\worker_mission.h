#pragma once
#include "info_base.h"
#include "mission_manager.h"
#include "scene_interface.h"
#include "usr_worker.h"

namespace tvo {
	class type_mission;
}

namespace dvo {
	class player_vo;
	class data_mission;
}

namespace mission {
	struct mission_message;
	struct mission_message;
	class mission_runner;

	class worker_mission final : public dvo::usr_worker, implements imission_family {
	public :
		DELETE_MOVE_COPY(worker_mission)
		TYPE_CAST_IMPLEMENT(worker_mission)
		worker_mission();
		~worker_mission() override;
		void start() override;
		void stop() override;
		void update(const dvo::update_context*) override;
		void on_notifiction(const dvo::worker_notification*) override;
		mission_manager& manager() override;

		template <typename T, mission_manager::message_board_t::support_type<T>* = nullptr>
		void batch_invoke(const T& msg) {
			if (!_start) {
				return;
			}
			std::vector<mission_manager*> list{};
			managers(list);
			for (auto* m : list) {
				m->invoke(msg);
			}
		}

		void managers(std::vector<mission_manager*>&);
		void batch_invoke(int);
	private:
		void on_load(); 
		bool _start{}; 
		int _scene_mode{};
		mission_manager _manager;
	public:
		bool is_single() const override;
		bool is_leader(id_64) const override;
		bool contains(id_64) const override;
		void members(std::vector<id_64>&) const override;
		void members(std::vector<dvo::usr_info*>&) const override;
		ds::float_t nextd() override;
		cpx::iscene_base* scene() const override;
		dvo::info_base& domain() override;
		dvo::player_vo* leader() const override;
		mission_family family() const override;
		id_64 family_id() const override;
		mission_flag flag(const dvo::data_mission&) const override;
		bool runnable(const dvo::data_mission&) const override;
		nav::randseed& seed() override;
	};
}
