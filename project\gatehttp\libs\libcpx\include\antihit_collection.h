#pragma once
#include  <libcute.h>
#include <fp_math.hpp>

namespace cpx {
	class antihit_collection {
	public:
		DELETE_MOVE_COPY(antihit_collection)
		antihit_collection();
		~antihit_collection();
		void add(int id, nav::float_t val, nav::double_t due);
		nav::float_t get(nav::double_t now);
		nav::float_t temp(nav::double_t now);
		void set_base(nav::float_t);
		void remove(int id);
	private:
		struct anithit_vo {
			int aid;
			nav::float_t value;
			nav::double_t due;
		};

		void purge(nav::double_t now);
		nav::float_t _base;
		std::vector<anithit_vo> _list;
	};
}
