#pragma once
#include "composition_return_mode.h"
#include "cpx_action.h"

namespace cpx {
	class cpx_composition : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_composition)
		~cpx_composition() override;
		int maxLoops;
		bt::composition_return_mode returnMode;
	protected:
		cpx_composition();
		void on_start(cpx_input*, cpx_output*) const final;
		virtual bt::bt_context* context(cpx_input*) const = 0;
		bt::btree_status on_update(cpx_input*, cpx_output*) const final;
		virtual void cpx_start(cpx_input*, cpx_output*) const = 0;
		virtual bt::btree_status cpx_update(cpx_input*, cpx_output*) const = 0;
	};
}
