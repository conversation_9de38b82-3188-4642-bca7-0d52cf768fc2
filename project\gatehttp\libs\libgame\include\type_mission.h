#pragma once
#include <libcute.h>
#include <stack>
#include <type_data.h>
#include "mission_enums.h"
#include "typedata_wrapper_libgame.h"
#include <context_cluster.h>

namespace mission {
	class mission_initor;
	class mission_atree;
	class mission_transition;
	class context_t;
	class workdata_t;
}

namespace tvo {
	class type_mission final : public type_data {
	public:
		friend class mission::mission_initor;
		DELETE_MOVE_COPY(type_mission)
		type_mission();
		~type_mission() override;
		static void awake_all(const awake_context_t&);
		mission::mission_type type{};
		bool auto_active{};
		bool head{};
		bool visible{};
		int act_hash{};
		int trans_hash{};
		size_t cnt_actions{};
		const std::vector<mission::mission_atree*>& alist() const;
		const std::vector<mission::mission_transition*>& tlist() const;
		bool can_save() const;
		bt::context_cluster_pool& pool();
		static const std::vector<const type_mission*>& auto_missions();

	private:
		std::vector<mission::mission_atree*> _alist{};
		std::vector<mission::mission_transition*> _tlist{};
		bt::context_cluster_pool _cpool{};

	public:
		TYPE_MISSION_WRAPPER()
	};
}
