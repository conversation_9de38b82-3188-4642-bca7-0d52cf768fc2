#pragma once
#include <atomic>
#include <libcute.h>
#include <array>
namespace sys {
	template <typename T>
	class double_copy {
	public:
		DELETE_MOVE_COPY(double_copy)
		double_copy() = default;
		~double_copy() = default;

		T& working(const std::memory_order p = std::memory_order_seq_cst) {
			return _buffers.at(_wc.load(p));
		}

		const T& working(const std::memory_order p = std::memory_order_seq_cst) const {
			return _buffers.at(_wc.load(p));
		}

		T& backup(const std::memory_order p = std::memory_order_seq_cst) {
			return _buffers.at(1 ^ _wc.load(p));
		}

		const T& backup(const std::memory_order p = std::memory_order_seq_cst) const {
			return _buffers.at(1 ^ _wc.load(p));
		}

		void rotate(const std::memory_order p = std::memory_order_seq_cst) {
			_wc.fetch_xor(1, p);
		}

	private:
		std::array<T, 2> _buffers{};
		std::atomic<int> _wc{};
	};
}
