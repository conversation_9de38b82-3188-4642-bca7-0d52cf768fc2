#pragma once
#include <string>

namespace cpx {
#define ANIMATION_TYPE(XX)\
XX(is_running 	, "isRunning"	) 	\
XX(is_vertigo 	, "isVertigo"	) 	\
XX(is_dead  	, "isDead"		) 	\
XX(has_horse 	, "hasHorse"	) 	\
XX(mount_mode 	, "mountMode"	) 	\
XX(weapon_mode 	, "weaponMode"	) 	\
XX(block 		, "block"		) 	\
XX(speed_mode 	, "speedMode"	) 	\
XX(random_mode 	, "randomMode"	) 	\
XX(set_mode 	, "setMode"		) 	\
XX(away_mode 	, "awayMode"	) 	\
XX(phase_mode 	, "phaseMode"	) 	\
XX(dir_mode 	, "dirMode"		) 	\
XX(attack_mode 	, "attackMode"	)	\
XX(anim_speed 	, "animSpeed"	) 	\
XX(ground_mode 	, "groundMode"	) 	\

#define ANIMATION_TRIGGER(XX)\
XX(trigger_hit 				, "Hit"				) 	\
XX(trigger_jump 			, "Jump"			) 	\
XX(trigger_punch 			, "Punch"			) 	\
XX(trigger_backfree 		, "Backfree"		) 	\
XX(trigger_shield_broken 	, "ShieldBroken"	) 	\
XX(trigger_hitback 			, "Hitback" 		)

	struct anim_type {
#define XX(name, string)static const int name;
		ANIMATION_TYPE(XX)
		ANIMATION_TRIGGER(XX)
#undef XX
		static int hash(const std::string&);
		static int is_mendatory(int);
	};
}
