#pragma once
#include <libcute.h>

namespace cpx {
	struct tick_context;
	class rpx_controller;
	class rpx_machine;
	class cpx_entity;

	class rpx_core {
	public:
		DELETE_MOVE_COPY(rpx_core)
		int instance_id{};
		cpx_entity* entity{};
		id_64 entity_id{};
		rpx_machine* machine{};
		rpx_controller* controller{};
		int64_t machine_id() const;
		virtual void enter() = 0;
		virtual void update(const tick_context*) = 0;
		virtual void exit();
		rpx_core* setup(cpx_entity* e, rpx_machine* m, rpx_controller* c);
	protected:
		rpx_core();
		virtual ~rpx_core();
	};
}
