#pragma once
#include <libcute.h>

namespace mem {
	template <size_t BlockSize = 4096>
	class buffer_pool {
	public:
		DELETE_MOVE_COPY(buffer_pool)
		buffer_pool() = default;

		~buffer_pool() {
			auto* v = _free;
			while (v) {
				auto* n = v->next;
				delete v;
				v = n;
			}
		}

		char* obtain() {
			if (_free) {
				auto* n = _free;
				_free = n->next;
				n->next = nullptr;
				return n->buffer;
			}
			mem_slot* mem = new mem_slot();
			return mem->buffer;
		}

		void release(char* mem) {
			mem_slot* slot = reinterpret_cast<mem_slot*>(mem);
			slot->next = _free;
			_free = slot;
		}

	private:
		struct mem_slot {
			char buffer[BlockSize]{};
			mem_slot* next{};
		};

		mem_slot* _free{};
	};
}
