#pragma once
#include "libcute.h"
#include "sys_exception.h"
#include <typeinfo>

namespace sys {
	class any {
		class iplaceholder {
		public :
			DELETE_MOVE_COPY(iplaceholder)
			virtual const std::type_info& type() const = 0;
			virtual iplaceholder* clone() const = 0;
			virtual ~iplaceholder();
		protected:
			iplaceholder() = default;
		};

		template <typename T>
		class holder_t final : public iplaceholder {
		public:
			explicit holder_t(const T& val): iplaceholder(), _value(val) {
			}

			const std::type_info& type() const override {
				return typeid(T);
			}

			iplaceholder* clone() const override {
				return new holder_t(_value);
			}

			T& value() {
				return _value;
			}

			friend class any;
		private:
			T _value;
		};

	public:
		any();

		template <typename T>
		explicit any(const T& val): _holder(new holder_t<T>(val)) {
		}

		template <typename T>
		explicit operator T&() {
			const bool ok = type() == typeid(T);
			if (!ok) {
				throw_e("T (%s) is not any<%s>", typeid(T).name(), type().name());
			}
			return static_cast<holder_t<T>*>(_holder)->_value;
		}

		template <typename T>
		T& cast() const {
			return static_cast<holder_t<T>*>(_holder)->_value;
		}

		template <typename T>
		bool is() const {
			return typeid(T) == type();
		}

		bool is(const any& rhs) const;
		any(const any& other);
		any(any&& other) noexcept;
		any& operator =(const any& other);
		any& operator =(any&& other) noexcept;
		const std::type_info& type() const;
		void swap(any& other) noexcept;
		~any();
	private:
		iplaceholder* _holder;
		template <typename T>
		friend T any_cast(const any& operand);
	};

	template <typename T>
	T any_cast(const any& operand) {
		if (typeid(T) != operand.type()) {
			throw_e("T (%s) is not any<%s>", typeid(T).name(), operand.type().name());
		}
		auto* h = dynamic_cast<any::holder_t<T>*>(operand._holder);
		return h ? h->value() : T();
	}

	void swap(any& lhs, any& rhs) noexcept;
}
