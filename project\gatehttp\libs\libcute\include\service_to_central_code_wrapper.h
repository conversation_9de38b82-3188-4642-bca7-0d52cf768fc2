/*auto gen, don't modify it*/
#pragma once 
#include "pb_types.h"
#include <vector>
#include <pb_manager.h>
#include <ecs_domain.h>
#include <ecs_class.h>
#include <ecs_object.h>
#include <ecs_value.h>
#include <pb_base.h>
#include <room_id.h>
#include <logger.h>
class c2s_service_info; 
class error_message; 
class s2c_error_log; 
class s2c_service_info; 
class s2c_service_shakehand; 
class s2c_service_stat; 
class service_fail; 
class service_ping; 
class service_pong; 
class service_tick; 
/*---------------c2s_service_info--------------------*/
class c2s_service_info final :public pb_base{
public:
DEFAULT_MOVE_COPY(c2s_service_info)
c2s_service_info():pb_base(){} 

~c2s_service_info() override =default ; 

enum property_key{
kId = 1, 
kStype = 2, 
kAwakable = 3, 
kStatus = 4, 
kReboot = 8, 
kDump = 6, 
kWorkload = 7, 

};

pb::int64_t id{}; 
pb::int32_t stype{}; 
pb::int32_t awakable{}; 
pb::int32_t status{}; 
pb::int32_t reboot{}; 
pb::int32_t dump{}; 
pb::int64_t workload{}; 

int type()const override {return -688534989;}static constexpr int assign_type () {return -688534989;}

int encode(pb_buffer* to) const override {if(id != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(3)));pb_addvarint64(to, id);}
if(stype != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(stype));}
if(awakable != 0) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(awakable));}
if(status != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(status));}
if(reboot != 0) {pb_addvarint32(to, pb_pair(8, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(reboot));}
if(dump != 0) {pb_addvarint32(to, pb_pair(6, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(dump));}
if(workload != 0) {pb_addvarint32(to, pb_pair(7, pb_wtypebytype(3)));pb_addvarint64(to, workload);}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}id = o__.u64; break;}
case kStype :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}stype = (static_cast<int>(o__.u64)); break;}
case kAwakable :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}awakable = (static_cast<int>(o__.u64)); break;}
case kStatus :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}status = (static_cast<int>(o__.u64)); break;}
case kReboot :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}reboot = (static_cast<int>(o__.u64)); break;}
case kDump :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}dump = (static_cast<int>(o__.u64)); break;}
case kWorkload :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}workload = o__.u64; break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

c2s_service_info (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kId)->get_to(id);
ecs__.at(kStype)->get_to(stype);
ecs__.at(kAwakable)->get_to(awakable);
ecs__.at(kStatus)->get_to(status);
ecs__.at(kReboot)->get_to(reboot);
ecs__.at(kDump)->get_to(dump);
ecs__.at(kWorkload)->get_to(workload);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(-688534989)->new_instance();ecs__.at(kId)->set_to(id);
ecs__.at(kStype)->set_to(stype);
ecs__.at(kAwakable)->set_to(awakable);
ecs__.at(kStatus)->set_to(status);
ecs__.at(kReboot)->set_to(reboot);
ecs__.at(kDump)->set_to(dump);
ecs__.at(kWorkload)->set_to(workload);
return ecs__ ;}

};
#define pb_properties_c2s_service_info id,stype,awakable,status,reboot,dump,workload

/*---------------error_message--------------------*/
class error_message final :public pb_base{
public:
DEFAULT_MOVE_COPY(error_message)
error_message():pb_base(){} 

~error_message() override =default ; 

enum property_key{
kErrTime = 1, 
kErrType = 2, 
kErrLevel = 3, 
kErrMsg = 4, 
kErrStack = 5, 

};

pb::int64_t err_time{}; 
pb::int32_t err_type{}; 
pb::int32_t err_level{}; 
pb::string_t err_msg{}; 
pb::string_t err_stack{}; 

int type()const override {return -1938755376;}static constexpr int assign_type () {return -1938755376;}

int encode(pb_buffer* to) const override {if(err_time != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(3)));pb_addvarint64(to, err_time);}
if(err_type != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(err_type));}
if(err_level != 0) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(err_level));}
if(!err_msg.empty()) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(err_msg.c_str(), err_msg.size());pb_addbytes(to, s__);}
if(!err_stack.empty()) {pb_addvarint32(to, pb_pair(5, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(err_stack.c_str(), err_stack.size());pb_addbytes(to, s__);}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kErrTime :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}err_time = o__.u64; break;}
case kErrType :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}err_type = (static_cast<int>(o__.u64)); break;}
case kErrLevel :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}err_level = (static_cast<int>(o__.u64)); break;}
case kErrMsg :{pbd_out o__;pbd_readbytes(s, o__.s);err_msg = o__.s->base; break;}
case kErrStack :{pbd_out o__;pbd_readbytes(s, o__.s);err_stack = o__.s->base; break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

error_message (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kErrTime)->get_to(err_time);
ecs__.at(kErrType)->get_to(err_type);
ecs__.at(kErrLevel)->get_to(err_level);
ecs__.at(kErrMsg)->get_to(err_msg);
ecs__.at(kErrStack)->get_to(err_stack);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(-1938755376)->new_instance();ecs__.at(kErrTime)->set_to(err_time);
ecs__.at(kErrType)->set_to(err_type);
ecs__.at(kErrLevel)->set_to(err_level);
ecs__.at(kErrMsg)->set_to(err_msg);
ecs__.at(kErrStack)->set_to(err_stack);
return ecs__ ;}

};
#define pb_properties_error_message err_time,err_type,err_level,err_msg,err_stack

/*---------------s2c_error_log--------------------*/
class s2c_error_log final :public pb_base{
public:
DEFAULT_MOVE_COPY(s2c_error_log)
s2c_error_log():pb_base(){} 

~s2c_error_log() override =default ; 

enum property_key{
kRole = 1, 
kRoleType = 2, 
kMachine = 3, 
kDevice = 4, 
kIp = 5, 
kMessages = 6, 

};

pb::string_t role{}; 
pb::int32_t role_type{}; 
pb::string_t machine{}; 
pb::string_t device{}; 
pb::string_t ip{}; 
std::vector<error_message> messages{}; 

int type()const override {return -186010574;}static constexpr int assign_type () {return -186010574;}

int encode(pb_buffer* to) const override {if(!role.empty()) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(role.c_str(), role.size());pb_addbytes(to, s__);}
if(role_type != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(role_type));}
if(!machine.empty()) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(machine.c_str(), machine.size());pb_addbytes(to, s__);}
if(!device.empty()) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(device.c_str(), device.size());pb_addbytes(to, s__);}
if(!ip.empty()) {pb_addvarint32(to, pb_pair(5, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(ip.c_str(), ip.size());pb_addbytes(to, s__);}
for(const auto & v__ : messages){pb_addvarint32(to, pb_pair(6, pb_wtypebytype(11)));{const size_t len__ = pb_bufflen(to);v__.encode(to);pbe_addlength(to, len__);}}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kRole :{pbd_out o__;pbd_readbytes(s, o__.s);role = o__.s->base; break;}
case kRoleType :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}role_type = (static_cast<int>(o__.u64)); break;}
case kMachine :{pbd_out o__;pbd_readbytes(s, o__.s);machine = o__.s->base; break;}
case kDevice :{pbd_out o__;pbd_readbytes(s, o__.s);device = o__.s->base; break;}
case kIp :{pbd_out o__;pbd_readbytes(s, o__.s);ip = o__.s->base; break;}
case kMessages :{error_message v__;pbd_slice __sv{};pbd_readbytes(s, &__sv);v__.decode(&__sv);messages.emplace_back(v__); break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

s2c_error_log (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kRole)->get_to(role);
ecs__.at(kRoleType)->get_to(role_type);
ecs__.at(kMachine)->get_to(machine);
ecs__.at(kDevice)->get_to(device);
ecs__.at(kIp)->get_to(ip);
ecs__.at(kMessages)->get_to(messages);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(-186010574)->new_instance();ecs__.at(kRole)->set_to(role);
ecs__.at(kRoleType)->set_to(role_type);
ecs__.at(kMachine)->set_to(machine);
ecs__.at(kDevice)->set_to(device);
ecs__.at(kIp)->set_to(ip);
ecs__.at(kMessages)->set_to(messages);
return ecs__ ;}

};
#define pb_properties_s2c_error_log role,role_type,machine,device,ip,messages

/*---------------s2c_service_info--------------------*/
class s2c_service_info final :public pb_base{
public:
DEFAULT_MOVE_COPY(s2c_service_info)
s2c_service_info():pb_base(){} 

~s2c_service_info() override =default ; 

enum property_key{
kInfo = 1, 
kOpcode = 2, 

};

c2s_service_info info{}; 
pb::int32_t opcode{}; 

int type()const override {return -768143821;}static constexpr int assign_type () {return -768143821;}

int encode(pb_buffer* to) const override {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(11)));{const size_t len__ = pb_bufflen(to);info.encode(to);pbe_addlength(to, len__);}
if(opcode != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(opcode));}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kInfo :{pbd_slice __sv{};pbd_readbytes(s, &__sv);info.decode(&__sv); break;}
case kOpcode :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}opcode = (static_cast<int>(o__.u64)); break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

s2c_service_info (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kInfo)->get_to(info);
ecs__.at(kOpcode)->get_to(opcode);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(-768143821)->new_instance();ecs__.at(kInfo)->set_to(info);
ecs__.at(kOpcode)->set_to(opcode);
return ecs__ ;}

};
#define pb_properties_s2c_service_info info,opcode

/*---------------s2c_service_shakehand--------------------*/
class s2c_service_shakehand final :public pb_base{
public:
DEFAULT_MOVE_COPY(s2c_service_shakehand)
s2c_service_shakehand():pb_base(){} 

~s2c_service_shakehand() override =default ; 

enum property_key{
kSrcId = 1, 
kSrcType = 2, 
kDstId = 3, 
kDstType = 4, 
kPhase = 5, 

};

pb::int64_t src_id{}; 
pb::int32_t src_type{}; 
pb::int64_t dst_id{}; 
pb::int32_t dst_type{}; 
pb::int32_t phase{}; 

int type()const override {return -2065981936;}static constexpr int assign_type () {return -2065981936;}

int encode(pb_buffer* to) const override {if(src_id != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(3)));pb_addvarint64(to, src_id);}
if(src_type != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(src_type));}
if(dst_id != 0) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(3)));pb_addvarint64(to, dst_id);}
if(dst_type != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(dst_type));}
if(phase != 0) {pb_addvarint32(to, pb_pair(5, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(phase));}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kSrcId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}src_id = o__.u64; break;}
case kSrcType :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}src_type = (static_cast<int>(o__.u64)); break;}
case kDstId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}dst_id = o__.u64; break;}
case kDstType :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}dst_type = (static_cast<int>(o__.u64)); break;}
case kPhase :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}phase = (static_cast<int>(o__.u64)); break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

s2c_service_shakehand (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kSrcId)->get_to(src_id);
ecs__.at(kSrcType)->get_to(src_type);
ecs__.at(kDstId)->get_to(dst_id);
ecs__.at(kDstType)->get_to(dst_type);
ecs__.at(kPhase)->get_to(phase);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(-2065981936)->new_instance();ecs__.at(kSrcId)->set_to(src_id);
ecs__.at(kSrcType)->set_to(src_type);
ecs__.at(kDstId)->set_to(dst_id);
ecs__.at(kDstType)->set_to(dst_type);
ecs__.at(kPhase)->set_to(phase);
return ecs__ ;}

};
#define pb_properties_s2c_service_shakehand src_id,src_type,dst_id,dst_type,phase

/*---------------s2c_service_stat--------------------*/
class s2c_service_stat final :public pb_base{
public:
DEFAULT_MOVE_COPY(s2c_service_stat)
s2c_service_stat():pb_base(){} 

~s2c_service_stat() override =default ; 

enum property_key{
kId = 1, 
kStype = 2, 
kExe = 3, 
kTime = 4, 
kPid = 5, 
kIp = 6, 
kMem = 7, 
kCpu = 8, 
kExt = 9, 
kNet = 10, 

};

pb::int32_t id{}; 
pb::int32_t stype{}; 
pb::string_t exe{}; 
pb::int64_t time{}; 
pb::int32_t pid{}; 
pb::string_t ip{}; 
pb::int32_t mem{}; 
pb::int32_t cpu{}; 
pb::string_t ext{}; 
pb::string_t net{}; 

int type()const override {return -767840295;}static constexpr int assign_type () {return -767840295;}

int encode(pb_buffer* to) const override {if(id != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(id));}
if(stype != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(stype));}
if(!exe.empty()) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(exe.c_str(), exe.size());pb_addbytes(to, s__);}
if(time != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(3)));pb_addvarint64(to, time);}
if(pid != 0) {pb_addvarint32(to, pb_pair(5, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(pid));}
if(!ip.empty()) {pb_addvarint32(to, pb_pair(6, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(ip.c_str(), ip.size());pb_addbytes(to, s__);}
if(mem != 0) {pb_addvarint32(to, pb_pair(7, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(mem));}
if(cpu != 0) {pb_addvarint32(to, pb_pair(8, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(cpu));}
if(!ext.empty()) {pb_addvarint32(to, pb_pair(9, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(ext.c_str(), ext.size());pb_addbytes(to, s__);}
if(!net.empty()) {pb_addvarint32(to, pb_pair(10, pb_wtypebytype(9)));const pb_slice s__ = pb_slice_buf(net.c_str(), net.size());pb_addbytes(to, s__);}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}id = (static_cast<int>(o__.u64)); break;}
case kStype :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}stype = (static_cast<int>(o__.u64)); break;}
case kExe :{pbd_out o__;pbd_readbytes(s, o__.s);exe = o__.s->base; break;}
case kTime :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}time = o__.u64; break;}
case kPid :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}pid = (static_cast<int>(o__.u64)); break;}
case kIp :{pbd_out o__;pbd_readbytes(s, o__.s);ip = o__.s->base; break;}
case kMem :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}mem = (static_cast<int>(o__.u64)); break;}
case kCpu :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}cpu = (static_cast<int>(o__.u64)); break;}
case kExt :{pbd_out o__;pbd_readbytes(s, o__.s);ext = o__.s->base; break;}
case kNet :{pbd_out o__;pbd_readbytes(s, o__.s);net = o__.s->base; break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

s2c_service_stat (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kId)->get_to(id);
ecs__.at(kStype)->get_to(stype);
ecs__.at(kExe)->get_to(exe);
ecs__.at(kTime)->get_to(time);
ecs__.at(kPid)->get_to(pid);
ecs__.at(kIp)->get_to(ip);
ecs__.at(kMem)->get_to(mem);
ecs__.at(kCpu)->get_to(cpu);
ecs__.at(kExt)->get_to(ext);
ecs__.at(kNet)->get_to(net);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(-767840295)->new_instance();ecs__.at(kId)->set_to(id);
ecs__.at(kStype)->set_to(stype);
ecs__.at(kExe)->set_to(exe);
ecs__.at(kTime)->set_to(time);
ecs__.at(kPid)->set_to(pid);
ecs__.at(kIp)->set_to(ip);
ecs__.at(kMem)->set_to(mem);
ecs__.at(kCpu)->set_to(cpu);
ecs__.at(kExt)->set_to(ext);
ecs__.at(kNet)->set_to(net);
return ecs__ ;}

};
#define pb_properties_s2c_service_stat id,stype,exe,time,pid,ip,mem,cpu,ext,net

/*---------------service_fail--------------------*/
class service_fail final :public pb_base{
public:
DEFAULT_MOVE_COPY(service_fail)
service_fail():pb_base(){} 

~service_fail() override =default ; 

enum property_key{
kFormId = 1, 
kClassId = 2, 
kData = 3, 

};

pb::int32_t form_id{}; 
pb::int32_t class_id{}; 
pb::bytes_t data{}; 

int type()const override {return 359641704;}static constexpr int assign_type () {return 359641704;}

int encode(pb_buffer* to) const override {if(form_id != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(form_id));}
if(class_id != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(class_id));}
if(!data.empty()) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(12)));const pb_slice s__ = pb_slice_buf(data.c_str(), data.size());pb_addbytes(to, s__);}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kFormId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}form_id = (static_cast<int>(o__.u64)); break;}
case kClassId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}class_id = (static_cast<int>(o__.u64)); break;}
case kData :{pbd_out o__;pbd_readbytes(s, o__.s);data = o__.s->base; break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

service_fail (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kFormId)->get_to(form_id);
ecs__.at(kClassId)->get_to(class_id);
ecs__.at(kData)->get_to(data);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(359641704)->new_instance();ecs__.at(kFormId)->set_to(form_id);
ecs__.at(kClassId)->set_to(class_id);
ecs__.at(kData)->set_to(data);
return ecs__ ;}

};
#define pb_properties_service_fail form_id,class_id,data

/*---------------service_ping--------------------*/
class service_ping final :public pb_base{
public:
DEFAULT_MOVE_COPY(service_ping)
service_ping():pb_base(){} 

~service_ping() override =default ; 

enum property_key{
kTime = 1, 

};

pb::int64_t time{}; 

int type()const override {return 359947452;}static constexpr int assign_type () {return 359947452;}

int encode(pb_buffer* to) const override {if(time != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(3)));pb_addvarint64(to, time);}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kTime :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}time = o__.u64; break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

service_ping (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kTime)->get_to(time);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(359947452)->new_instance();ecs__.at(kTime)->set_to(time);
return ecs__ ;}

};
#define pb_properties_service_ping time

/*---------------service_pong--------------------*/
class service_pong final :public pb_base{
public:
DEFAULT_MOVE_COPY(service_pong)
service_pong():pb_base(){} 

~service_pong() override =default ; 

enum property_key{
kTime = 2, 

};

pb::int64_t time{}; 

int type()const override {return 359953218;}static constexpr int assign_type () {return 359953218;}

int encode(pb_buffer* to) const override {if(time != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(3)));pb_addvarint64(to, time);}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kTime :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}time = o__.u64; break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

service_pong (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kTime)->get_to(time);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(359953218)->new_instance();ecs__.at(kTime)->set_to(time);
return ecs__ ;}

};
#define pb_properties_service_pong time

/*---------------service_tick--------------------*/
class service_tick final :public pb_base{
public:
DEFAULT_MOVE_COPY(service_tick)
service_tick():pb_base(){} 

~service_tick() override =default ; 

enum property_key{
kTime = 1, 

};

pb::int64_t time{}; 

int type()const override {return 360066279;}static constexpr int assign_type () {return 360066279;}

int encode(pb_buffer* to) const override {if(time != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(3)));pb_addvarint64(to, time);}
return 0 ;}

int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__ = pb_gettag(tag__);switch(fnumber__){
case kTime :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}time = o__.u64; break;}
default: {pb_skipvalue(&s->base, tag__); break;}}
}
return 0 ;}

service_tick (const ecs::object_t& ecs__){if(ecs__.ok()){ecs__.at(kTime)->get_to(time);
}}

operator ecs::object_t ()const {const auto ecs__ = pb_manager::current_domain()->find(360066279)->new_instance();ecs__.at(kTime)->set_to(time);
return ecs__ ;}

};
#define pb_properties_service_tick time

