#ifndef ENUM_MARCO_H
#define ENUM_MARCO_H

#define enum_operator_make(T , op)														\
inline T operator op(const T   a	, const T   b){return (T)((int)a	op (int)b); }	\
inline T operator op(const int a	, const T   b){return (T)(	   a	op (int)b); }	\
inline T operator op(const T   a	, const int b){return (T)((int)a	op      b); }


#define enum_operator_compare(T , op)	\
inline bool operator op(const T   a	, const int b){return (int)a op		 b; }	\
inline bool operator op(const int a	, const T   b){return	   a op (int)b; }	\


#define enum_op_flags(T)		\
enum_operator_make(T, <<)		\
enum_operator_make(T, >>)		\
enum_operator_make(T, &)		\
enum_operator_make(T, |)		\
enum_operator_compare(T, >)		\
enum_operator_compare(T, >=)	\
enum_operator_compare(T, <)		\
enum_operator_compare(T, <=)	\
enum_operator_compare(T, ==)	\
enum_operator_compare(T, !=)	\

#define enum_op_int(T)			\
enum_operator_compare(T, >)		\
enum_operator_compare(T, >=)	\
enum_operator_compare(T, <)		\
enum_operator_compare(T, <=)	\
enum_operator_compare(T, ==)	\
enum_operator_compare(T, !=)	\


#define enum_case_string(num, name, string)case num: return string ;

#define enum_to_string(T, LIST)		\
inline const char* T##_str(T v){	\
	switch((int)v){					\
		LIST(enum_case_string)		\
	default:						\
		return "unknown"#T;			\
}}


#define enum_body_xx(num, name, string)k##name = (num),

 /*define a single enum*/
#define enum_class(T,LIST)			\
enum class T{LIST(enum_body_xx)};	\
enum_to_string(T, LIST)				\
enum_op_int(T)

 /*define a maskable enum*/
#define enum_flags_define(T, LIST)	\
enum class T{LIST(enum_body_xx)	};	\
enum_to_string(T, LIST)				\
enum_op_flags(T)

 /*define a C type enum*/
#define enum_ctype(T, LIST) 	\
enum T{LIST(enum_body_xx)}; 	\
enum_to_string(T, LIST)


#endif
