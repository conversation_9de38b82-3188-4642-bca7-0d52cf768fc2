
#ifndef CJSON_H
#define CJSON_H
#ifdef __cplusplus
extern "C"
{
#endif
#if !defined(__WINDOWS__) && (defined(WIN32) || defined(WIN64) || defined(_MSC_VER) || defined(_WIN32))
#define __WINDOWS__
#endif
#ifdef __WINDOWS__
/* When compiling for windows, we specify a specific calling convention to avoid issues where we are being called from a project with a different default calling convention.  For windows you have 3 define options:

CJSON_HIDE_SYMBOLS - Define this in the case where you don't want to ever dllexport symbols
CJSON_EXPORT_SYMBOLS - Define this on library build when you want to dllexport symbols (default)
CJSON_IMPORT_SYMBOLS - Define this if you want to dllimport symbol

For *nix builds that support visibility attribute, you can define similar behavior by

setting default visibility to hidden by adding
-fvisibility=hidden (for gcc)
or
-xldscope=hidden (for sun cc)
to CFLAGS

then using the CJSON_API_VISIBILITY flag to "export" the same symbols the way CJSON_EXPORT_SYMBOLS does

*/
#define CJSON_CDECL __cdecl
#define CJSON_STDCALL __stdcall
/* export symbols by default, this is necessary for copy pasting the C and header file */
#if !defined(CJSON_HIDE_SYMBOLS) && !defined(CJSON_IMPORT_SYMBOLS) && !defined(CJSON_EXPORT_SYMBOLS)
#define CJSON_EXPORT_SYMBOLS
#endif
#if defined(CJSON_HIDE_SYMBOLS)
#define CJSON_PUBLIC(type)   type CJSON_STDCALL
#elif defined(CJSON_EXPORT_SYMBOLS)
#define CJSON_PUBLIC(type)   __declspec(dllexport) type CJSON_STDCALL
#elif defined(CJSON_IMPORT_SYMBOLS)
#define CJSON_PUBLIC(type)   __declspec(dllimport) type CJSON_STDCALL
#endif
#else /* !__WINDOWS__ */
#define CJSON_CDECL
#define CJSON_STDCALL

#if (defined(__GNUC__) || defined(__SUNPRO_CC) || defined (__SUNPRO_C)) && defined(CJSON_API_VISIBILITY)
#define CJSON_PUBLIC(type)   __attribute__((visibility("default"))) type
#else
#define CJSON_PUBLIC(type) type
#endif
#endif
/* project version */
#define CJSON_VERSION_MAJOR 1
#define CJSON_VERSION_MINOR 7
#define CJSON_VERSION_PATCH 15
#include <stddef.h>
/* cJSON Types: */
#define CJSON_INVALID (0)
#define CJSON_FALSE  (1 << 0)
#define CJSON_TRUE   (1 << 1)
#define CJSON_NULL   (1 << 2)
#define CJSON_NUMBER (1 << 3)
#define CJSON_STRING (1 << 4)
#define CJSON_ARRAY  (1 << 5)
#define CJSON_OBJECT (1 << 6)
#define CJSON_RAW    (1 << 7) /* raw json */
#define CJSON_IS_REFERENCE 256
#define CJSON_CONST_STRING 512

/* The cJSON structure: */
typedef struct jsonc_s {
	/* next/prev allow you to walk array/object chains. Alternatively */
	struct jsonc_s* next;
	struct jsonc_s* prev;
	/* An array or object item will have a child pointer pointing to a chain of the items in the array/object. */
	struct jsonc_s* child;
	/* The type of the item, as above. */
	int type;
	/* The item's string, if type==cJSON_String  and type == cJSON_Raw */
	char* valuestring;
	/* writing to valueint is DEPRECATED, use cJSON_SetNumberValue instead */
	int valueint;
	/* The item's number, if type==cJSON_Number */
	double valuedouble;
	/* The item's name string, if this item is the child of, or is in the list of subitems of an object. */
	char* string;
} jsonc;

typedef struct cjson_hooks {
	/* malloc/free are CDECL on Windows regardless of the default calling convention of the compiler, so ensure the hooks allow passing those functions directly. */
	void*(CJSON_CDECL *malloc_fn)(size_t sz);
	void (CJSON_CDECL *free_fn)(void* ptr);
} json_hooks;

typedef int cjson_bool;
/* Limits how deeply nested arrays/objects can be before cJSON rejects to parse them.
 * This is to prevent stack overflows. */
#ifndef CJSON_NESTING_LIMIT
#define CJSON_NESTING_LIMIT 1000
#endif
/* returns the version of cJSON as a string */
CJSON_PUBLIC(const char*) jsonc_version(void);
/* Supply malloc, realloc and free functions to cJSON */
CJSON_PUBLIC(void) jsonc_hook_init(json_hooks* hooks);
/* Memory Management: the caller is always responsible to free the results from all variants of cJSON_Parse (with cJSON_Delete) and cJSON_Print (with stdlib free, cJSON_Hooks.free_fn, or cJSON_free as appropriate). The exception is cJSON_PrintPreallocated, where the caller has full responsibility of the buffer. */
/* Supply a block of JSON, and this returns a cJSON object you can interrogate. */
CJSON_PUBLIC(jsonc *) jsonc_parse(const char* value);
CJSON_PUBLIC(jsonc *) jsonc_parse_with_length(const char* value, size_t buf_len);
/* ParseWithOpts allows you to require (and check) that the JSON is null terminated, and to retrieve the pointer to the final byte parsed. */
/* If you supply a ptr in return_parse_end and parsing fails, then return_parse_end will contain a pointer to the error so will match cJSON_GetErrorPtr(). */
CJSON_PUBLIC(jsonc *) jsonc_parse_with_opts(const char* value, const char** return_parse_end,
                                            cjson_bool require_null_terminated);
CJSON_PUBLIC(jsonc *) jsonc_parse_with_lengthopts(const char* value, size_t buffer_length,
                                                  const char** return_parse_end, cjson_bool require_null_terminated); 

CJSON_PUBLIC(jsonc *) jsonc_parse_file(const char * file); 

/* Render a cJSON entity to text for transfer/storage. */
CJSON_PUBLIC(char *) jsonc_output(const jsonc* item);
/* Render a cJSON entity to text for transfer/storage without any formatting. */
CJSON_PUBLIC(char *) jsonc_output_formated(const jsonc* item);
/* Render a cJSON entity to text using a buffered strategy. prebuffer is a guess at the final size. guessing well reduces reallocation. fmt=0 gives unformatted, =1 gives formatted */
CJSON_PUBLIC(char *) jsonc_output_buffered(const jsonc* item, int prebuffer, cjson_bool fmt);
/* Render a cJSON entity to text using a buffer already allocated in memory with given length. Returns 1 on success and 0 on failure. */
/* NOTE: cJSON is not always 100% accurate in estimating how much memory it will use, so to be safe allocate 5 bytes more than you actually need */
CJSON_PUBLIC(cjson_bool) jsonc_output_preallocated(jsonc* item, char* buffer, int length, cjson_bool format);
/* Delete a cJSON entity and all subentities. */
CJSON_PUBLIC(void) jsonc_delete(jsonc* item);
/* Returns the number of items in an array (or object). */
CJSON_PUBLIC(int) jsonc_get_arraysize(const jsonc* array);
/* Retrieve item number "index" from array "array". Returns NULL if unsuccessful. */
CJSON_PUBLIC(jsonc *) jsonc_array_get(const jsonc* array, int index);
/* Get item "string" from object. Case insensitive. */
CJSON_PUBLIC(jsonc *) jsonc_object_get(const jsonc* object, const char* key);
CJSON_PUBLIC(jsonc *) jsonc_object_get_casesensitive(const jsonc* object, const char* key);
CJSON_PUBLIC(cjson_bool) jsonc_object_contains(const jsonc* object, const char* key);
/* For analysing failed parses. This returns a pointer to the parse error. You'll probably need to look a few chars back to make sense of it. Defined when cjson_parse() returns 0. 0 when cjson_parse() succeeds. */
CJSON_PUBLIC(const char *) cjson_strerr(void);
/* Check item type and return its value */
CJSON_PUBLIC(char *) jsonc_tostring(const jsonc* item); 
CJSON_PUBLIC(int) jsonc_copystring(const jsonc* item, const char* key, char * dst); 

CJSON_PUBLIC(double) jsonc_tonumber(const jsonc* item);
CJSON_PUBLIC(int) jsonc_toint(const jsonc* item);
/* These functions check the type of an item */
CJSON_PUBLIC(cjson_bool) jsonc_is_valid(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_false(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_true(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_bool(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_null(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_number(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_string(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_array(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_object(const jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_is_raw(const jsonc* item);
/* These calls create a cJSON item of the appropriate type. */
CJSON_PUBLIC(jsonc *) jsonc_null_new(void);
CJSON_PUBLIC(jsonc *) jsonc_true_new(void);
CJSON_PUBLIC(jsonc *) jsonc_false_new(void);
CJSON_PUBLIC(jsonc *) jsonc_bool_new(cjson_bool val);
CJSON_PUBLIC(jsonc *) jsonc_number_new(double val);
CJSON_PUBLIC(jsonc *) jsonc_string_new(const char* val);
/* raw json */
CJSON_PUBLIC(jsonc *) jsonc_raw_new(const char* val);
CJSON_PUBLIC(jsonc *) jsonc_array_new(void);
CJSON_PUBLIC(jsonc *) jsonc_object_new(void);
/* Create a string where valuestring references a string so
 * it will not be freed by cJSON_Delete */
CJSON_PUBLIC(jsonc *) jsonc_stringref_new(const char* val);
/* Create an object/array that only references it's elements so
 * they will not be freed by cJSON_Delete */
CJSON_PUBLIC(jsonc *) jsonc_objectref_new(const jsonc* child);
CJSON_PUBLIC(jsonc *) jsonc_arrayref_new(const jsonc* child);
/* These utilities create an Array of count items.
 * The parameter count cannot be greater than the number of elements in the number array, otherwise array access will be out of bounds.*/
CJSON_PUBLIC(jsonc *) jsonc_array_new_int(const int* numbers, int count);
CJSON_PUBLIC(jsonc *) jsonc_array_new_float(const float* numbers, int count);
CJSON_PUBLIC(jsonc *) jsonc_array_new_double(const double* numbers, int count);
CJSON_PUBLIC(jsonc *) jsonc_array_new_string(const char* const * strings, int count);
/* Append item to the specified array/object. */
CJSON_PUBLIC(cjson_bool) jsonc_array_add(jsonc* array, jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_array_remove(jsonc* object, const char* string, jsonc* item);
/* Use this when string is definitely const (i.e. a literal, or as good as), and will definitely survive the cJSON object.
 * WARNING: When this function was used, make sure to always check that (item->type & cJSON_StringIsConst) is zero before
 * writing to `item->string` */
CJSON_PUBLIC(cjson_bool) jsonc_object_add(jsonc* object, const char* key, jsonc* item);
/* Append reference to item to the specified array/object. Use this when you want to add an existing cJSON to a new cJSON, but don't want to corrupt your existing cJSON. */
CJSON_PUBLIC(cjson_bool) jsonc_array_addref(jsonc* array, jsonc* item);
CJSON_PUBLIC(cjson_bool) jsonc_object_addref(jsonc* object, const char* key, jsonc* item);
/* Remove/Detach items from Arrays/Objects. */
CJSON_PUBLIC(jsonc *) jsonc_detach_viaptr(jsonc* parent, jsonc* item);
CJSON_PUBLIC(jsonc *) jsonc_array_detach(jsonc* array, int at);
CJSON_PUBLIC(void) jsonc_array_delete(jsonc* array, int at);
CJSON_PUBLIC(jsonc *) jsonc_object_detach(jsonc* object, const char* string);
CJSON_PUBLIC(jsonc *) jsonc_object_detach_casesensitive(jsonc* object, const char* key);
CJSON_PUBLIC(void) jsonc_object_delete(jsonc* object, const char* key);
CJSON_PUBLIC(void) jsonc_object_delete_casesensitive(jsonc* object, const char* key);
/* Update array items. */
CJSON_PUBLIC(cjson_bool) jsonc_array_insert(jsonc* array, int at, jsonc* newitem);
/* Shifts pre-existing items to the right. */
CJSON_PUBLIC(cjson_bool) jsonc_replace_viaptr(jsonc* parent, jsonc* item, jsonc* replacement);
CJSON_PUBLIC(cjson_bool) jsonc_array_replace(jsonc* array, int at, jsonc* newitem);
CJSON_PUBLIC(cjson_bool) jsonc_object_replace(jsonc* object, const char* key, jsonc* newitem);
CJSON_PUBLIC(cjson_bool) jsonc_object_replace_casesensitive(jsonc* object, const char* key, jsonc* newitem);
/* Clone a cjson item */
/* Clone will create a new, identical cjson item to the one you pass, in new memory that will
 * need to be released. With recurse!=0, it will duplicate any children connected to the item.
 * The item->next and ->prev pointers are always zero on return from Duplicate. */
CJSON_PUBLIC(jsonc *) jsonc_clone(const jsonc* item, cjson_bool recurse);
/* Recursively compare two cJSON items for equality. If either a or b is NULL or invalid, they will be considered unequal.
 * case_sensitive determines if object keys are treated case sensitive (1) or case insensitive (0) */
CJSON_PUBLIC(cjson_bool) jsonc_compare(const jsonc* a, const jsonc* b, cjson_bool case_sensitive);
/* Minify a strings, remove blank characters(such as ' ', '\t', '\r', '\n') from strings.
 * The input pointer json cannot point to a read-only address area, such as a string constant, 
 * but should point to a readable and writable address area. */
CJSON_PUBLIC(void) jsonc_minify(char* json);
/* Helper functions for creating and adding items to an object at the same time.
 * They return the added item or NULL on failure. */
CJSON_PUBLIC(jsonc*) jsonc_object_add_null(jsonc* object, const char* name);
CJSON_PUBLIC(jsonc*) jsonc_object_add_true(jsonc* object, const char* name);
CJSON_PUBLIC(jsonc*) jsonc_object_add_false(jsonc* object, const char* name);
CJSON_PUBLIC(jsonc*) jsonc_object_add_bool(jsonc* object, const char* name, cjson_bool boolean);
CJSON_PUBLIC(jsonc*) jsonc_object_add_number(jsonc* object, const char* name, double number);
CJSON_PUBLIC(jsonc*) jsonc_object_add_string(jsonc* object, const char* name, const char* string);
CJSON_PUBLIC(jsonc*) jsonc_object_add_raw(jsonc* object, const char* name, const char* raw);
CJSON_PUBLIC(jsonc*) jsonc_object_add_object(jsonc* object, const char* name);
CJSON_PUBLIC(jsonc*) jsonc_object_add_array(jsonc* object, const char* name);
/* When assigning an integer value, it needs to be propagated to valuedouble too. */
#define cjson_setint(object, number) ((object) ? (object)->valueint = (object)->valuedouble = (number) : (number))
/* helper for the cJSON_SetNumberValue macro */
CJSON_PUBLIC(double) jsonc_help_setnumber(jsonc* object, double number);
#define cjson_setnumber(object, number) (((object) != NULL) ? jsonc_help_set_number(object, (double)(number)) : (number))
/* Change the valuestring of a cJSON_String object, only takes effect when type of object is cJSON_String */
CJSON_PUBLIC(char*) jsonc_help_setstring(jsonc* object, const char* valuestring);
/* Macro for iterating over an array or object */
#define jsonc_foreach(element, array) for((element) = ((array) != NULL) ? (array)->child : NULL; (element) != NULL; (element) = (element)->next)
/* malloc/free objects using the malloc/free functions that have been set with cJSON_InitHooks */
CJSON_PUBLIC(void *) jsonc_malloc(size_t size);
CJSON_PUBLIC(void) jsonc_free(void* object);
#ifdef __cplusplus
}
#endif
#endif
