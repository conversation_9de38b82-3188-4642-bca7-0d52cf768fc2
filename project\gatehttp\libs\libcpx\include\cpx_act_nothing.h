#pragma once
#include <cpx_action.h>
#include "cpx_action_builtin_wrapper.h"

namespace cpx {
	class cpx_act_nothing final : public cpx_action {
	public:
		DELETE_MOVE_COPY(cpx_act_nothing)
		CPXA_CPX_ACT_NOTHING_WRAPPER()
		cpx_act_nothing();
		~cpx_act_nothing() override;
	protected:
		void on_start(cpx_input*, cpx_output*) const override;
		void on_stop(cpx_input*, cpx_output*) const override;
		bt::btree_status on_update(cpx_input*, cpx_output*) const override;
	};
}
