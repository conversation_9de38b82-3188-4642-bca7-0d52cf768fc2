#pragma once
#include <libcute.h> 

namespace sys {
	class graph_route;
	class graph_central;
	class service_entity;

	class graph_stat {
	public:
		DELETE_MOVE_COPY(graph_stat)
		graph_stat();
		~graph_stat();

		struct context_t {
			service_entity* entity{};
			const graph_central* central{};
			const graph_route * routes{};
			time_t time{};
		};

		void update(const context_t*);
		bool running{};
	private:
		time_t _time{};
	};
}
