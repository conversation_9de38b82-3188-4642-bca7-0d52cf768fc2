#pragma once
#include "web_message.h"
#include "web_peer.h"

namespace net {
	class web_server;
	struct web_stream_reader;
	struct web_parser;
	struct web_session_request;
	struct web_session_response;

	class web_session final : public web_peer {
	public:
		friend web_session_request;
		friend web_session_response;
		DELETE_MOVE_COPY(web_session)
		web_session(web_server*, uv_loop_t*, size_t);
		~web_session() override;
		void reset();
		int on_http_parse(const char*, size_t);
		const web_message& msg() const;
		http_phase phase() const;

	private:
		void notify_server();
		web_message _msg{};
		web_server* _server{};
		web_parser* _parser{};
		web_session_request* _req{};
		web_session_response* _resp{};
	};
}
