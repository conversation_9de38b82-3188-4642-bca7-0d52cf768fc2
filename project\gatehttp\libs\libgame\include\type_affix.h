#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "thing_group.h"
#include <affix_property.h>
#include <affix_tree.h>
#include <btree_list.h>

namespace tvo {
	class type_buff;
	class type_props;

	class type_affix final : public type_data {
	public:
		static void awake_all(const awake_context_t&);
		cpx::btree_list<affix::affix_property> property_list;
		cpx::btree_list<affix::affix_tree> constrain_list;
		bool can_equip(type_props*) const;
		void make_property(affix::input_t*, affix::output_t*) const;
		type_buff* buff() const;

	private: 
		type_buff * _buff{};
	public:
		// @see TYPE_AFFIX_WRAPPER() in typeclass_define.h
		enum position_t {
			kPositionNull = 0,
			kPostionFront,
			kPostionAfter
		};

		TYPE_AFFIX_WRAPPER() 

	
	};
}
