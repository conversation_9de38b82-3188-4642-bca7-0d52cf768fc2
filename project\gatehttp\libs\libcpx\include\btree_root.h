#pragma once
#include <libcute.h>
#include <mutex>
#include <sys_exception.h>
#include <btree_factory.h>
#include <context_cluster.h>

namespace bt {
	template <typename BaseT>
	class btree_root : public BaseT {
	public:
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;
		using factory_t = btree_factory<base_type>;

		enum source_type_t {
			kScene,
			kFile,
		};

		void parse(const dynamic_vo& vo) final {
			if (!vo.contains("name")) {
				throw_e("is not a tree file");
			}
			_name = vo.at("name");
			this->base_type::tree_size = 1;
			if (!vo.contains("children")) {
				return;
			}
			auto list = vo.at("children");
			for (const auto& jc : list) {
				auto* c = make(jc, base_type::tree_size);
				if (c) {
					base_type::add(c);
				}
			}
			_cpool.size(base_type::tree_size + 1);
			_cpool.name("btree: " + _name);
		}

		context_cluster_pool& cpool() {
			return _cpool;
		}

		std::string name() const noexcept {
			return _name;
		}

		source_type_t src_type() const {
			return _src_type;
		}

		void src_type(source_type_t v) {
			_src_type = v;
		}

	private:
		context_cluster_pool _cpool{};
		std::mutex _mutex;
		std::string _name{};
		source_type_t _src_type{};

		base_type* make(const dynamic_vo& vo, size_t& total) {
			std::string cmd = vo["cmd"];
			base_type* q = factory_t::create(cmd);
			if (nullptr == q) {
				log_error("btree[%s] not registed", cmd.c_str());
				return nullptr;
			}
			q->idx = total++;
			q->root = this;
			q->parse(vo);
			if (vo.contains("children")) {
				const auto& list = vo.at("children");
				for (const auto& jc : list) {
					auto* c = make(jc, total);
					if (!c) {
						continue;
					}
					q->base_type::add(c);
				}
			}
			q->awake();
			return q;
		}

	protected:
		void on_start(input_type*, output_type*) const final {
			throw_e("on_start() should not be called , use static function ::update_node() ");
		}

		btree_status on_update(input_type*, output_type*) const final {
			throw_e("on_update() should not be call, use static function ::update_node()");
			return BST_FAILURE;
		}

		void on_stop(input_type*, output_type*) const final {
		}

	public:
		static btree_status update_node(const base_type*, input_type*, output_type*);
	};

	/*basiclly is a sequence run mode*/
	template <typename BaseT>
	btree_status btree_root<BaseT>::update_node(const base_type* node, input_type* in, output_type* out) {
		bool running = false;
		bool all_fail = true;
		for (size_t i = 0; i < node->size(); ++i) {
			const auto* c = node->child_at(i);
			const auto cst = c->status(in);
			if (cst == BST_SUCCESS) {
				all_fail = false;
				continue;
			}
			if (cst == BST_FAILURE) {
				continue;
			}
			const auto st = c->update(in, out);
			switch (st) {
				case BST_SUCCESS:
					c->status(in, st);
					c->stop(in, out);
					all_fail = false;
					break;
				case BST_RUN:
					running = true;
					all_fail = false;
					break;
				case BST_FAILURE:
					c->status(in, st);
					c->stop(in, out);
					break;
				case BST_IDLE:
					break;
				default:
					throw_e("out of range");
					break;
			}
		}
		btree_status r;
		if (running) {
			r = BST_RUN;
		} else if (all_fail) {
			r = BST_FAILURE;
		} else {
			r = BST_SUCCESS;
		}
		return r;
	}
}
