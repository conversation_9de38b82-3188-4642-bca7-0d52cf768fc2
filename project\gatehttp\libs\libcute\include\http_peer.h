#pragma once
#include "net_peer.h"
#include "http_stream_write.h"

namespace net {
	class http_peer : public net_peer {
	public:
		friend class http_request;
		friend class http_worker;
		DELETE_MOVE_COPY(http_peer)
		const http_message_t& message() const;
		pb_buffer& body() const;
		void on_phase(http_phase);

	protected:
		virtual void on_parse_(http_phase) = 0;
		http_peer(uv_loop_t* loop, net_peer_id);
		void reset();
		size_t read(pb_buffer*);
		~http_peer() override;
		http_phase _phase;

	public:
		class co_read final : public co_job {
		public:
			DELETE_MOVE_COPY(co_read)
			explicit co_read(ptr_t ptr);
			~co_read() override;

		private:
			ptr_t _ptr;
			void on_read(const event_t*);
		};
	};
}
