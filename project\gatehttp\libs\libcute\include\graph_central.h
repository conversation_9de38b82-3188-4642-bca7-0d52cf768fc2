#pragma once
#include <libcute.h>
#include <mutex>
#include <shared_mutex>
#include "cluster_event.h"
#include "net_base.h"
#include "service_d.h"
#include "service_form.h"
#include "service_struct.h"
#include "tcp_peer.h"
class c2s_service_info;
class s2c_service_shakehand;

namespace net {
	class net_base;
}

namespace sys {
	class cluster_manager;
	class consistent_ring;

	class graph_central {
		using lock_read = std::shared_lock<std::shared_mutex>;
		using lock_write = std::lock_guard<std::shared_mutex>;

	public:
		enum step_t {
			kIdle,
			kConnecting,
			kShaking,
			kWorking
		};

		struct event_t {
			graph_central* graph{};
			std::vector<cluster_event> events{};
			step_t step{};
		};

		typedef void (*event_callback)(const event_t*);
		DELETE_MOVE_COPY(graph_central)
		graph_central();
		~graph_central();
		friend cluster_manager;
		bool select_one(const route_t&, service_d&);
		bool is_running(const route_t&);
		void to_array(int t, std::vector<service_d>& to);
		void to_array(std::vector<service_d>& to);
		void to_cring(int t, consistent_ring& to);

		template <typename Fn, typename... Args,
		          std::enable_if_t<std::is_invocable_r_v<bool, const service_d&, Fn, Args...>>* = nullptr>
		void select_list(const int t, std::vector<service_d>& to, Fn&& fn, Args&&... args) {
			lock_read g(_mutex);
			const auto it = _sheets.find(t);
			if (it == _sheets.end()) {
				return;
			}
			auto* sh = &it->second;
			for (const auto& [_,d] : sh->map) {
				if (fn(d, std::forward<Args>(args)...)) {
					to.emplace_back(d);
				}
			}
		}

		template <typename Fn, typename... Args,
		          std::enable_if_t<std::is_invocable_r_v<bool, const service_d&, Fn, Args...>>* = nullptr>
		bool select_one(const int t, service_d& out, Fn&& fn, Args&&... args) {
			lock_read g(_mutex);
			const auto it = _sheets.find(t);
			if (it == _sheets.end()) {
				return nullptr;
			}
			auto* sh = &it->second;
			for (const auto& [_, d] : sh->map) {
				if (fn(d, std::forward<Args>(args)...)) {
					out = d;
					return true;
				}
			}
			return false;
		}

	private:
		service_d* locate(const route_t&);
		const service_d* locate(const route_t&) const;
		service_d& make(const route_t&);
		/*
			recv info and trigger event
		*/
		void recv_prepare(c2s_service_info&&, std::vector<cluster_event>&);
		void recv_remove(c2s_service_info&&, std::vector<cluster_event>&);
		void recv_join(c2s_service_info&&, std::vector<cluster_event>&);
		void recv_update_info(const ref_ptr<service_form>&);
		void callback(std::vector<cluster_event>&&);
		/*
			shake hand
		*/
		void recv_shakehand_request(s2c_service_shakehand&&) const;
		void recv_shakehand_response(s2c_service_shakehand&&);
		void recv_shakehand(const ref_ptr<service_form>&);
		bool send_shakehand(const std::vector<ref_ptr<net::tcp_peer>>& links) const;
		bool all_hand_shaked() const;

	public:
		std::vector<route_t> unshaked_services() const;

	private:
		/*
			call login
		*/
		void process_join();

		struct sheet_t {
			hashmapx<route_t, service_d> map{};
		};

		hashmapx<int, sheet_t> _sheets{};
		std::shared_mutex _mutex;

	public:
		void enter();
		void update(time_t);
		void exit();
		std::vector<ref_ptr<net::tcp_peer>> alives() const;
		step_t step{};
		event_callback cb{};

	private:
		void on_conn_event(const net::net_event*);
		void on_conn_lost(net::net_base*);
		void retry_connect();
		void recv(net::net_base*, const ref_ptr<service_form>&);
		void move_next();
		bool all_connection_ok() const;
		hashmapx<route_t, ref_ptr<net::tcp_peer>> _connections{};
		time_t _tick_time{};
		time_t _time{};
	};
}
