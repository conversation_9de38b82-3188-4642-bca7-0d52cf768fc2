#ifndef ECS_MACRO_H
#define ECS_MACRO_H 
#include <pb_manager.h>
#include <ecs_object.h>
#include <ecs_value.h>
#include <ecs_domain.h>
#include <ecs_class.h>
#include <system_error.h>
#include <logger.h>
#define ecs_object_getter(num,field, field_k,field_t)__o.at(field_k)->get_to(field);
#define ecs_object_setter(num,field, field_k,field_t)__o.at(field_k)->set_to(field);
#define ecs_object_copy(num, field, field_k,field_t)field = __o->field;
#define ecs_other_to(num, name, str, ptype)v.name = name;
#define ecs_other_from(num, name, str, ptype)name = v.name;
#define ecs_object_field(num, field, field_k,field_t)field_t field{};
/*

*/
#define esc_to_mise(T, DEFINES)		\
template <typename T1>				\
explicit operator T1() const {T1 v{};	DEFINES(ecs_other_to)	return v;} \
template <typename T1>				\
explicit T(const T1& v) { DEFINES(ecs_other_from)}
/*

*/
#define ECS_CLASS_DEFINE(T,name, DEFINES)						\
T(const ecs::object_t & __o) {									\
	if(!__o.ok())return ;										\
	DEFINES(ecs_object_getter)									\
}																\
operator ecs::object_t() const{									\
	auto* d = pb_manager::current_domain();						\
	auto* c = d->find(#name);									\
	if (!c) {													\
		throw_e("protobuf message [%s] is undefined:", #name);	\
	}															\
	auto __o = c->new_instance();								\
	DEFINES(ecs_object_setter)									\
	return __o;													\
}																\
esc_to_mise(T, DEFINES)
/*

sub object implement ecs::iparser_t ;
*/
#define ECS_SUBVO_DEFINE(T, name, DEFINES)								\
int to_ecs(const ecs::object_t & __o) const override{					\
	if (!__o.ok()) return ERR_USE_NULLPTR;								\
	DEFINES(ecs_object_setter)											\
	return 0;															\
}																		\
int from_ecs(const ecs::object_t & __o) override{						\
	if(!__o.ok())return ERR_USE_NULLPTR;								\
	DEFINES(ecs_object_getter)											\
	return 0;															\
}																		\
const char* ecs_name() const noexcept override{return #name;}			\
void parse(const T* __o) {												\
	if(!__o ) return ;													\
	DEFINES(ecs_object_copy)											\
	mark_fdirty();														\
}
/*


*/
#define ECS_FIELD_DEFINE(DEFINES)\
DEFINES(ecs_object_field)
#endif
