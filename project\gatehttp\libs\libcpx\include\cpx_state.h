#pragma once
#include <hashmapx.h>
#include <libcutex.h>
#include <logger.h>
#include <vector>
#include "cpx_core.h"
#include "rpx_state.h"

namespace cpx {
	class cpx_machine;
	class rpx_state;
	class cpx_transition;
}

namespace cpx {
	class cpx_state : public cpx_core {
	public:
		DELETE_MOVE_COPY(cpx_state)
		enum state_type_t {
			kStateAction,
			kStateAny,
			kStateExit,
			kStateMachine
		};

		virtual state_type_t state_type() const = 0;
		virtual int forcast_priority() const;
		virtual rpx_state* obtain() const = 0;
		virtual void release(rpx_state*) const = 0;
		std::vector<cpx_transition*> transitions{};
		std::string stateName{};
		const cpx_machine* mac {};
		bool isDefault{};
		bool isEnter{};
		int priority{};
		void read(const dynamic_vo&, const std::string& file);
		bool can_reach(const cpx_state*);
		~cpx_state() override;
		std::string str() const;
	protected:
		virtual void parse(const dynamic_vo&) = 0;
		cpx_state();
	private:
		void read_transitions(const dynamic_vo&, const std::string& file);
		sys::hashmapx<const cpx_state*, bool> _reachable{};
	};

	
}
