#ifndef  VECTOR_C_H
#define  VECTOR_C_H 
#include "libcute.h"
LP_NS_BEGIN
typedef struct vectorc_s vectorc;
typedef int (*vectorc_cmp_cb)(void* element);

struct vectorc_s {
	size_t size_;
	size_t capacity_;
	size_t nmemb_;
	void* array_;
	size_t version_;
};

#define  vectorc_capacity(v_ptr)(v_ptr)->capacity_;
#define  vectorc_size(v_ptr)(v_ptr)->size_
#define  vectorc_elmentat(v_ptr , idx)\
((void *)(((char *)((v_ptr)->array_))+(idx)*(v_ptr)->nmemb_))  

//iterator the vectorc
#define vectorc_foreach(v_ptr, p_ele)										\
vectorc_get_element(v_ptr,0,(p_ele));										\
for(size_t __VC_IDX__ = 1, __VC_VER__ = (v_ptr)->version_;					\
__VC_IDX__<=(v_ptr)->size_;													\
vectorc_get_element(v_ptr,__VC_IDX__,(p_ele)),								\
assert((v_ptr)->version_==__VC_VER__ && "vector change during iterator"),	\
++__VC_IDX__)
LP_API int vectorc_init(vectorc* v, size_t nmemb, size_t capacity);
LP_API int vectorc_add(vectorc* v, const void* element);
LP_API int vectorc_remove_at(vectorc* v, size_t index);
LP_API int vectorc_remove(vectorc* v, void* element);
LP_API int vectorc_remove_range(vectorc* v, size_t index, size_t count);
LP_API int vectorc_remove_on(vectorc* v, vectorc_cmp_cb cmp);
LP_API int vectorc_clear(vectorc* v);
LP_API int vectorc_get_element(vectorc* v, size_t idx, void* element);
LP_API int vectorc_set_element(vectorc* v, size_t idx, void* element);
LP_API int vectorc_get_ptr(vectorc* v, size_t idx, void** ptr);
LP_API size_t vectorc_get_size(vectorc* v);
LP_API size_t vectorc_get_capacity(vectorc* v);
LP_API int vectorc_free(vectorc* v);
LP_NS_END
#endif
