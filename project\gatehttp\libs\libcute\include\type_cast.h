#pragma once
#ifndef TYPE_CAST_H
#define TYPE_CAST_H
#include "libcute.h"
#include <str_tools.h>
#include <typeinfo> 
#include <cassert>
#define TYPE_CAST_BASE_DEFINE(T)							\
public :													\
virtual const 	char* class_n() 	const noexcept  = 0;	\
virtual 		int   class_id() 	const noexcept  = 0;	\
virtual	~T() = default;
/*

*/
#define TYPE_CAST_IMPLEMENT(T)													\
static int assign_type(){														\
	static const int __at__ = sys::gen_hash32(typeid(T).name());				\
	return __at__	;															\
}																				\
static const char * assign_name(){												\
	static const char * __an__ = typeid(T).name(); 								\
	return __an__ ; 															\
}																				\
const 	char* 	class_n()	const noexcept override	{return assign_name();}		\
		int 	class_id()	const noexcept override {return assign_type();}

namespace sys {
	template <typename T, typename Base>
	using is_true_type = std::enable_if_t<std::is_base_of_v<Base, T> && !std::is_abstract_v<T>>;
	template <typename T, typename Base>
	using is_virtual_type = std::enable_if_t<!std::is_base_of_v<Base, T> || std::is_abstract_v<T>>;

	template <typename T, typename Base, is_true_type<T, Base> * = nullptr>
	T* type_cast(Base* b) {
		return b && b->class_id() == T::assign_type() ? static_cast<T*>(b) : nullptr;
	}

	template <typename T, typename Base, is_true_type<T, Base> * = nullptr>
	const T* type_cast(const Base* b) {
		return b && b->class_id() == T::assign_type() ? static_cast<const T*>(b) : nullptr;
	}

	template <typename T, typename Base, is_virtual_type<T, Base> * = nullptr>
	T* type_cast(Base* b) {
		return b ? dynamic_cast<T*>(b) : nullptr;
	}

	template <typename T, typename Base, is_virtual_type<T, Base> * = nullptr>
	const T* type_cast(const Base* b) {
		return b ? dynamic_cast<const T*>(b) : nullptr;
	}

	template <typename T, typename BaseType>
	struct struct_type_impl : BaseType {
		TYPE_CAST_IMPLEMENT(T)
	};

	template <typename T, typename BaseType>
	class class_type_impl : public BaseType {
	public:
		TYPE_CAST_IMPLEMENT(T)
	};
}

namespace sys {
	template <typename T, typename Base, std::enable_if<std::is_base_of_v<Base, T>> * = nullptr>
	T* force_cast(Base* in) {
		if (!in)return nullptr;
#if DEBUG
		T* o = dynamic_cast<T*>(in);
		if (!o) { 
			assert(nullptr != o && "cast wrong type");
		}
		return o;
#else
		return static_cast<T*>(in);
#endif
	}
}
#endif
