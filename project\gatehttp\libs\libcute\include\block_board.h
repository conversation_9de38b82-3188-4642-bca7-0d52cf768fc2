#pragma once
#include <utility>
#include <vector>
#include "libcute.h"
#include "sys_exception.h"
#include <type_cast.h>

namespace sys {
	template <typename Base, size_t Block>
	class block_board {
		using self_type = block_board<Base, Block>;
	public:
		using base_type = Base;
		template <typename T>
		using support_type = std::enable_if_t<
			std::is_base_of_v<base_type, T> &&
			sizeof(T) <= Block - sizeof(void*)
		>;

		template <typename T, support_type<T>* = nullptr>
		void emplace_back(const T& v) {
			expend_size(_size + 1);
			entry* e = &_entries[_size ++];
			e->holder = new(e->mem)holder_t<T>(v);
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<base_type, T>> * = nullptr>
		T* arg() {
			for (size_t i = 0; i < _size; ++i) {
				auto* h = _entries[i].holder;
				if (!h) {
					continue;
				}
				auto* v = h->obj();
				auto* t = type_cast<T>(v);
				if (t) return t;
			}
			return nullptr;
		}

		template <typename T, typename Fn, typename ...Args,
		          std::enable_if_t<std::is_base_of_v<base_type, T> &&
			          std::is_invocable_r_v<bool, Fn, const T*, Args...>> * = nullptr>
		T* select_one(Fn&& fn, Args&& ... args) {
			for (size_t i = 0; i < _size; ++i) {
				auto* h = _entries[i].holder;
				if (!h) {
					continue;
				}
				auto* v = h->obj();
				auto* t = type_cast<T>(v);
				if (t && fn(t, std::forward<Args>(args)...)) {
					return t;
				}
			}
			return nullptr;
		}

		template <typename T, typename Fn, typename ...Args,
		          std::enable_if_t<std::is_base_of_v<base_type, T> &&
			          std::is_invocable_r_v<bool, Fn, const T*, Args...>> * = nullptr>
		void select_list(std::vector<T*>& to, Fn&& fn, Args&& ... args) {
			for (size_t i = 0; i < _size; ++i) {
				auto* h = _entries[i].holder;
				if (!h) {
					continue;
				}
				auto* v = h->obj();
				auto* t = type_cast<T>(v);
				if (t && fn(t, std::forward<Args>(args)...)) {
					to.emplace_back(t);
				}
			}
		}

		size_t size() const {
			return _size;
		}

		bool empty() const {
			return _size == 0;
		}

		base_type& at(size_t p) const {
			if (_size >= _capacity) {
				throw_e("out of range");
			}
			auto* h = _entries[p].holder;
			if (!h) {
				throw_e("holder is nil");
			}
			return *h->obj();
		}

		void reserve(const size_t v) {
			expend_size(v);
		}

		block_board() = default;

		~block_board() {
			delete []_entries;
			_capacity = 0;
			_size = 0;
		}

		block_board(const block_board& rhs) {
			copy(rhs);
		}

		block_board(block_board&& rhs) noexcept {
			swap(std::forward<block_board>(rhs));
		}

		block_board& operator =(const block_board& rhs) {
			if (this != &rhs) {
				copy(rhs);
			}
			return *this;
		}

		block_board& operator =(block_board&& rhs) noexcept {
			if (&rhs != this) {
				swap(std::forward<block_board>(rhs));
			}
			return *this;
		}

		void swap(block_board&& rhs) {
			std::swap(rhs._entries, _entries);
			std::swap(rhs._size, _size);
			std::swap(rhs._capacity, _capacity);
		}

		void copy(const block_board& rhs) {
			expend_size(rhs._size);
			for (size_t i = 0; i < rhs._size; ++i) {
				_entries[i] = rhs._entries[i];
			}
			_size = rhs._size;
		}

		void append(const block_board& rhs) {
			expend_size(_size + rhs._size);
			for (size_t i = 0; i < rhs._size; ++i) {
				_entries[i + _size] = rhs._entries[i];
			}
			_size += rhs._size;
		}

		void clear() {
			for (size_t i = 0; i < _size; ++i) {
				entry* e = &_entries[i];
				e->~entry();
				new(e)entry();
			}
			_size = 0;
		}

	private:
		struct place_holder {
			DELETE_MOVE_COPY(place_holder)
			place_holder() = default;
			virtual place_holder* clone(void*) const =0;
			virtual base_type* obj() = 0;
			virtual ~place_holder() = default;
		};

		template <typename T>
		struct holder_t final : place_holder {
			using value_type = T;
			DELETE_MOVE_COPY(holder_t)
			holder_t() = default;

			explicit holder_t(value_type v): value(std::move(v)) {
			}

			base_type* obj() override {
				return &value;
			}

			place_holder* clone(void* mem) const override {
				return new(mem)holder_t(value);
			}

			value_type value{};
			~holder_t() override = default;
		};

		struct entry {
			place_holder* holder{};
			char mem[Block]{};

			entry(): holder(nullptr) {
			}

			~entry() {
				if (holder) {
					holder->~place_holder();
				}
				holder = nullptr;
			}

			entry(const entry& rhs) {
				holder = rhs.holder
					         ? rhs.holder->clone(mem)
					         : nullptr;
			}

			entry(entry&& rhs) noexcept {
				swap(rhs);
			}

			entry& operator =(entry&& rhs) noexcept {
				if (this != &rhs) {
					swap(std::forward<entry>(rhs));
				}
				return *this;
			}

			entry& operator =(const entry& rhs) {
				if (this != &rhs) {
					copy(rhs);
				}
				return *this;
			}

			void swap(entry&& rhs) noexcept {
				entry e{};
				if (rhs.holder) {
					e.holder = rhs.holder->clone(e.mem);
					rhs.holder->~place_holder();
				}
				rhs.holder = holder
					             ? holder->clone(rhs.mem)
					             : nullptr;
				if (holder) {
					holder->~place_holder();
				}
				holder = e.holder
					         ? e.holder->clone(mem)
					         : nullptr;
			}

			void copy(const entry& rhs) {
				if (holder)holder->~place_holder();
				holder = rhs.holder
					         ? rhs.holder->clone(mem)
					         : nullptr;
			}
		};

		void expend_size(const size_t val) {
			if (val < _capacity) {
				return;
			}
			size_t cap = _capacity ? _capacity << 1 : 16;
			while (cap < val) {
				cap = cap << 1;
			}
			auto* list = new entry[cap]();
			for (size_t i = 0; i < _size; ++i) {
				list[i] = std::move(_entries[i]);
			}
			delete[] _entries;
			_entries = list;
			_capacity = cap;
		}

		entry* _entries{};
		size_t _size{};
		size_t _capacity{};
	public:
		struct iterator {
			using iterator_category = std::forward_iterator_tag;
			using value_type = entry;
			using difference_type = ptrdiff_t;
			using pointer = entry*;
			using reference = entry&;

			iterator(block_board* b, const size_t pos): _board(b), _pos(pos) {
			}

			iterator(): _board(nullptr), _pos(0) {
			}

			iterator(const iterator&) = default;
			iterator& operator =(const iterator&) = default;

			iterator(iterator&& rhs) noexcept {
				swap(rhs);
			}

			iterator& operator =(iterator&& rhs) noexcept {
				if (this != &rhs) {
					swap(std::forward<iterator>(rhs));
				}
				return *this;
			}

			void swap(iterator&& rhs) noexcept {
				std::swap(rhs._board, _board);
				std::swap(rhs._pos, _pos);
			}

			~iterator() = default;

			bool operator ==(const iterator& rhs) {
				return rhs._board == _board && rhs._pos == _pos;
			}

			bool operator !=(const iterator& rhs) {
				return !(*this == rhs);
			}

			iterator& operator ++() {
				if (!_board) {
					_board = 0;
					return *this;
				}
				_pos ++;
				if (_board && _board->_size <= _pos) {
					_board = nullptr;
					_pos = 0;
				}
				return *this;
			}

			iterator& operator ++(int) {
				auto temp = *this;
				++(*this);
				return temp;
			}

			base_type& operator *() const {
				if (!_board || _pos >= _board->_size) {
					throw_e("out of range");
				}
				if (!_board->_entries) {
					throw_e("entries is nil");
				}
				auto* h = _board->_entries[_pos].holder;
				if (!h) {
					throw_e("entry is nil");
				}
				return *h->obj();
			}

			base_type* operator->() const {
				if (!_board || _pos >= _board->_size) {
					throw_e("out of range");
					return nullptr;
				}
				if (!_board->_entries) {
					throw_e("entries is nil");
					return nullptr;
				}
				auto* h = _board->_entries[_pos].holder;
				if (!h) {
					throw_e("entry is nil");
					return nullptr;
				}
				return h->obj();
			}

			operator bool() const {
				return _board;
			}

		private:
			block_board* _board;
			size_t _pos;
		};

		struct const_iterator {
			using iterator_category = std::forward_iterator_tag;
			using value_type = entry;
			using difference_type = ptrdiff_t;
			using pointer = entry*;
			using reference = entry&;

			const_iterator(const block_board* b, const size_t pos): _board(b), _pos(pos) {
			}

			const_iterator(): _board(nullptr), _pos(0) {
			}

			const_iterator(const const_iterator&) = default;
			const_iterator& operator =(const const_iterator&) = default;

			const_iterator(const_iterator&& rhs) noexcept {
				swap(rhs);
			}

			const_iterator& operator =(const_iterator&& rhs) noexcept {
				if (this != &rhs) {
					swap(std::forward<const_iterator>(rhs));
				}
				return *this;
			}

			void swap(const_iterator&& rhs) noexcept {
				std::swap(rhs._board, _board);
				std::swap(rhs._pos, _pos);
			}

			~const_iterator() = default;

			bool operator ==(const const_iterator& rhs) {
				return rhs._board == _board && rhs._pos == _pos;
			}

			bool operator !=(const const_iterator& rhs) {
				return !(*this == rhs);
			}

			const_iterator& operator ++() {
				if (!_board) {
					_board = 0;
					return *this;
				}
				_pos ++;
				if (_board && _board->_size <= _pos) {
					_board = nullptr;
					_pos = 0;
				}
				return *this;
			}

			const_iterator& operator ++(int) {
				auto temp = *this;
				++(*this);
				return temp;
			}

			base_type& operator *() const {
				if (!_board || _pos >= _board->_size) {
					throw_e("out of range");
				}
				if (!_board->_entries) {
					throw_e("entries is nil");
				}
				auto* h = _board->_entries[_pos].holder;
				if (!h) {
					throw_e("entry is nil");
				}
				return *h->obj();
			}

			base_type* operator->() const {
				if (!_board || _pos >= _board->_size) {
					throw_e("out of range");
					return nullptr;
				}
				if (!_board->_entries) {
					throw_e("entries is nil");
					return nullptr;
				}
				auto* h = _board->_entries[_pos].holder;
				if (!h) {
					throw_e("entry is nil");
					return nullptr;
				}
				return h->obj();
			}

			operator bool() const {
				return _board;
			}

		private:
			const block_board* _board;
			size_t _pos;
		};

		iterator begin() {
			if (0 == _size) {
				return iterator();
			}
			return iterator(this, 0);
		}

		iterator end() {
			return iterator();
		}

		const_iterator begin() const {
			if (0 == _size) {
				return const_iterator();
			}
			return const_iterator(this, 0);
		}

		const_iterator end() const {
			if (0 == _size) {
				return const_iterator();
			}
			return const_iterator();
		}

		const_iterator cbegin() const {
			return const_iterator(this, 0);
		}

		const_iterator cend() const {
			return const_iterator();
		}
	};
}
