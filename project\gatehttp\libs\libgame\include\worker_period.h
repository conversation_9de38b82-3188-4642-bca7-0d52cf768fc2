#pragma once
#include "usr_worker.h"
#include <ref_ptr.h>
class timeinfo_period;

namespace dvo {
	class data_period;
}

namespace period {
	class worker_period final : public dvo::usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_period)
		void start() override;
		void stop() override;
		void update(const dvo::update_context*) override;
		void recv(const sys::hashmapx<int, timeinfo_period>&) const;
	private:
		void on_notifiction(const dvo::worker_notification*) override;
		void on_load() const;
		time_t _time{};
	};
}
