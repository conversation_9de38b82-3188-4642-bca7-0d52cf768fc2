#pragma once
#include "net_stream_writer.h"

namespace net {
	class http_stream_write final : public net_stream_writer {
	public:
		DELETE_MOVE_COPY(http_stream_write)
		http_stream_write();
		int write(const char*, size_t) override;
		int flush() override;
		void get_uvbuf(uv_buf_t*) override;
		size_t size() override;
		void on_write_done() override;
		size_t capacity() const;
		size_t space() const;
		~http_stream_write() override;
		int write(const recycle_stream&) override;
		int ping(time_t) override;
		int pong(time_t) override;

	private:
		pb_block _bufw;
	};
}
