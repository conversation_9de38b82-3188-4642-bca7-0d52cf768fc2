#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "dlink_make.h"
#include <tvo_enums.h>
#include <range.h>
#include <thing_group.h>
#include "type_skill_override.h"
#include "nav_struct.h"

namespace tvo {
	class type_skill_curse;
	class type_buff;
	class type_fxsource;

	class type_skill_chip final : public type_data {
	public:
		DELETE_MOVE_COPY(type_skill_chip)
		type_skill_chip();
		static void awake_all(const awake_context_t&);
		void awake(const awake_context_t&) override;
		type_fxsource* fxsource() const;
		~type_skill_chip() override;
		void pre_parse(const dynamic_vo&) override;
		UPLEVEL_LINK_FIELDS(type_skill_chip)
		char override_fields[SKILL_CHIP_OVERRIDE_MAX];
		std::vector<type_buff*> bufflist;
		std::vector<type_skill_curse*> curselist;

	public:
		using open_mode_t = skill_open_mode_t;
		using locate_mode_t = skill_locate_mode_t;
		TYPE_SKILL_CHIP_WRAPPER()
	};
}
