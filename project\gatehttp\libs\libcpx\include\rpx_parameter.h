#pragma once
#include <fp_math.hpp>
#include <libcute.h>
#include <hashmapx.h>

namespace cpx {
	class cpx_controller;

	class rpx_parameter {
	public:
		DELETE_MOVE_COPY(rpx_parameter)
		rpx_parameter();
		~rpx_parameter();
		void set(const cpx_controller*);
		void set_int(int name, int value);
		int get_int(int name);
		void set_float(int name, nav::float_t value);
		nav::float_t get_float(int name);
		void set_bool(int name, bool value);
		bool get_bool(int name);
		void set_trigger(int name);
		int get_trigger(int name);
		void reset();
		void reset(int name);
		void clear();
		void add(const rpx_parameter&);
	private:
		sys::hashmapx<int, nav::float_t> _hash{};
		const cpx_controller* _controller{};
	};
}
