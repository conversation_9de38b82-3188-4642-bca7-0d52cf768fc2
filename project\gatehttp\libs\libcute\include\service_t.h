#pragma once
#include "libcute.h"
#include "libcutex.h"
#include "ref_ptr.h"
#include <json_macro.h>

namespace sys {
	struct net_address;
	struct tcp_simple_info;
}

namespace sys {
	class service_tvo {
	public:
		CUTE_REF_PTR_CLASS_DECL(service_tvo)

	public:
		DELETE_MOVE_COPY(service_tvo)

	public:
		service_tvo();
		id_64 id{};
		int type{};
		bool awakable{};
		dynamic_vo config{};
		std::string name{};
		std::string exepath{};
		int64_t max_workload{};
		std::string root_ip{};
		std::string root_ipv6{};
		std::vector<id_64> routes{};
		service_tvo* parent{};
		std::vector<ptr_t> children{};
		int address(net_address&) const;
		int info(tcp_simple_info&) const;
		~service_tvo();
	};

	using service_t = service_tvo::ptr_t;
}
