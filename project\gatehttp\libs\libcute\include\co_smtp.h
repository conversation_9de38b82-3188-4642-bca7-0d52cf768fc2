#pragma once
#include "libcute.h"
#include "ref_ptr.h"
#include "smtp_code.h"
#include "smtp_peer.h"
#define SMTP_COMMAND_INTERVAL (100)

namespace net {
	class co_smtp {
	public:
		DELETE_MOVE_COPY(co_smtp)
		co_smtp() = default;
		~co_smtp();
		std::string serv{};
		int port{};
		int ssl{};
		std::string usr{};
		std::string psw{};
		std::string title{};
		std::string content{};
		std::vector<std::string> recievers{};
		time_t ms_interval{SMTP_COMMAND_INTERVAL};
		int send();

	private:
		int create_conn();
		int send_head();
		int send_content() const;
		int send_end();
		int login();
		int send(const char*, size_t) const;
		stmp_message wait_event();
		int send_base64(const std::string&) const;
		void on_net_event(const net_event*);
		ref_ptr<smtp_peer> _tcp{};
		co_thread::ptr_t _ctx{};
	};
}
