#pragma once
#include "pb.h"
#include "pb_stream.h" 
struct pbd_slice;

class pb_base {
public:
	friend class service_form;
	friend class SocketForm;
	DEFAULT_MOVE_COPY(pb_base)
	pb_base();
	virtual int type() const = 0;
	virtual int encode(pb_buffer*) const = 0;
	//virtual int decode(pbd_slice*, pb_Type* pbt) = 0;
	virtual int decode(pbd_slice*) = 0;
	int decode(pb_slice);
	pb_Type* pbtype() const;
	int error() const;
	operator bool() const;
protected:
	virtual ~pb_base();
private:
	int _error{};
	bool _encode{};
};
