#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "nav_struct.h"
#include "render_interface.h"
#include "render_setting.h"

namespace tvo {
	class type_buff;
	class type_property_relation;
	class type_career;
	class type_body_position;
	class type_skill_positive;
	class type_skill;

	class type_hero final : public type_data,
	                        public ds::render_body {
	public:
		DELETE_MOVE_COPY(type_hero)
		type_hero();
		static void awake_all(const awake_context_t &);
		type_career* get_career() const;
		type_buff* buff() const;
		ds::float_t damage_ratio() const;
		~type_hero() override;
		bool single() const override;
		bool ignore(const type_body_position*) const override;
		std::string art_key() const override;
		int default_set() const override;
		ds::render_display get_display() const override;
		std::vector<type_skill*> skills{};
		std::vector<type_skill_positive*> pskills{};
		using display_t = ds::render_display;
		TYPE_HERO_WRAPPER()

	private: 
		type_career* _career{};  
	};
}
