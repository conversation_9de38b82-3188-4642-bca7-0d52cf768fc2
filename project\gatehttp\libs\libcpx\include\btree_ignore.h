#pragma once
#include <context_cluster.h>

namespace bt {
	template <typename BaseT>
	class btree_ignore : public BaseT {
		using input_type = typename BaseT::input_type;
		using output_type = typename BaseT::output_type;
		using base_type = BaseT;

	public:
		void parse(const dynamic_vo&) final {
		}

		void awake() final {
			base_type* self = static_cast<base_type*>(this);
			++self->root->cnt_ignord;
		}

	protected:
		void on_start(input_type*, output_type*) const final {
			throw_e("on_start() should not be called ");
		}

		btree_status on_update(input_type*, output_type*) const final {
			throw_e("on_update() should not be call");
			return BST_FAILURE;
		}

		void on_stop(input_type*, output_type*) const final {
			throw_e("on_start() should not be called ");
		}
	};
}
