#pragma once
#include <libcute.h>
#include <libcutex.h>
#include <logger.h>
#include <mutex>
#include "btree_status.h"
#include <bt_context.h> 

namespace bt {
	class bt_context;

	class context_cluster_ptr {
		struct holder {
			DELETE_MOVE_COPY(holder)
			virtual void release() = 0;
			virtual bt_context* obtain() = 0;
			virtual const std::type_info& type() const noexcept = 0;
			virtual bt_context* addr() = 0;
			holder();
			virtual ~holder();
		};

		template <typename T, std::enable_if_t<std::is_base_of_v<bt_context, T>> * = nullptr>
		struct holder_t final : holder {
			DELETE_MOVE_COPY(holder_t)

			holder_t() : ctx_(), alive_(true) {
			}

			~holder_t() override {
				release();
			}

			void release() override {
				if (!alive_) {
					return;
				}
				alive_ = false;
				try_call(ctx_.~T());
			}

			bt_context* obtain() override {
				if (!alive_) {
					new(&ctx_)T();
					alive_ = true;
				}
				return &ctx_;
			}

			const std::type_info& type() const noexcept override {
				return typeid(T);
			}

			bt_context* addr() override {
				if (!alive_) {
					return nullptr;
				}
				return &ctx_;
			}

			T ctx_;
			bool alive_;
		};

	public:
		DELETE_MOVE_COPY(context_cluster_ptr)
		explicit context_cluster_ptr(size_t);
		~context_cluster_ptr();
		friend class context_cluster_pool;

		template <typename T, std::enable_if_t<std::is_base_of_v<bt_context, T>> * = nullptr>
		T* context(const size_t p) {
			auto* h = at(p);
			if (!h) {
				h = new holder_t<T>();
				put(p, h);
			}
			return static_cast<T*>(h->obtain());
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<bt_context, T>> * = nullptr>
		T* rawget(const size_t p) {
			auto* h = at(p);
			auto* a = h ? h->addr() : nullptr;
			return a ? static_cast<T*>(a) : nullptr;
		}

		bt_context* rawget(size_t) const;
		btree_status status(size_t) const;
		void reset(size_t) const;
		void stop(size_t) const;
		void freelist() const;
		size_t size() const;
	private:
		holder* at(size_t) const;
		void put(size_t, holder*) const;
		holder** _list;
		size_t _size;
		context_cluster_ptr* _next{};
	};

	class context_cluster_pool {
	public:
		DELETE_MOVE_COPY(context_cluster_pool)
		context_cluster_pool();
		~context_cluster_pool();
		void size(size_t v);
		size_t size() const;
		std::string name() const;
		void name(const std::string&);
		size_t used() const;
		context_cluster_ptr* obtain();
		void release(context_cluster_ptr*); 
		static void print_usage();
	private:
		std::string _name{};
		size_t _size{}; 
		size_t _used{};
		std::recursive_mutex _mutex{};
		context_cluster_ptr* _free{};
	};

	struct context_cluster {
		DELETE_MOVE_COPY(context_cluster)
		context_cluster();
		~context_cluster();
		context_cluster& obtain(context_cluster_pool*);
		void release();

		template <typename T, std::enable_if_t<std::is_base_of_v<bt_context, T>> * = nullptr>
		T* context(const size_t p) const {
			if (!_ptr) {
				throw_e("cluster is nil");
			}
			return _ptr->context<T>(p);
		}

		template <typename T, std::enable_if_t<std::is_base_of_v<bt_context, T>> * = nullptr>
		T* rawget(const size_t p) const {
			if (!_ptr) {
				throw_e("cluster is nil");
			}
			return _ptr->rawget<T>(p);
		}

		operator bool() const;
		bt_context* rawget(size_t) const;
		btree_status status(size_t) const;
		void reset(size_t) const;
		void stop(size_t) const;
		size_t size() const;
	private:
		context_cluster_pool* _cpool;
		context_cluster_ptr* _ptr;
	};
}
