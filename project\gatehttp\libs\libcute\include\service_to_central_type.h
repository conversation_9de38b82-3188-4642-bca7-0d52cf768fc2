/*auto gen, don't modify it*/
#ifndef SERVICE_TO_CENTRAL_TYPE_H 
#define SERVICE_TO_CENTRAL_TYPE_H 
#define SERVICE_TO_CENTRAL_TYPE(XX)\
XX(-688534989, c2s_service_info, "c2s_service_info")\
XX(-1938755376, error_message, "error_message")\
XX(-186010574, s2c_error_log, "s2c_error_log")\
XX(-768143821, s2c_service_info, "s2c_service_info")\
XX(-2065981936, s2c_service_shakehand, "s2c_service_shakehand")\
XX(-767840295, s2c_service_stat, "s2c_service_stat")\
XX(359641704, service_fail, "service_fail")\
XX(359947452, service_ping, "service_ping")\
XX(359953218, service_pong, "service_pong")\
XX(360066279, service_tick, "service_tick")\

#endif  // endof SERVICE_TO_CENTRAL_TYPE_H