#pragma once
#include "libcute.h" 
#include "ecs_object.h"
#include <type_traits>
namespace sys {
	class raw_string;
}

namespace ecs {
	class object_t;
	struct value_t;
	struct fieldinfo_t;

	class array_t {
	public:
		array_t(const array_t&) = delete;
		array_t(array_t&&) noexcept = delete;
		array_t& operator =(array_t&&) noexcept;
		array_t& operator =(const array_t&);
		explicit array_t(const fieldinfo_t*);

		template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
		void emplace(const T v) {
			add_integer(static_cast<int64_t>(v));
		}

		template <typename T, std::enable_if_t<std::is_integral_v<T>>* = nullptr>
		T at(const size_t at) const {
			return static_cast<T>(integer_at(at));
		}

		template <typename T, std::enable_if_t<std::is_floating_point_v<T>>* = nullptr>
		void emplace(const T v) {
			add_real(static_cast<double>(v));
		}

		template <typename T, std::enable_if_t<std::is_floating_point_v<T>>* = nullptr>
		T at(const size_t at) const {
			return static_cast<T>(read_at(at));
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<T, sys::raw_string>>* = nullptr>
		void emplace(const T& v) {
			add_string(sys::raw_string(v));
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<sys::raw_string, T>>* = nullptr>
		T at(const size_t idx) const {
			return static_cast<T>(string_at(idx));
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<T, object_t>>* = nullptr>
		void emplace(const T& v) {
			add_object(static_cast<object_t>(v));
		}

		template <typename T, std::enable_if_t<std::is_convertible_v<object_t, T>>* = nullptr>
		T at(const size_t idx) const {
			return static_cast<T>(object_at(idx));
		}

		value_t* value_at(size_t) const;
		void clear();
		size_t size() const;
		bool empty() const;
		void reserve(size_t);
		value_t* append();
		~array_t();
	private:
		char* next_addr();
		void add_integer(int64_t);
		int64_t integer_at(size_t at) const;
		void add_real(double v);
		double read_at(size_t idx) const;
		void add_string(sys::raw_string&&);
		const sys::raw_string& string_at(size_t) const;
		void add_object(object_t&&);
		const object_t& object_at(size_t) const;
		/*----------------- field infos --------------------*/
		size_t _elesize{};
		int _support{};
		/*----------------- array memory block -----------------*/
		char* _head{};
		size_t _size{};
		size_t _capacity{};
	};
}
