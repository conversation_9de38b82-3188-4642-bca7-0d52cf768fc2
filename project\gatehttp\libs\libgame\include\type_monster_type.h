#pragma once
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"

namespace tvo {
	class type_buff;

	class type_monster_type final : public type_data {
	public:
		DELETE_MOVE_COPY(type_monster_type)
		type_monster_type();
		static void awake_all(const awake_context_t&);
		~type_monster_type() override;
		type_buff* buff() const;

	public:
		TYPE_MONSTER_TYPE_WRAPPER()

	private:
		type_buff* _buff{};
	};
}
