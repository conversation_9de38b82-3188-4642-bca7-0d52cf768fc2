#ifndef TIME_TOOLS_H
#define TIME_TOOLS_H 
#include "libcute.h"
LP_NS_BEGIN
#define PER_MINUTE	(60)
#define PER_HOUR	(3600)
#define PER_DAY		(86400)
#define PER_WEEK	(604800)
typedef struct dtime dtime_t;

struct dtime {
	time_t stamp; // time stamp in second;
	int year; /* Year like 2018.  */
	int month; /* Month.	[0-11] */
	int day; /* Day.		[1-31] */
	int minute; /* Minutes.	[0-59] */
	int second; /* Seconds.	[0-60] (1 leap second) */
	int hour; /* Hours.	[0-23] */
	int wday; /* Day of week.	[0-6] */
	int yday; /* Days in year.[0-365]	*/
	int isdst; /* DST.		[-1/0/1]*/
	short millsec; /* millsecond [0-999]*/
};

LP_API dtime_t cute_system_time(void);
LP_API dtime_t cute_localtime(time_t);
LP_API time_t cute_mktime(const dtime_t*);
LP_API time_t cute_timestamp(void);
LP_API int cute_timezone(void);
/*the system time with double, the floating part is millsecond / 1000 */
LP_API double cute_precise_time(void);
LP_API double cute_precise_maketime(const dtime_t*);
/*default will be  "%Y-%m-%d %T [%Z]" while format is NULL*/
LP_API void cute_dtime_str(const dtime_t* dtm, char* buf, size_t buflen, const char* format);
LP_API void cute_time_str(time_t time, char* buf, size_t buflen, const char* format);
/* get days in year-month, month [0-11]*/
LP_API int cute_month_days(int year, int month);
LP_API int cute_year_days(int year);
/*return the timeslot n format yyyymmdd, eg 20230315 */
LP_API int cute_timeslot_ymd(time_t);
/*return the timeslot n format yyyymm, eg 202303 */
LP_API int cute_timeslot_ym(time_t);
/*return the 00:00:00 of input time stamp*/
LP_API time_t cute_timezero(time_t);
/* 
* parse 2000-12-30 23:56:16 in to dtime_t
*/
LP_API int cute_compile_time(const char*, dtime_t*);
/*
	for cpu issure 
	cache the time per frame so we can use for all the connection 
	is a rough timestamp
*/
LP_API void cute_cache_timestamp(void);
LP_API time_t cute_cached_timestamp(void);
/*
* 
*/
LP_NS_END
#endif
