#pragma once
#include <cstddef>
#include <string>
#include "pb.h"
#include <libcutex.h>
#include <type_traits>

namespace ecs {
	struct value_t;
}

namespace sys {
	class raw_string {
	public:
		friend std::hash<raw_string>;
		raw_string();
		raw_string(const char*);
		raw_string(const std::string&);
		operator std::string() const;
		raw_string(const pb_buffer*);
		raw_string(const char*, size_t);
		raw_string(const pb_slice&);
		operator pb_slice() const;
		raw_string(const raw_string&);
		raw_string(raw_string&&) noexcept;
		raw_string& operator =(raw_string&& rhs) noexcept;
		raw_string& operator =(const raw_string&);
		bool operator ==(const raw_string& rhs) const;
		bool operator !=(const raw_string& rhs) const;
		bool operator ==(const pb_slice&) const;
		bool operator !=(const pb_slice&) const;
		bool operator ==(const std::string&) const;
		bool operator !=(const std::string&) const;
		bool operator ==(const char*) const;
		bool operator !=(const char*) const;
		size_t size() const;
		bool empty() const;
		const char* c_str() const;
		void clear() noexcept;
		void push_to(pb_buffer*) const;
		~raw_string();
		friend void to_json(dynamic_vo&, const raw_string&);
		friend void from_json(const dynamic_vo&, raw_string&);
	private:
		void swap(raw_string&&) noexcept;
		void copy(const raw_string&) noexcept;
		char* _buf{};
		uint32_t _size{};
		uint32_t _capacity{};
	};

	template <typename E, typename T>
	std::basic_ostream<E, T>& operator<<(std::basic_ostream<E, T>& os, const raw_string& str) {
		os << str.c_str();
		return os;
	}
}


namespace std {
	template <>
	struct hash<sys::raw_string> {
		size_t operator()(const sys::raw_string& v) const noexcept;
	};
}
