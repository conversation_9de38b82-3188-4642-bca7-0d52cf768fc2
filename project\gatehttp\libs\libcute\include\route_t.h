#pragma once
#include "libcute.h"
#include "pb.h"
#include <string> 

namespace sys {
	class service_tvo;
}

struct route_t;

struct route_t {
	id_64 id{};
	int type{};
	int role{}; /*process_type*/
	int sub{};
	route_t() = default;
	~route_t();
	route_t(route_t&&) noexcept = default;
	route_t(const route_t& rhs) = default;
	route_t& operator =(const route_t&) = default;
	route_t& operator =(route_t&&) noexcept = default;
	route_t(const sys::service_tvo*);
	explicit route_t(pb_slice);
	explicit route_t(const std::string&);
	route_t(id_64 i, int t, int r, int s);
	route_t(id_64 i, int t);
	bool operator ==(const route_t& v) const;
	bool operator !=(const route_t& v) const;
	bool operator <(const route_t& v) const;
	route_t main() const;
	route_t physic() const;
	std::string str() const;
	bool empty() const;
	static const route_t zero;
};

namespace std {
	template <>
	struct hash<route_t> {
		size_t operator()(const route_t& v) const noexcept;
	};
}
