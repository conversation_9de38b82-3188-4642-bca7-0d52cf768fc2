#pragma once
#include  "libcute.h"
#include "http_peer.h"
#include "ref_ptr.h"

namespace net {
	class http_client final : public http_peer {
	public:
		DELETE_MOVE_COPY(http_client)
		friend class http_request;
		explicit http_client(uv_loop_t* l);

	protected:
		void on_parse_(http_phase) override;
		~http_client() override;

	private:
		void release_();
		void unlock();
		int _occupy;
		int _repeat;
		static ptr_t get(const char*);
	};
}
