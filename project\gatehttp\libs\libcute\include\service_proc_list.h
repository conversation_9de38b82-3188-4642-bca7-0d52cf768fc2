#pragma once
#include <uv.h>
#include <vector>
#include "child_process.h"
#include "delegate.h"
#include "libcute.h"

namespace proc {
	class service_proc_list {
	public:
		typedef delegate<void(const child_exit*)> proc_observe_t;
		CUTE_DELEGATE_FUNC_DECL(proc_observe_t, _observer, observe_proc, stop_observe_proc)
		proc_observe_t _observer{};
	public:
		DELETE_MOVE_COPY(service_proc_list)
		friend child_process;
		explicit service_proc_list(uv_loop_t*);
		int spawn(child_config&&);
		int spawn(reboot_info&&);
		void stop_unit(child_process*, const child_stop_cb&);
		void stop_unit(size_t at, const child_stop_cb&);
		void stop_tail(const child_stop_cb&);
		void co_stop_all();
		virtual ~service_proc_list();
		using child_list = std::vector<child_process*>;
		using iterator = child_list::iterator;
		using const_iterator = child_list::const_iterator;
		size_t size() const;
		child_process* child(size_t at) const;
		iterator begin();
		iterator end();
		const_iterator begin() const;
		const_iterator end() const;
		const_iterator cbegin() const;
		const_iterator cend() const;
	private:
		void on_child_exit(const child_exit*);
		uv_loop_t* _ploop;
		child_list _units{};
	public:
		template <typename Predicator, typename ... Args,
		          std::enable_if_t<std::is_invocable_r_v<bool, Predicator, child_process*, Args ...>>* = nullptr>
		child_process* select(Predicator&& fn, Args&& ... args) const {
			for (auto* p : _units) {
				if (fn(p, std::forward<Args>(args)...)) {
					return p;
				}
			}
			return nullptr;
		}

		template <typename Predicator, typename ... Args,
		          std::enable_if_t<std::is_invocable_r_v<bool, Predicator, child_process*, Args ...>>* = nullptr>
		void select(std::vector<child_process*>& to, Predicator&& fn, Args&& ... args) const {
			for (auto* p : _units) {
				if (fn(p, std::forward<Args>(args)...)) {
					to.emplace_back(p);
				}
			}
		}
	};
}
