#pragma once
#include <string_view>

namespace sys {
	template <typename T>
	constexpr std::string_view type_name();

	template <>
	constexpr std::string_view type_name<void>() {
		return "void";
	}

	using type_name_prober = void;

	template <typename T>
	constexpr std::string_view wrapped_type_name() {
#ifdef __clang__
    return __PRETTY_FUNCTION__;
#elif defined(__GNUC__)
		return __PRETTY_FUNCTION__;
#elif defined(_MSC_VER)
		return __FUNCSIG__;
#else
#error "Unsupported compiler"
#endif
	}

	constexpr std::size_t wrapped_type_name_prefix_length() {
		return wrapped_type_name<type_name_prober>().find(type_name<type_name_prober>());
	}

	constexpr std::size_t wrapped_type_name_suffix_length() {
		return wrapped_type_name<type_name_prober>().length()
			- wrapped_type_name_prefix_length()
			- type_name<type_name_prober>().length();
	}

	template <typename T>
	constexpr std::string_view type_name() {
		constexpr std::string_view wrapped_name = sys::wrapped_type_name<T>();
		constexpr auto prefix_length = wrapped_type_name_prefix_length();
		constexpr auto suffix_length = wrapped_type_name_suffix_length();
		constexpr auto type_name_length = wrapped_name.length() - prefix_length - suffix_length;
		return wrapped_name.substr(prefix_length, type_name_length);
	}

	constexpr int calc_hash_internal(const char* str, const size_t size, const size_t pos, const int h) {
		return pos >= size
			       ? h
			       : calc_hash_internal(str, size, pos + 1,
			                            static_cast<int>(static_cast<int>(h * 31L) + str[pos]));
	}

	template <typename T>
	constexpr int type_hash() {
		constexpr std::string_view tn = type_name<T>();
		return calc_hash_internal(tn.data(), tn.size(), 0, 0);
	}
}
