#pragma once
#include <cstdint>
#include <ctime>
#include "libcute.h"

namespace sys {
	float lerp(float a, float b, float t);
	int lerp(int a, int b, float t);
	double repeat(double t, double length, int& loop);
	int64_t repeat(int64_t t, int64_t length, int& loop);

	template <typename T>
	T min(T a, T b) { return a < b ? a : b; }

	template <typename T>
	T max(T a, T b) { return a > b ? a : b; }

	template <typename T>
	T clamp(T in, T min, T max) {
		if (in < min) in = min;
		if (in > max) in = max;
		return in;
	}

	template <typename T>
	T clamp01(T in) {
		return clamp(in, T(0), T(1));
	}

	template <typename T>
	T sign(T v) {
		return v > T(0) ? T(1) : T(-1);
	}

	template <typename T>
	T abs(T v) {
		return v > T(0) ? v : -v;
	}

	template <typename T>
	T move_forward(T current, T target, T max_delta) {
		return abs(target - current) < max_delta
			       ? target
			       : current + sign(target - current) * max_delta;
	}
}

namespace sys {
	void cute_local_id_init(uint32_t machine);
	void cute_local_id_update(time_t);
	void cute_local_id_stop();
	id_64 cute_next_id();
}
