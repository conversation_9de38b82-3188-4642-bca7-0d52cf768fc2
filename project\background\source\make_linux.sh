#!/bin/bash

set -e

#svn update --username server_dev --password server_dev1019

echo "building"
INSTALL_PATH=../../../bin/
INCLUDE_PATH=${INSTALL_PATH}/include
MY_LIB=../../../libs
THIRD_LIB=../../../third_lib

m_debug=1
m_release=1

for arg in $*
do
	echo "${arg}"
	if [ $arg == '--debug' ];	then
		m_release=0
		echo 'cut release'
	fi

	if [ $arg == '--release' ]; then
		m_debug=0
		echo 'cut debug'
	fi
done

echo "building d:${m_debug} ,r: ${m_release}"

if [ -d ${INCLUDE_PATH} ] ;then
	rm -rf ${INCLUDE_PATH}
fi

# -----------------release----------------------
if [ ${m_release} == 1 ] ; then
	mkdir -p ../build-release && cd ../build-release
	cmake ../source ./ \
		-DCMAKE_BUILD_TYPE=Release\
		-DCMAKE_INSTALL_PREFIX=${INSTALL_PATH}/release\
		-DMY_LIB=${MY_LIB}\
		-DTHIRD_LIB=${THIRD_LIB}\
		-DMAKE_INSTALL=ON
	make -j4
	make install
fi


# -----------------debug----------------------
if [ ${m_debug} == 1 ] ; then
	mkdir -p ../build-debug && cd ../build-debug
	cmake ../source ./ \
		-DCMAKE_BUILD_TYPE=Debug\
		-DCMAKE_INSTALL_PREFIX=${INSTALL_PATH}/debug\
		-DMY_LIB=${MY_LIB}\
		-DTHIRD_LIB=${THIRD_LIB}\
		-DMAKE_INSTALL=ON
	make -j4
	make install
fi

cd ../source
