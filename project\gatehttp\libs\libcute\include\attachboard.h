#pragma once
#ifndef ATTACH_BOARD_H
#define ATTACH_BOARD_H 
#include <vector>
#include <str_tools.h>
#include <type_cast.h>

template <typename T>
struct attachboard {
	using base_type = T;
	using self_type = attachboard<T>;
	attachboard(const attachboard&) = delete;
	attachboard& operator =(const attachboard&) = delete;

	attachboard(attachboard&& rhs) noexcept {
		std::swap(rhs._attachment, _attachment);
	}

	attachboard& operator =(attachboard&& rhs) noexcept {
		self_type(static_cast<self_type&&>(rhs)).swap(*this);
		return *this;
	}

	void swap(attachboard& rhs) noexcept {
		std::swap(_attachment, rhs._attachment);
	}

	template <typename U, sys::is_true_type<U, T>* = nullptr>
	U& attach() {
		for (auto* v : _attachment) {
			U* u{};
			if ((u = sys::type_cast<U>(v))) {
				return *u;
			}
		}
		{
			U* u = new U();
			_attachment.emplace_back(u);
			return *u;
		}
	}

	template <typename U>
	U* tryget() const {
		for (auto* v : _attachment) {
			U* u{};
			if ((u = sys::type_cast<U>(v))) {
				return u;
			}
		}
		return nullptr;
	}

	void clear() {
		for (const auto* v : _attachment) {
			delete v;
		}
		_attachment.clear();
	}

	attachboard() = default;

	~attachboard() {
		for (const auto* v : _attachment) {
			delete v;
		}
		_attachment.clear();
	}

	using iterator = typename std::vector<T*>::iterator;
	using const_iterator = typename std::vector<T*>::const_iterator;

	iterator begin() {
		return _attachment.begin();
	}

	iterator end() {
		return _attachment.end();
	}

	const_iterator begin() const {
		return _attachment.begin();
	}

	const_iterator end() const {
		return _attachment.end();
	}

	size_t size() const {
		return _attachment.size();
	}

	bool empty() const {
		return _attachment.empty();
	}

private:
	std::vector<T*> _attachment;
};
#endif
