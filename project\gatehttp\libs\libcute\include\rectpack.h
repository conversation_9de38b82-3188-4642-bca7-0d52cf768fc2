﻿#pragma once
#ifndef RECT_PACKER_H
#define RECT_PACKER_H
#include <vector>

namespace sys {
	enum pack_status { None, Done };

	struct image_info {
		union {
			id_64 id{};
			void* data;
		};

		int status{};
		int x{};
		int y{};
		int width{};
		int height{};
	};

	class int_rect {
	public:
		DEFAULT_MOVE_COPY(int_rect)
		int x{};
		int y{};
		int width{};
		int height{};
		int_rect();
		int_rect(int x, int y, int w, int h);
		~int_rect();
		bool operator==(int_rect const& a) const;
		int xmax() const;
		int ymax() const;
		bool contains(const int_rect& other) const;
		bool overlaps(const int_rect& other) const;
		static const int_rect zero;
	};
}

namespace sys {
	class rectpack {
	public:
		DELETE_MOVE_COPY(rectpack)

		enum pack_method {
			Pack,
			XFisrt,
			YFirst
		};

		pack_method method{};
		rectpack(int w, int h, int p);
		void reset();
		void reset(int w, int h);
		void reset(int padding);
		void reset(int w, int h, int padding);
		bool pack(std::vector<image_info>&);
		int remove(int_rect& area);
		void packed_size(int& w, int& h) const;
		int count() const;
		~rectpack();

	private:
		int _width{};
		int _height{};
		int _padding{};
		int _packedw{};
		int _packedh{};
		int_rect _outbounds{};
		std::vector<int_rect> _insert_list{};
		std::vector<int_rect> _free_list{};
		void gen_freearea(const int_rect&, std::vector<int_rect>& result);
		int get_freeindex(int w, int h) const;
		bool merge_recrusive(int_rect& target);
	};
}
#endif
