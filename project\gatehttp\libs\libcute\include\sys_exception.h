#pragma once
#include <exception>
#include <string>
#include "libcute.h" 

namespace sys {
	class sys_exception final : public std::exception {
	public:
		DEFAULT_MOVE_COPY(sys_exception)
		explicit sys_exception(std::string);
		~sys_exception() noexcept override;
		const char* what() const noexcept override;
	private:
		std::string _what;
	};

	void throw_full(const char* file, size_t filelen,
	                const char* func, size_t funclen,
	                long line, const char* fmt, ...) NO_RETURN CHECK_PRINTF(6, 7);
}

#define throw_e(...)\
sys::throw_full(__FILE__, sizeof(__FILE__)-1, __func__, sizeof(__func__)-1, __LINE__, \
 __VA_ARGS__)

#define not_implement_method()throw_e("NOT IMPLEMENT METHOD") 
#define throw_obsolete()throw_e("Obsolete")



namespace sys {

	std::string traceback();
}