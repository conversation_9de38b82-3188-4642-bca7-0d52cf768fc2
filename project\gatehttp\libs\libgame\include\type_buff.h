#pragma once
#include <atomic>
#include <btree_list.h>
#include <libcute.h>
#include <type_data.h>
#include "typedata_wrapper_libgame.h"
#include "buff_tree.h"
#include "nav_struct.h"
#include "property_group.h"

namespace dvo {
	class usr_info;
	class info_base;
}

namespace ds {
	class buff_data;
}

namespace buff {
	class buff_tree;
}

namespace tvo {
	class type_buff final : public type_data {
	public:
		static void awake_all(const awake_context_t&);
		bool can_replace(const type_buff*) const;
		id_64 next_id();
		void make_cache(dvo::usr_info&, std::vector<ds::buff_data>& to) const;
		cpx::btree_list<buff::buff_tree> actions;
		void phase(int);
		bool has_phase(int) const;

	private:
		std::vector<int> _interest{};
		std::atomic<int> _rid{};

	public:
		enum buff_type_t {
			//todo: enums decl! @see TYPE_BUFF_WRAPPER() in typeclass_define.h
		};

		enum life_t {
			kLongLife = 1 << 0,
			kSceneLife = 1 << 1
		};

		enum overlap_mode_t {
			kOverlap,
			kTimeMaxDue,
			kTimeAdd,
		};


		TYPE_BUFF_WRAPPER()
	};
}
