#pragma once
#include "type_cast.h"
#include "xtick.h"
#include "libcute.h"
#include "service_entity.h"

namespace sys {
	struct route_stat;
	struct service_notification;
}

namespace sys {
	class service_aux {
	public :
		struct update_context {
			xtick now;
		};

		DELETE_MOVE_COPY(service_aux)
		TYPE_CAST_BASE_DEFINE(service_aux)

		template <typename T>
		T* get_aux() const {
			return _entity->get_aux<T>();
		}

		virtual void on_notification(service_notification&);
		virtual void stat(dynamic_vo&);
		virtual void stat(std::vector<route_stat>&);
		void entity(service_entity*);
		service_entity* entity() const;
		virtual void enter() = 0;
		virtual void exit() =0;
		virtual void update(const update_context* ctx);

	protected :
		service_aux();
		service_entity* _entity{};
	};

	template <typename T>
	using service_aux_impl = class_type_impl<T, service_aux>;
}
