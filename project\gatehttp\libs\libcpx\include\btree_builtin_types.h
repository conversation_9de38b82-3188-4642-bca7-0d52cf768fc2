//gen by cpp_autogen automatically, don't modify it!
#pragma once
#ifndef BTREE_BUILTIN_TYPES_H
#define BTREE_BUILTIN_TYPES_H

#define BTREE_BUILTIN_TYPES(XX)\
XX(1728580721,scene_composite_sequence,"scene:seq")\
XX(1384601484,scene_composite_sequence_selector,"scene:seq_sel")\
XX(1728577709,scene_composite_parallel,"scene:pal")\
XX(-1397043768,scene_composite_parallel_selector,"scene:pal_sel")\
XX(1734428780,scene_deco_repeat,"scene:deco_repeater")\
XX(-835514951,scene_deco_never_stop,"scene:deco_never_stop")\
XX(-969596610,scene_deco_until_success,"scene:deco_untilSucess")\
XX(787798832,scene_deco_until_failure,"scene:deco_untilFailure")\
XX(677905082,personality_composition_seq,"person:seq")\
XX(-925858192,personality_composition_pal,"pseron:pal")\
XX(2095345252,personality_composition_selector,"person:selector")\
XX(-729199358,firefx_composite_sequence,"fx:seqence")\
XX(-1293970513,firefx_composite_parallel,"fx:parallel")\
XX(-1273800313,firefx_composite_sequence_selector,"fx:selector")\
XX(1430572206,firefx_composite_parallel_selector,"fx:parallelSelector")\
XX(-1991844541,firefx_deco_repeat,"fx:repeat")\
XX(1584548221,firefx_deco_never_stop,"fx:never_stop")\
XX(1037917690,firefx_deco_until_success,"fx:untilSucess")\
XX(-1403767308,firefx_deco_until_failure,"fx:untilFailure")
 


#endif //BTREE_BUILTIN_TYPES_H
