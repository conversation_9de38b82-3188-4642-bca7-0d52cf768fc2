#pragma once
#include "pb_types.h"

namespace ecs {
	typedef pb::int64_t ptr_t;
	uint32_t ecs_field_size(unsigned type_id);
}

#define value_meta_size()offsetof(value_t, i64)

namespace ecs {
	enum encode_mode_t {
		PB_ENCODE_FULL = 0,
		PB_ENCODE_INCRE = 1
	};

	enum decode_mode_t {
		PB_DECODE_NONE = 0,
		PB_DECODE_PARSER = 1 << 0,
		PB_DECODE_DIRTY = 1 << 1
	};

	enum value_dirty_t {
		VAR_NONE = 0,
		VAR_CLI = 1 << 0,
		VAR_SQL = 1 << 1,
		VAR_ALL = VAR_CLI | VAR_SQL
	};

	struct encode_context {
		encode_mode_t mode{};
		value_dirty_t content{};
	};

	struct decode_context {
		decode_mode_t mode{};
		uint32_t ver{};
		value_dirty_t dirty{};
	};
}
