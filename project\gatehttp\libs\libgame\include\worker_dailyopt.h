#pragma once
#include <vector>
#include "usr_worker.h"

namespace dvo {
	class data_dailyopt;
}

namespace dailyopt {
	class worker_dailyopt final : public dvo::usr_worker {
	public:
		TYPE_CAST_IMPLEMENT(worker_dailyopt)
		void start() override;
		void stop() override;
		void update(const dvo::update_context*) override;
	private:
		void on_notifiction(const dvo::worker_notification*) override;
		void on_load() const;
		time_t _ticktime{};
	};
}
