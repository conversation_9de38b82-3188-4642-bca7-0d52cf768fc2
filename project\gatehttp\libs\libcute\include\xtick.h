#pragma once
#include <ctime>
#include <string>
#include "libcute.h"
#include "time_tools.h"

namespace sys {
	struct xtick {
		DEFAULT_MOVE_COPY(xtick)
		xtick();
		~xtick();
		xtick(dtime_t&&);
		xtick& operator =(dtime_t&&);
		xtick& operator =(const dtime_t&);
		operator dtime_t() const;
		xtick& operator =(const double&);
		operator double() const;
		operator time_t() const;
		/*%Y-%m-%d %T [%Z:%z] , format @see strftime*/
		std::string str(const char* fmt = nullptr) const;
		double time;
		uint64_t frame;
	};
}
