#pragma once
#include <blackboard.h>
#include <vector>
#include "rpx_core.h"
#include "rpx_parameter.h"

namespace cpx {
	class rpx_state;
	class cpx_state;
	class rpx_parameter;

	class rpx_controller final : public rpx_core {
	public:
		DELETE_MOVE_COPY(rpx_controller)
		rpx_controller();
		rpx_controller& root();
		~rpx_controller() override;
		void source(const cpx_controller*);
		const cpx_controller* source() const;
		void update(const tick_context*) override;
		void exit() override;
		rpx_machine* machine_at(size_t) const;
		rpx_state* runing_state(size_t) const;
		size_t layers() const;
		sys::blackboard& blackboard();
		rpx_parameter& parameter();
		void inited(bool);
		bool inited() const;

	private:
		void enter() override;
		bool _inited{};
		bool _updating{};
		const cpx_controller* _next{};
		const cpx_controller* _source{};
		rpx_parameter _params{};
		sys::blackboard _blackboard{};
		std::vector<rpx_machine*> _machines{};
		void change_next();
		void clear_current_machine();
	};
}
