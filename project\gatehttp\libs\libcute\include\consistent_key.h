#pragma once
#include "service_type.h"
#include "libcute.h"

namespace sys {
	struct consistent_key {
		consistent_key() = default;
		~consistent_key() = default;
		DEFAULT_MOVE_COPY(consistent_key)

		consistent_key(const service_type st, const size_t h): type(st), hash(h) {
		}

		template <typename TValue, std::enable_if_t<std::is_integral_v<TValue>> * = nullptr>
		consistent_key(const service_type st, const TValue h) : type(st),
		                                                        hash(static_cast<size_t>(h)) {
		}

		template <typename TValue, std::enable_if_t<!std::is_integral_v<TValue>> * = nullptr>
		consistent_key(const service_type st, const TValue& h): type(st),
		                                                        hash(std::hash<TValue>().operator()(h)) {
		}

		template <typename TService, typename TValue, std::enable_if_t<std::is_enum_v<TService>> * = nullptr>
		consistent_key(const TService st, const TValue h) {
			new(this)consistent_key(static_cast<service_type>(st), h);
		}

		template <typename TService, typename TValue, std::enable_if_t<std::is_integral_v<TService>> * = nullptr>
		consistent_key(const TService st, const TValue& h) {
			new(this)consistent_key(static_cast<service_type>(st), h);
		}

		template <typename TService>
		consistent_key(const TService st, const char* h) {
			new(this)consistent_key(st, std::string(h));
		}

		service_type type{};
		size_t hash{};
	};
}
