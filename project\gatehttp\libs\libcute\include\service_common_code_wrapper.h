/*auto gen, don't modify it*/
#pragma once 
#include "pb_types.h"
#include <vector>
#include <pb_manager.h>
#include <ecs_domain.h>
#include <ecs_class.h>
#include <ecs_object.h>
#include <ecs_value.h>
#include <pb_base.h> 
#include <logger.h>
/*---------------service_form--------------------*/
#define PB_WRAPPER_service_form()\
enum property_key{\
kFormId = 1, \
kClassId = 2, \
kData = 3, \
kSrcId = 4, \
kSrcType = 5, \
kDstId = 6, \
kDstType = 7, \
kChash = 8, \
kFormMethod = 9, \
\
};\
\
pb::int32_t form_id{}; \
pb::int32_t class_id{}; \
pb::bytes_t data{}; \
pb::int64_t src_id{}; \
pb::int32_t src_type{}; \
pb::int64_t dst_id{}; \
pb::int32_t dst_type{}; \
pb::int64_t chash{}; \
pb::int32_t form_method{}; \
\
int type()const override {return 359655438;}static constexpr int assign_type () {return 359655438;}\
\
int encode(pb_buffer* to) const override {if(form_id != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(form_id));}\
if(class_id != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(class_id));}\
if(!data.empty()) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(12)));const pb_slice s__ = pb_slice_buf(data.c_str(), data.size());pb_addbytes(to, s__);}\
if(src_id != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(3)));pb_addvarint64(to, src_id);}\
if(src_type != 0) {pb_addvarint32(to, pb_pair(5, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(src_type));}\
if(dst_id != 0) {pb_addvarint32(to, pb_pair(6, pb_wtypebytype(3)));pb_addvarint64(to, dst_id);}\
if(dst_type != 0) {pb_addvarint32(to, pb_pair(7, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(dst_type));}\
if(chash != 0) {pb_addvarint32(to, pb_pair(8, pb_wtypebytype(3)));pb_addvarint64(to, chash);}\
if(form_method != 0) {pb_addvarint32(to, pb_pair(9, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(form_method));}\
return 0 ;}\
\
int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__=pb_gettag(tag__);switch(fnumber__){\
case kFormId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}form_id = (static_cast<int>(o__.u64)); break;}\
case kClassId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}class_id = (static_cast<int>(o__.u64)); break;}\
case kData :{pbd_out o__;pbd_readbytes(s, o__.s);data = o__.s->base; break;}\
case kSrcId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}src_id = o__.u64; break;}\
case kSrcType :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}src_type = (static_cast<int>(o__.u64)); break;}\
case kDstId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}dst_id = o__.u64; break;}\
case kDstType :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}dst_type = (static_cast<int>(o__.u64)); break;}\
case kChash :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}chash = o__.u64; break;}\
case kFormMethod :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}form_method = (static_cast<int>(o__.u64)); break;}\
default: {pb_skipvalue(&s->base, tag__); break;}}\
}\
return 0 ;}\
\



/*---------------MessageData--------------------*/
#define PB_WRAPPER_MessageData()\
enum property_key{\
kCode = 1, \
kSheet = 2, \
kBytes = 3, \
\
};\
\
pb::int32_t code{}; \
pb::int32_t sheet{}; \
pb::bytes_t bytes{}; \
\
int type()const override {return 794275889;}static constexpr int assign_type () {return 794275889;}\
\
int encode(pb_buffer* to) const override {if(code != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(code));}\
if(sheet != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(sheet));}\
if(!bytes.empty()) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(12)));const pb_slice s__ = pb_slice_buf(bytes.c_str(), bytes.size());pb_addbytes(to, s__);}\
return 0 ;}\
\
int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__=pb_gettag(tag__);switch(fnumber__){\
case kCode :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}code = (static_cast<int>(o__.u64)); break;}\
case kSheet :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}sheet = (static_cast<int>(o__.u64)); break;}\
case kBytes :{pbd_out o__;pbd_readbytes(s, o__.s);bytes = o__.s->base; break;}\
default: {pb_skipvalue(&s->base, tag__); break;}}\
}\
return 0 ;}\
\



/*---------------SocketForm--------------------*/
#define PB_WRAPPER_SocketForm()\
enum property_key{\
kFormId = 1, \
kClassId = 2, \
kData = 3, \
kUsrId = 4, \
\
};\
\
pb::int32_t form_id{}; \
pb::int32_t class_id{}; \
pb::bytes_t data{}; \
pb::int64_t usr_id{}; \
\
int type()const override {return 2024311543;}static constexpr int assign_type () {return 2024311543;}\
\
int encode(pb_buffer* to) const override {if(form_id != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(form_id));}\
if(class_id != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(class_id));}\
if(!data.empty()) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(12)));const pb_slice s__ = pb_slice_buf(data.c_str(), data.size());pb_addbytes(to, s__);}\
if(usr_id != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(3)));pb_addvarint64(to, usr_id);}\
return 0 ;}\
\
int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__=pb_gettag(tag__);switch(fnumber__){\
case kFormId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}form_id = (static_cast<int>(o__.u64)); break;}\
case kClassId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}class_id = (static_cast<int>(o__.u64)); break;}\
case kData :{pbd_out o__;pbd_readbytes(s, o__.s);data = o__.s->base; break;}\
case kUsrId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}usr_id = o__.u64; break;}\
default: {pb_skipvalue(&s->base, tag__); break;}}\
}\
return 0 ;}\
\



/*---------------MessageCollection--------------------*/
#define PB_WRAPPER_MessageCollection()\
enum property_key{\
kDatas = 1, \
kUsrId = 2, \
kSceneId = 3, \
kRequestKey = 4, \
kTime = 5, \
\
};\
\
std::vector<MessageData> datas{}; \
pb::int64_t usrId{}; \
pb::int64_t sceneId{}; \
pb::int32_t requestKey{}; \
pb::double_t time{}; \
\
int type()const override {return 1530375653;}static constexpr int assign_type () {return 1530375653;}\
\
int encode(pb_buffer* to) const override {for(const auto & v__ : datas){pb_addvarint32(to, pb_pair(1, pb_wtypebytype(11)));{const size_t len__ = pb_bufflen(to);v__.encode(to);pbe_addlength(to, len__);}}\
if(usrId != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(3)));pb_addvarint64(to, usrId);}\
if(sceneId != 0) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(3)));pb_addvarint64(to, sceneId);}\
if(requestKey != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(requestKey));}\
if(std::abs(time)>0.0001) {pb_addvarint32(to, pb_pair(5, pb_wtypebytype(1)));pb_addfixed64(to, pb_encode_double(time));}\
return 0 ;}\
\
int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__=pb_gettag(tag__);switch(fnumber__){\
case kDatas :{MessageData v__;pbd_slice __sv{};pbd_readbytes(s, &__sv);v__.decode(&__sv);datas.emplace_back(v__); break;}\
case kUsrId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}usrId = o__.u64; break;}\
case kSceneId :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}sceneId = o__.u64; break;}\
case kRequestKey :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}requestKey = (static_cast<int>(o__.u64)); break;}\
case kTime :{pbd_out o__;if (pb_readfixed64(&s->base, &o__.u64) == 0){log_error("invalid fixed64 value at offset %d", pbd_offset(s));}*(uint64_t*)static_cast<void*>(&time)   = o__.u64; break;}\
default: {pb_skipvalue(&s->base, tag__); break;}}\
}\
return 0 ;}\
\



/*---------------room_id--------------------*/
#define PB_WRAPPER_room_id()\
enum property_key{\
kZone = 1, \
kPlace = 2, \
kMachine = 3, \
kRoom = 4, \
\
};\
\
pb::int32_t zone{}; \
pb::int32_t place{}; \
pb::int32_t machine{}; \
pb::int64_t room{}; \
\
int type()const override {return 1379892991;}static constexpr int assign_type () {return 1379892991;}\
\
int encode(pb_buffer* to) const override {if(zone != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(zone));}\
if(place != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(place));}\
if(machine != 0) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(5)));pb_addvarint64(to, pb_expandsig(machine));}\
if(room != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(3)));pb_addvarint64(to, room);}\
return 0 ;}\
\
int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__=pb_gettag(tag__);switch(fnumber__){\
case kZone :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}zone = (static_cast<int>(o__.u64)); break;}\
case kPlace :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}place = (static_cast<int>(o__.u64)); break;}\
case kMachine :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}machine = (static_cast<int>(o__.u64)); break;}\
case kRoom :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}room = o__.u64; break;}\
default: {pb_skipvalue(&s->base, tag__); break;}}\
}\
return 0 ;}\
\



/*---------------hash128--------------------*/
#define PB_WRAPPER_hash128()\
enum property_key{\
kM0 = 1, \
kM1 = 2, \
kM2 = 3, \
kM3 = 4, \
\
};\
\
pb::uint32_t m0{}; \
pb::uint32_t m1{}; \
pb::uint32_t m2{}; \
pb::uint32_t m3{}; \
\
int type()const override {return 697481833;}static constexpr int assign_type () {return 697481833;}\
\
int encode(pb_buffer* to) const override {if(m0 != 0) {pb_addvarint32(to, pb_pair(1, pb_wtypebytype(13)));pb_addvarint32(to, m0);}\
if(m1 != 0) {pb_addvarint32(to, pb_pair(2, pb_wtypebytype(13)));pb_addvarint32(to, m1);}\
if(m2 != 0) {pb_addvarint32(to, pb_pair(3, pb_wtypebytype(13)));pb_addvarint32(to, m2);}\
if(m3 != 0) {pb_addvarint32(to, pb_pair(4, pb_wtypebytype(13)));pb_addvarint32(to, m3);}\
return 0 ;}\
\
int decode(pbd_slice * s) override {uint32_t tag__; while (pb_readvarint32(&s->base, &tag__)){const int fnumber__=pb_gettag(tag__);switch(fnumber__){\
case kM0 :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}m0 = (static_cast<int>(o__.u64)); break;}\
case kM1 :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}m1 = (static_cast<int>(o__.u64)); break;}\
case kM2 :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}m2 = (static_cast<int>(o__.u64)); break;}\
case kM3 :{pbd_out o__;if (pb_readvarint64(&s->base, &o__.u64) == 0) {log_error("invalid varint value at offset %d", pbd_offset(s));}m3 = (static_cast<int>(o__.u64)); break;}\
default: {pb_skipvalue(&s->base, tag__); break;}}\
}\
return 0 ;}\
\



