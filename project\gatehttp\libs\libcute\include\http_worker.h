#pragma once
#include "http_session.h"
#include "web.h"

namespace net {
	class http_worker {
	public:
		using session_t = http_session::ptr_t;
		friend http_session;
		DELETE_MOVE_COPY(http_worker)
		int error() const;
		void start();
		void operator ()();

	protected:
		explicit http_worker(session_t);
		virtual void run() = 0;
		bool aborted() const;
		void write(const char* buf, size_t len);
		void write(const std::string&);
		void flush(const char* buf, size_t len, int fin) const;
		http_method method() const;
		http_phase phase() const;
		size_t read(pb_buffer* buf) const;
		http_session* session() const;
		head_response_t* response();
		void read_field(int fname, pb_slice& slice) const;
		http_content_type content_type() const;
		void decode_url_encoded(pb_stream&) const;
		virtual ~http_worker();

	private:
		session_t _pstream;
		http_session* _session;
		head_response_t _resp;
		int _version;
		// control---------------------  
		void flush() const;
		void finish();
		void write_head(size_t);
	};
}
