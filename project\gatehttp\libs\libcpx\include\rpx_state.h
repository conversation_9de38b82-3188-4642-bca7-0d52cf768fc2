#pragma once
#include "cpx_transition.h"
#include "rpx_core.h"
#include "time_vo.h"

namespace cpx {
	class cpx_transition;

	class rpx_state : public rpx_core {
	public:
		enum runtime_type {
			kAny,
			kAction,
			kMachine,
			kExit
		};

		DELETE_MOVE_COPY(rpx_state)
		rpx_state();
		~rpx_state() override;
		const cpx_state* source() const;
		void source(const cpx_state*);
		void destory();
		virtual int priority() const;
		std::string state_name() const;
		virtual runtime_type rtype() const = 0;
	protected:
		virtual bool update_transtion(const time_vo& tick, cpx_transition::check_mode_t);
		const cpx_transition* transition(size_t) const;
	private:
		const cpx_state* _source{};
	};
}
